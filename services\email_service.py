"""
增强版邮件服务 - 支持数据库配置和热更新
"""
import smtplib
import logging
import time
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Optional, Dict, Any
from threading import Lock
from datetime import datetime, timedelta
from flask import current_app, has_app_context
from models import db, EmailConfig

logger = logging.getLogger(__name__)


class EmailService:
    """增强版邮件服务类 - 支持数据库配置和热更新"""
    
    _instance = None
    _lock = Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(EmailService, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._config_cache = None
            self._cache_timestamp = 0
            self._cache_ttl = 300  # 缓存5分钟
            self._send_stats = {}  # 发送统计
            self._initialized = True

    def _get_config(self) -> Optional[EmailConfig]:
        """获取邮件配置（带缓存）"""
        current_time = time.time()
        
        # 检查缓存是否有效
        if (self._config_cache and 
            current_time - self._cache_timestamp < self._cache_ttl):
            return self._config_cache
        
        try:
            # 检查是否在Flask应用上下文中
            if not has_app_context():
                logger.warning("不在Flask应用上下文中，无法获取数据库配置")
                return self._get_fallback_config()
            
            # 从数据库获取单一配置（修复：使用与测试脚本一致的方法）
            config = EmailConfig.get_single_config()
            
            if config:
                self._config_cache = config
                self._cache_timestamp = current_time
                logger.debug(f"从数据库加载邮件配置: {config.name}")
                return config
            else:
                logger.warning("数据库中没有找到默认邮件配置，使用回退配置")
                return self._get_fallback_config()
                
        except Exception as e:
            logger.error(f"获取邮件配置失败: {e}")
            return self._get_fallback_config()

    def _get_fallback_config(self) -> Optional[EmailConfig]:
        """获取回退配置（从环境变量或config.py）"""
        try:
            import os
            from config import Config
            
            # 创建临时配置对象
            fallback_config = EmailConfig()
            fallback_config.smtp_server = getattr(Config, 'MAIL_SERVER', os.environ.get('MAIL_SERVER', 'localhost'))
            fallback_config.smtp_port = getattr(Config, 'MAIL_PORT', int(os.environ.get('MAIL_PORT', '587')))
            fallback_config.use_tls = getattr(Config, 'MAIL_USE_TLS', os.environ.get('MAIL_USE_TLS', 'true').lower() == 'true')
            fallback_config.use_ssl = getattr(Config, 'MAIL_USE_SSL', os.environ.get('MAIL_USE_SSL', 'false').lower() == 'true')
            fallback_config.username = getattr(Config, 'MAIL_USERNAME', os.environ.get('MAIL_USERNAME', ''))
            fallback_config.password = getattr(Config, 'MAIL_PASSWORD', os.environ.get('MAIL_PASSWORD', ''))
            fallback_config.default_sender = getattr(Config, 'MAIL_DEFAULT_SENDER', os.environ.get('MAIL_DEFAULT_SENDER', fallback_config.username))
            fallback_config.sender_name = getattr(Config, 'MAIL_SENDER_NAME', os.environ.get('MAIL_SENDER_NAME', '系统通知'))
            fallback_config.name = '回退配置'
            
            logger.info("使用回退邮件配置")
            return fallback_config
            
        except Exception as e:
            logger.error(f"获取回退配置失败: {e}")
            return None

    def refresh_config(self):
        """手动刷新配置缓存"""
        self._config_cache = None
        self._cache_timestamp = 0
        logger.info("邮件配置缓存已刷新")

    def send_email(self, to_email: str, subject: str, body: str, 
                   is_html: bool = False, attachments: List = None) -> bool:
        """
        发送邮件
        
        Args:
            to_email: 收件人邮箱
            subject: 邮件主题
            body: 邮件内容
            is_html: 是否为HTML格式
            attachments: 附件列表
            
        Returns:
            bool: 发送是否成功
        """
        config = self._get_config()
        if not config:
            logger.error("无法获取邮件配置")
            return False
        
        try:
            # 检查发送限制
            if not self._check_rate_limit(config):
                logger.warning(f"邮件发送频率超限: {to_email}")
                return False
            
            # 确定发送方地址 - 统一使用认证用户名确保一致性
            sender_email = config.username  # 使用认证用户名作为发送方
            sender_display = f"{config.sender_name} <{sender_email}>" if config.sender_name else sender_email

            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = sender_display
            msg['To'] = to_email
            msg['Subject'] = subject

            # 添加邮件内容
            if is_html:
                msg.attach(MIMEText(body, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # 添加附件
            if attachments:
                for attachment in attachments:
                    msg.attach(attachment)

            # 连接SMTP服务器并发送
            server = smtplib.SMTP(config.smtp_server, config.smtp_port)

            if config.use_tls:
                server.starttls()
            elif config.use_ssl:
                server = smtplib.SMTP_SSL(config.smtp_server, config.smtp_port)

            if config.username and config.password:
                server.login(config.username, config.password)

            text = msg.as_string()
            # 使用认证用户名作为发送方，确保与登录凭据一致
            server.sendmail(sender_email, to_email, text)
            server.quit()

            logger.info(f"邮件发送成功: {to_email}, 发送方: {sender_email}")
            
            # 更新统计信息
            self._update_send_stats(config, True)
            if has_app_context():
                try:
                    # 重新获取config对象确保Session绑定
                    fresh_config = EmailConfig.query.get(config.id)
                    if fresh_config:
                        fresh_config.update_stats(True)
                        db.session.commit()
                except Exception as stats_error:
                    logger.warning(f"更新邮件统计信息失败: {stats_error}")
                    # 统计更新失败不应该影响邮件发送成功的结果
                    pass
            
            logger.info(f"邮件发送成功: {to_email}")
            return True
            
        except Exception as e:
            error_msg = str(e)
            
            # 详细的错误诊断
            diagnostic_info = self._diagnose_smtp_error(e, config)
            logger.error(f"邮件发送失败 {to_email}: {error_msg}")
            logger.info(f"错误诊断: {diagnostic_info}")

            # 更新统计信息
            if config:
                self._update_send_stats(config, False)
                if has_app_context():
                    try:
                        # 重新获取config对象确保Session绑定
                        fresh_config = EmailConfig.query.get(config.id)
                        if fresh_config:
                            fresh_config.update_stats(False)
                            db.session.commit()
                    except Exception as stats_error:
                        logger.warning(f"更新邮件统计信息失败: {stats_error}")
                        # 统计更新失败不应该影响邮件发送的错误处理
                        pass

            return False

    def _check_rate_limit(self, config: EmailConfig) -> bool:
        """检查发送频率限制"""
        current_time = time.time()
        minute_key = int(current_time // 60)
        
        # 初始化统计
        if minute_key not in self._send_stats:
            self._send_stats[minute_key] = 0
        
        # 清理旧统计数据
        old_keys = [k for k in self._send_stats.keys() if k < minute_key - 5]
        for key in old_keys:
            del self._send_stats[key]
        
        # 检查当前分钟的发送量
        if self._send_stats[minute_key] >= config.rate_limit_per_minute:
            return False
        
        return True

    def _update_send_stats(self, config: EmailConfig, success: bool):
        """更新发送统计"""
        current_time = time.time()
        minute_key = int(current_time // 60)
        
        if minute_key not in self._send_stats:
            self._send_stats[minute_key] = 0
        
        if success:
            self._send_stats[minute_key] += 1

    def _diagnose_smtp_error(self, error: Exception, config: EmailConfig) -> str:
        """诊断SMTP错误并提供解决建议"""
        error_str = str(error).lower()
        
        # 检查是否为QQ邮箱
        is_qq_email = 'qq.com' in config.smtp_server.lower() or '@qq.com' in config.username.lower()
        
        # 常见SMTP错误诊断
        if '550' in error_str and 'unauthenticated' in error_str:
            if is_qq_email:
                return (
                    "QQ邮箱认证失败 - 解决方案: "
                    "1) 确保已开启IMAP/SMTP服务 (在QQ邮箱设置→账户中开启) "
                    "2) 使用授权码而非QQ登录密码 "
                    "3) 授权码获取: QQ邮箱设置→账户→开启服务时获得16位授权码 "
                    "4) 推荐配置: smtp.qq.com, 端口587, TLS加密 "
                    "5) 确保发送方地址与认证用户名一致"
                )
            else:
                return (
                    "SMTP认证失败 - 可能原因: "
                    "1) 用户名或密码错误 "
                    "2) 需要启用应用专用密码 (Gmail/Outlook) "
                    "3) 邮件服务器不允许从当前IP发送邮件 "
                    "4) 需要启用'允许不够安全的应用'设置 "
                    "5) 发送方地址与认证用户名不匹配 - 已修复为使用认证用户名"
                )
        elif '535' in error_str:
            if is_qq_email:
                return "QQ邮箱认证失败 - 请确认使用的是授权码而非QQ密码，并检查用户名是否为完整邮箱地址"
            else:
                return "认证失败 - 请检查用户名和密码是否正确"
        elif '587' in error_str or 'connection' in error_str:
            return f"连接失败 - 请检查SMTP服务器地址({config.smtp_server})和端口({config.smtp_port})是否正确"
        elif 'ssl' in error_str or 'tls' in error_str:
            return f"加密连接失败 - 当前设置: TLS={config.use_tls}, SSL={config.use_ssl}, 请尝试调整加密方式"
        elif 'timeout' in error_str:
            return "连接超时 - 请检查网络连接和防火墙设置"
        elif 'refused' in error_str:
            return "连接被拒绝 - 请检查SMTP服务器是否允许外部连接"
        else:
            if is_qq_email:
                return f"QQ邮箱配置错误 - 推荐配置: smtp.qq.com, 端口587, TLS加密, 使用授权码作为密码"
            else:
                return f"未知错误 - 请检查邮件配置: 服务器={config.smtp_server}, 端口={config.smtp_port}, 用户={config.username}"

    def test_connection(self, config_id: Optional[int] = None) -> Dict[str, Any]:
        """
        测试邮件连接
        
        Args:
            config_id: 配置ID，如果为None则使用默认配置
            
        Returns:
            Dict: 测试结果
        """
        try:
            if config_id:
                config = EmailConfig.query.get(config_id)
            else:
                config = self._get_config()
            
            if not config:
                return {'success': False, 'message': '无法获取邮件配置'}
            
            # 测试SMTP连接
            server = smtplib.SMTP(config.smtp_server, config.smtp_port)
            
            if config.use_tls:
                server.starttls()
            elif config.use_ssl:
                server = smtplib.SMTP_SSL(config.smtp_server, config.smtp_port)
            
            if config.username and config.password:
                server.login(config.username, config.password)
            
            server.quit()
            
            return {
                'success': True, 
                'message': '邮件服务器连接成功',
                'config_name': config.name
            }
            
        except Exception as e:
            diagnostic_info = self._diagnose_smtp_error(e, config)
            return {
                'success': False, 
                'message': f'连接失败: {str(e)}',
                'diagnostic': diagnostic_info
            }


    def send_verification_code_email(self, to_email: str, code: str, code_type: str) -> bool:
        """发送验证码邮件"""
        config = self._get_config()
        if not config:
            logger.error(f"邮件配置不存在，无法发送验证码邮件: {to_email}")
            return False

        type_names = {
            'register': '注册验证',
            'login': '登录验证',
            'reset_password': '密码重置',
            'email_change': '邮箱变更'
        }

        type_name = type_names.get(code_type, '验证')
        subject = f"您的{type_name}验证码"

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{subject}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">验证码通知</h1>
                </div>

                <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
                    <h2 style="color: #495057;">您的{type_name}验证码</h2>
                    <p>您好，</p>
                    <p>您正在进行{type_name}操作，验证码为：</p>

                    <div style="background: #fff; border: 2px dashed #007bff; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px;">
                        <span style="font-size: 32px; font-weight: bold; color: #007bff; letter-spacing: 5px;">{code}</span>
                    </div>

                    <p style="color: #6c757d;">
                        <strong>注意事项：</strong><br>
                        • 验证码有效期为 10 分钟<br>
                        • 请勿将验证码告诉他人<br>
                        • 如非本人操作，请忽略此邮件
                    </p>
                </div>

                <div style="text-align: center; padding: 20px; color: #6c757d; font-size: 14px;">
                    <p>此邮件由系统自动发送，请勿回复</p>
                    <p>如有疑问，请联系客服</p>
                </div>
            </div>
        </body>
        </html>
        """

        return self.send_email(to_email, subject, html_body, is_html=True)

    def send_subscription_expiry_email(self, to_email: str, customer_name: str,
                                     subscription_info: Dict) -> bool:
        """发送订阅到期通知邮件"""
        subject = "订阅到期通知"

        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{subject}</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                    <h1 style="margin: 0; font-size: 28px;">订阅到期通知</h1>
                </div>

                <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
                    <h2 style="color: #495057;">尊敬的 {customer_name or '用户'}，</h2>
                    <p>您的订阅即将到期，请及时续费以免影响使用。</p>

                    <div style="background: #fff; border-left: 4px solid #ff6b6b; padding: 20px; margin: 20px 0;">
                        <h3 style="margin-top: 0; color: #ff6b6b;">订阅信息</h3>
                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>订阅ID：</strong></td>
                                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{subscription_info.get('id', 'N/A')}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>到期时间：</strong></td>
                                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{subscription_info.get('expires_at', 'N/A')}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>流量限制：</strong></td>
                                <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{subscription_info.get('traffic_limit_gb', 'N/A')} GB</td>
                            </tr>
                        </table>
                    </div>

                    <p style="color: #6c757d;">
                        <strong>温馨提示：</strong><br>
                        • 订阅到期后服务将自动停止<br>
                        • 请及时续费以保证服务连续性<br>
                        • 如有疑问，请联系客服
                    </p>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="#" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">立即续费</a>
                    </div>
                </div>

                <div style="text-align: center; padding: 20px; color: #6c757d; font-size: 14px;">
                    <p>此邮件由系统自动发送，请勿回复</p>
                    <p>如有疑问，请联系客服</p>
                </div>
            </div>
        </body>
        </html>
        """

        return self.send_email(to_email, subject, html_body, is_html=True)


# 创建全局实例
email_service = EmailService()
