{% extends "base.html" %}

{% block title %}订单管理 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-list-ul"></i> 订单管理
                </h1>
                <div>
                    <button class="btn btn-outline-secondary" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">订单状态</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">全部状态</option>
                                <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>待支付</option>
                                <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>已完成</option>
                                <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>已取消</option>
                                <option value="failed" {% if current_status == 'failed' %}selected{% endif %}>失败</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="customer_email" class="form-label">客户邮箱</label>
                            <input type="email" class="form-control" id="customer_email" name="customer_email" 
                                   value="{{ request.args.get('customer_email', '') }}" placeholder="输入客户邮箱">
                        </div>
                        <div class="col-md-3">
                            <label for="order_id" class="form-label">订单号</label>
                            <input type="text" class="form-control" id="order_id" name="order_id" 
                                   value="{{ request.args.get('order_id', '') }}" placeholder="输入订单号">
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary me-2">
                                <i class="bi bi-search"></i> 筛选
                            </button>
                            <a href="{{ url_for('admin.orders') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> 清除
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">订单列表</h5>
                </div>
                <div class="card-body p-0">
                    {% if orders.items %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 px-4 py-3">订单号</th>
                                    <th class="border-0 px-4 py-3">客户信息</th>
                                    <th class="border-0 px-4 py-3">产品信息</th>
                                    <th class="border-0 px-4 py-3">优惠码</th>
                                    <th class="border-0 px-4 py-3">优惠金额</th>
                                    <th class="border-0 px-4 py-3">订单金额</th>
                                    <th class="border-0 px-4 py-3">状态</th>
                                    <th class="border-0 px-4 py-3">创建时间</th>
                                    <th class="border-0 px-4 py-3">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders.items %}
                                <tr>
                                    <td class="px-4 py-3">
                                        <code class="text-primary">{{ order.order_id }}</code>
                                        {% if order.payment_id %}
                                        <br><small class="text-muted">支付ID: {{ order.payment_id }}</small>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3">
                                        <div>
                                            <strong>{{ order.customer_email }}</strong>
                                            {% if order.customer_name %}
                                            <br><small class="text-muted">{{ order.customer_name }}</small>
                                            {% endif %}
                                            {% if order.user %}
                                            <br><small class="text-info">用户ID: {{ order.user.id }}</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div>
                                            {% if order.product %}
                                            <strong>{{ order.product.name }}</strong>
                                            {% else %}
                                            <span class="text-muted">自定义订单</span>
                                            {% endif %}
                                            <br>
                                            <small class="text-muted">
                                                {{ order.node_type.value if order.node_type else 'N/A' }} | 
                                                {{ order.duration_days }}天 | 
                                                {{ order.traffic_limit_gb }}GB
                                            </small>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        {{ order.applied_coupon_code if order.applied_coupon_code else '-' }}
                                    </td>
                                    <td class="px-4 py-3">
                                        {{ "%.2f"|format(order.discount_amount) if order.discount_amount and order.discount_amount > 0 else '-' }}
                                    </td>
                                    <td class="px-4 py-3">
                                        <strong class="text-success">¥{{ "%.2f"|format(order.price) }}</strong>
                                        {% if order.payment_method %}
                                        <br><small class="text-muted">{{ order.payment_method }}</small>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3">
                                        {% if order.status.value == 'pending' %}
                                        <span class="badge bg-warning">待支付</span>
                                        {% elif order.status.value == 'completed' %}
                                        <span class="badge bg-success">已完成</span>
                                        {% elif order.status.value == 'cancelled' %}
                                        <span class="badge bg-secondary">已取消</span>
                                        {% elif order.status.value == 'failed' %}
                                        <span class="badge bg-danger">失败</span>
                                        {% else %}
                                        <span class="badge bg-light text-dark">{{ order.status.value }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3">
                                        <small class="text-muted">{{ order.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                        {% if order.expires_at %}
                                        <br><small class="text-warning">到期: {{ order.expires_at.strftime('%Y-%m-%d') }}</small>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="btn-group-vertical btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary btn-sm" onclick="viewOrderDetail({{ order.id }})">
                                                <i class="bi bi-eye"></i> 详情
                                            </button>
                                            {% if order.status.value == 'pending' %}
                                            <button class="btn btn-outline-success btn-sm" onclick="markOrderCompleted({{ order.id }})">
                                                <i class="bi bi-check-circle"></i> 完成
                                            </button>
                                            <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder({{ order.id }})">
                                                <i class="bi bi-x-circle"></i> 取消
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h5 class="mt-3 text-muted">暂无订单数据</h5>
                        <p class="text-muted">当前筛选条件下没有找到订单</p>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 分页 -->
                {% if orders.pages > 1 %}
                <div class="card-footer">
                    <nav aria-label="订单分页">
                        <ul class="pagination justify-content-center mb-0">
                            {% if orders.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.orders', page=orders.prev_num, status=current_status) }}">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in orders.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != orders.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.orders', page=page_num, status=current_status) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">…</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if orders.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.orders', page=orders.next_num, status=current_status) }}">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 订单详情模态框 -->
<div class="modal fade" id="orderDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">订单详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function viewOrderDetail(orderId) {
    const modal = new bootstrap.Modal(document.getElementById('orderDetailModal'));
    const content = document.getElementById('orderDetailContent');
    
    // 显示加载状态
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // 这里可以添加AJAX请求获取订单详情
    // 暂时显示基本信息
    setTimeout(() => {
        content.innerHTML = `
            <div class="alert alert-info">
                <i class="bi bi-info-circle"></i>
                订单详情功能正在开发中，订单ID: ${orderId}
            </div>
        `;
    }, 500);
}

function markOrderCompleted(orderId) {
    if (confirm('确定要将此订单标记为已完成吗？')) {
        // 这里添加AJAX请求标记订单完成
        alert('订单完成功能正在开发中');
    }
}

function cancelOrder(orderId) {
    if (confirm('确定要取消此订单吗？此操作不可撤销。')) {
        // 这里添加AJAX请求取消订单
        alert('订单取消功能正在开发中');
    }
}
</script>
{% endblock %}
