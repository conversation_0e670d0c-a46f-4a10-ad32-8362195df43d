"""
数据库模型枚举定义
"""
from enum import Enum


class OrderStatus(Enum):
    """订单状态枚举"""
    PENDING = "pending"      # 待处理
    PROCESSING = "processing"  # 处理中
    COMPLETED = "completed"   # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"   # 已取消


class NodeType(Enum):
    """节点类型枚举"""
    VLESS = "vless"
    VMESS = "vmess"
    TROJAN = "trojan"


class PanelStatus(Enum):
    """面板状态枚举"""
    ACTIVE = "active"        # 活跃
    INACTIVE = "inactive"    # 停用
    MAINTENANCE = "maintenance"  # 维护中


class UserRole(Enum):
    """用户角色枚举"""
    ADMIN = "admin"          # 管理员
    USER = "user"            # 普通用户


class ProductType(Enum):
    """产品类型枚举"""
    MONTHLY = "monthly"      # 月付
    QUARTERLY = "quarterly"  # 季付
    YEARLY = "yearly"        # 年付
    CUSTOM = "custom"        # 自定义


class GroupRole(Enum):
    """分组中的角色枚举"""
    PRIMARY = "primary"      # 主要节点
    BACKUP = "backup"        # 备用节点
    FALLBACK = "fallback"    # 故障转移节点
