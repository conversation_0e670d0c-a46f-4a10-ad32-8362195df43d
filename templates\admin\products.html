{% extends "base.html" %}

{% block title %}产品管理 - 管理后台{% endblock %}



{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-box"></i> 产品管理
                </h1>
                <div>
                    <a href="{{ url_for('admin.create_product') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 创建产品
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 产品列表 -->
    <div class="row">
        {% for product in products %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ product.name }}</h6>
                    <div>
                        <span class="badge bg-{{ 'success' if product.is_active else 'secondary' }} me-1">
                            {{ '启用' if product.is_active else '停用' }}
                        </span>
                        <span class="badge bg-info">
                            {% if product.product_type.value == 'monthly' %}月付
                            {% elif product.product_type.value == 'quarterly' %}季付
                            {% elif product.product_type.value == 'yearly' %}年付
                            {% else %}自定义{% endif %}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    {% if product.description %}
                    <p class="card-text text-muted small">{{ product.description }}</p>
                    {% endif %}
                    
                    <div class="row text-center mb-3">
                        <div class="col-4">
                            <small class="text-muted">有效期</small>
                            <div class="fw-bold">{{ product.duration_days }}天</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">流量</small>
                            <div class="fw-bold">{{ product.traffic_limit_gb }}GB</div>
                        </div>
                        <div class="col-4">
                            <small class="text-muted">价格</small>
                            <div class="fw-bold text-primary">¥{{ "%.2f"|format(product.price) }}</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">节点类型</small>
                            <div>
                                <span class="badge bg-secondary">{{ product.node_type.value.upper() }}</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">目标分组</small>
                            <div>
                                {% if product.target_group %}
                                <span class="badge" style="background-color: {{ product.target_group.color }}; color: white;">
                                    {{ product.target_group.name }}
                                </span>
                                {% else %}
                                <span class="badge bg-light text-dark">自动分配</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">库存</small>
                            <div class="fw-bold">
                                {% if product.stock_count == -1 %}
                                    <span class="text-success">无限</span>
                                {% else %}
                                    {{ product.stock_count }}
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">已售</small>
                            <div class="fw-bold">{{ product.sold_count }}</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-outline-primary btn-sm" onclick="editProduct({{ product.id }})">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="viewOrders({{ product.id }})">
                            <i class="bi bi-list-ul"></i> 订单
                        </button>
                        <form method="POST" action="#" style="display: inline;" 
                              onsubmit="return confirm('确定要删除产品 {{ product.name }} 吗？')">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        
        {% if not products %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-box text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无产品</h4>
                    <p class="text-muted">还没有创建任何产品</p>
                    <a href="{{ url_for('admin.create_product') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 创建第一个产品
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回仪表板
            </a>
        </div>
    </div>
</div>

<!-- 编辑产品模态框 -->
<div class="modal fade" id="editProductModal" tabindex="-1" aria-labelledby="editProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editProductModalLabel">
                    <i class="bi bi-pencil"></i> 编辑产品
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editProductForm">
                <div class="modal-body">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_name" class="form-label">产品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_product_type" class="form-label">产品类型 <span class="text-danger">*</span></label>
                                <select class="form-select" id="edit_product_type" name="product_type" required>
                                    <option value="">请选择产品类型</option>
                                    <option value="monthly">月付套餐</option>
                                    <option value="quarterly">季付套餐</option>
                                    <option value="yearly">年付套餐</option>
                                    <option value="custom">自定义套餐</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_description" class="form-label">产品描述</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"
                                  placeholder="详细描述产品特性和优势"></textarea>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_duration_days" class="form-label">有效期（天） <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_duration_days" name="duration_days"
                                       min="1" required placeholder="例如：30">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_traffic_limit_gb" class="form-label">流量限制（GB） <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_traffic_limit_gb" name="traffic_limit_gb"
                                       min="1" required placeholder="例如：100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="edit_price" class="form-label">价格（元） <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="edit_price" name="price"
                                       min="0" step="0.01" required placeholder="例如：29.99">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_node_type" class="form-label">节点类型</label>
                                <select class="form-select" id="edit_node_type" name="node_type">
                                    <option value="vless">VLESS</option>
                                    <option value="vmess">VMess</option>
                                    <option value="trojan">Trojan</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_target_group_id" class="form-label">目标分组</label>
                                <select class="form-select" id="edit_target_group_id" name="target_group_id">
                                    <option value="">请选择分组</option>
                                    <!-- 分组选项将通过JavaScript动态加载 -->
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_stock_count" class="form-label">库存数量</label>
                                <input type="number" class="form-control" id="edit_stock_count" name="stock_count"
                                       min="-1" value="-1" placeholder="-1表示无限库存">
                                <div class="form-text">-1 表示无限库存</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active" checked>
                                    <label class="form-check-label" for="edit_is_active">
                                        启用产品
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="edit_group_specific_config" class="form-label">分组特定配置</label>
                        <textarea class="form-control" id="edit_group_specific_config" name="group_specific_config"
                                  rows="3" placeholder="JSON格式的分组特定配置（可选）"></textarea>
                        <div class="form-text">可选的JSON格式配置，用于特定分组的自定义设置</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> 保存更改
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>



<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.badge {
    font-size: 0.75em;
}

.card-text {
    max-height: 3em;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>

{% endblock %}

{% block scripts %}
<script>
// 产品编辑功能的JavaScript代码
let currentEditingProductId = null;

function editProduct(productId) {
    currentEditingProductId = productId;

    // 显示加载状态
    const modal = new bootstrap.Modal(document.getElementById('editProductModal'));
    modal.show();

    // 禁用表单
    const form = document.getElementById('editProductForm');
    const inputs = form.querySelectorAll('input, select, textarea, button');
    inputs.forEach(input => input.disabled = true);

    // 加载产品数据
    fetch(`/admin/products/${productId}/edit?format=json`, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 填充表单数据
            populateEditForm(data.product, data.groups);

            // 启用表单
            inputs.forEach(input => input.disabled = false);
        } else {
            alert('加载产品数据失败: ' + (data.message || '未知错误'));
            modal.hide();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('加载产品数据失败: ' + error.message);
        modal.hide();
    });
}

function populateEditForm(product, groups) {
    // 填充基本信息
    document.getElementById('edit_name').value = product.name || '';
    document.getElementById('edit_description').value = product.description || '';
    document.getElementById('edit_product_type').value = product.product_type || '';
    document.getElementById('edit_duration_days').value = product.duration_days || '';
    document.getElementById('edit_traffic_limit_gb').value = product.traffic_limit_gb || '';
    document.getElementById('edit_price').value = product.price || '';
    document.getElementById('edit_node_type').value = product.node_type || 'vless';
    document.getElementById('edit_stock_count').value = product.stock_count || -1;
    document.getElementById('edit_is_active').checked = product.is_active || false;
    document.getElementById('edit_group_specific_config').value = product.group_specific_config || '';

    // 填充分组选项
    const groupSelect = document.getElementById('edit_target_group_id');
    groupSelect.innerHTML = '<option value="">请选择分组</option>';

    groups.forEach(group => {
        const option = document.createElement('option');
        option.value = group.id;
        option.textContent = group.name;
        if (product.target_group_id === group.id) {
            option.selected = true;
        }
        groupSelect.appendChild(option);
    });
}

function viewOrders(productId) {
    // 跳转到订单管理页面，筛选该产品的订单
    window.location.href = '{{ url_for("admin.orders") }}?product_id=' + productId;
}

// 页面加载完成后注册事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 处理表单提交
    const form = document.getElementById('editProductForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!currentEditingProductId) {
                alert('无效的产品ID');
                return;
            }

            // 获取表单数据
            const formData = new FormData(this);

            // 手动处理复选框值和空值
            const data = Object.fromEntries(formData);
            data.is_active = document.getElementById('edit_is_active').checked;

            // 处理空值，确保数字字段不为空字符串
            if (!data.target_group_id || data.target_group_id === '') {
                data.target_group_id = null;
            }
            if (!data.group_specific_config || data.group_specific_config.trim() === '') {
                data.group_specific_config = null;
            }

            console.log('提交的数据:', data);

            // 显示提交状态
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
            submitBtn.disabled = true;

            // 提交数据
            fetch(`/admin/products/${currentEditingProductId}/edit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('产品更新成功！');
                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editProductModal'));
                    modal.hide();
                    // 刷新页面
                    window.location.reload();
                } else {
                    alert('更新失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('更新失败: ' + error.message);
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }
});
</script>
{% endblock %}
