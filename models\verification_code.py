"""
验证码数据模型
"""
from datetime import datetime, timedelta
from . import db
from .enums import *
import random
import string


class VerificationCodeType:
    """验证码类型枚举"""
    REGISTER = 'register'
    LOGIN = 'login'
    RESET_PASSWORD = 'reset_password'
    EMAIL_CHANGE = 'email_change'


class VerificationCode(db.Model):
    """验证码模型"""
    __tablename__ = 'verification_codes'

    id = db.Column(db.Integer, primary_key=True)
    
    # 验证码信息
    code = db.Column(db.String(10), nullable=False)  # 验证码
    email = db.Column(db.String(255), nullable=False, index=True)  # 邮箱地址
    code_type = db.Column(db.String(50), nullable=False)  # 验证码类型
    
    # 状态信息
    is_used = db.Column(db.Boolean, nullable=False, default=False)  # 是否已使用
    is_expired = db.Column(db.<PERSON><PERSON><PERSON>, nullable=False, default=False)  # 是否已过期
    
    # 时间信息
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)  # 过期时间
    used_at = db.Column(db.DateTime, nullable=True)  # 使用时间
    
    # 安全信息
    ip_address = db.Column(db.String(45), nullable=True)  # 请求IP地址
    user_agent = db.Column(db.Text, nullable=True)  # 用户代理
    attempts = db.Column(db.Integer, nullable=False, default=0)  # 验证尝试次数
    max_attempts = db.Column(db.Integer, nullable=False, default=5)  # 最大尝试次数

    def __init__(self, email, code_type, valid_minutes=10, **kwargs):
        """
        初始化验证码
        
        Args:
            email: 邮箱地址
            code_type: 验证码类型
            valid_minutes: 有效期（分钟）
        """
        super(VerificationCode, self).__init__(**kwargs)
        self.email = email.lower().strip()
        self.code_type = code_type
        self.code = self._generate_code()
        self.expires_at = datetime.utcnow() + timedelta(minutes=valid_minutes)

    def _generate_code(self):
        """生成验证码"""
        # 生成6位数字验证码
        return ''.join(random.choices(string.digits, k=6))

    @property
    def is_valid(self):
        """检查验证码是否有效"""
        now = datetime.utcnow()
        return (
            not self.is_used and 
            not self.is_expired and 
            now < self.expires_at and
            self.attempts < self.max_attempts
        )

    def verify(self, input_code, ip_address=None):
        """
        验证验证码
        
        Args:
            input_code: 输入的验证码
            ip_address: 请求IP地址
            
        Returns:
            tuple: (是否验证成功, 错误信息)
        """
        self.attempts += 1
        
        # 检查是否已过期
        if datetime.utcnow() > self.expires_at:
            self.is_expired = True
            return False, '验证码已过期'
        
        # 检查是否已使用
        if self.is_used:
            return False, '验证码已使用'
        
        # 检查尝试次数
        if self.attempts > self.max_attempts:
            self.is_expired = True
            return False, '验证码尝试次数过多，已失效'
        
        # 验证码码
        if input_code != self.code:
            return False, f'验证码错误，还可尝试 {self.max_attempts - self.attempts} 次'
        
        # 验证成功
        self.is_used = True
        self.used_at = datetime.utcnow()
        if ip_address:
            self.ip_address = ip_address
        
        return True, '验证成功'

    def mark_expired(self):
        """标记为已过期"""
        self.is_expired = True

    @classmethod
    def cleanup_expired(cls, days_old=7):
        """
        清理过期的验证码
        
        Args:
            days_old: 清理多少天前的记录
            
        Returns:
            int: 清理的记录数
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        expired_codes = cls.query.filter(
            db.or_(
                cls.expires_at < datetime.utcnow(),
                cls.created_at < cutoff_date
            )
        ).all()
        
        count = len(expired_codes)
        for code in expired_codes:
            db.session.delete(code)
        
        return count

    @classmethod
    def get_valid_code(cls, email, code_type):
        """
        获取有效的验证码
        
        Args:
            email: 邮箱地址
            code_type: 验证码类型
            
        Returns:
            VerificationCode: 有效的验证码或None
        """
        return cls.query.filter_by(
            email=email.lower().strip(),
            code_type=code_type,
            is_used=False,
            is_expired=False
        ).filter(
            cls.expires_at > datetime.utcnow()
        ).order_by(cls.created_at.desc()).first()

    @classmethod
    def can_send_new_code(cls, email, code_type, cooldown_minutes=1):
        """
        检查是否可以发送新的验证码
        
        Args:
            email: 邮箱地址
            code_type: 验证码类型
            cooldown_minutes: 冷却时间（分钟）
            
        Returns:
            tuple: (是否可以发送, 剩余冷却时间秒数)
        """
        cutoff_time = datetime.utcnow() - timedelta(minutes=cooldown_minutes)
        recent_code = cls.query.filter_by(
            email=email.lower().strip(),
            code_type=code_type
        ).filter(
            cls.created_at > cutoff_time
        ).order_by(cls.created_at.desc()).first()
        
        if not recent_code:
            return True, 0
        
        # 计算剩余冷却时间
        elapsed = datetime.utcnow() - recent_code.created_at
        remaining = timedelta(minutes=cooldown_minutes) - elapsed
        remaining_seconds = max(0, int(remaining.total_seconds()))
        
        return remaining_seconds == 0, remaining_seconds

    def to_dict(self):
        """转换为字典（不包含敏感信息）"""
        return {
            'id': self.id,
            'email': self.email,
            'code_type': self.code_type,
            'is_used': self.is_used,
            'is_expired': self.is_expired,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'used_at': self.used_at.isoformat() if self.used_at else None,
            'attempts': self.attempts,
            'max_attempts': self.max_attempts,
            'is_valid': self.is_valid
        }

    def __repr__(self):
        return f'<VerificationCode {self.email}:{self.code_type}>'
