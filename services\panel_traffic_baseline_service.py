"""
面板流量基准服务 - 处理面板删除时的流量基准更新
"""
import logging
from typing import List, Dict, Optional
from datetime import datetime

from models import db
from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
from models.subscription import Subscription
from models.xui_panel import XUIPanel
from xui_client import XUIClient

logger = logging.getLogger(__name__)


class PanelTrafficBaselineService:
    """面板流量基准服务 - 解决面板删除导致的流量丢失问题"""
    
    def update_baselines_before_panel_removal(self, panel_id: int, group_id: Optional[int] = None) -> Dict:
        """
        在面板删除前更新相关订阅的流量基准
        
        Args:
            panel_id: 要删除的面板ID
            group_id: 分组ID，如果指定则只处理该分组的订阅
            
        Returns:
            Dict: 处理结果
        """
        try:
            panel = XUIPanel.query.get(panel_id)
            if not panel:
                return {'success': False, 'error': f'面板 {panel_id} 不存在'}
            
            logger.info(f"开始为面板 {panel.name} (ID: {panel_id}) 更新流量基准")
            
            # 获取相关订阅
            subscriptions = self._get_affected_subscriptions(panel_id, group_id)
            if not subscriptions:
                logger.info(f"面板 {panel.name} 没有相关的活跃订阅，无需更新基准")
                return {'success': True, 'message': '没有相关订阅需要处理', 'updated_count': 0}
            
            # 创建XUI客户端获取流量数据
            xui_client = XUIClient(
                base_url=panel.base_url,
                username=panel.username,
                password=panel.password,
                path_prefix=panel.path_prefix
            )
            
            # 获取面板的所有客户端流量数据
            panel_traffic_data = self._get_panel_traffic_data(xui_client)
            if not panel_traffic_data:
                logger.warning(f"无法获取面板 {panel.name} 的流量数据，可能面板已离线")
                return {'success': False, 'error': '无法获取面板流量数据，面板可能已离线'}
            
            # 更新每个订阅的基准
            updated_count = 0
            failed_count = 0
            
            for subscription in subscriptions:
                try:
                    if self._update_subscription_baseline(subscription, panel_traffic_data):
                        updated_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    logger.error(f"更新订阅 {subscription.id} 基准失败: {e}")
                    failed_count += 1
            
            # 提交数据库更改
            db.session.commit()
            
            result = {
                'success': True,
                'updated_count': updated_count,
                'failed_count': failed_count,
                'total_subscriptions': len(subscriptions)
            }
            
            if failed_count > 0:
                result['message'] = f'部分更新失败：成功 {updated_count}，失败 {failed_count}'
            else:
                result['message'] = f'成功更新 {updated_count} 个订阅的流量基准'
            
            logger.info(f"面板 {panel.name} 流量基准更新完成: {result['message']}")
            return result
            
        except Exception as e:
            logger.error(f"更新面板 {panel_id} 流量基准失败: {e}")
            db.session.rollback()
            return {'success': False, 'error': str(e)}
    
    def _get_affected_subscriptions(self, panel_id: int, group_id: Optional[int] = None) -> List[Subscription]:
        """获取受面板删除影响的订阅列表"""
        try:
            if group_id is not None:
                # 如果指定了分组，只获取该分组的活跃订阅
                subscriptions = Subscription.query.filter(
                    Subscription.group_id == group_id,
                    Subscription.is_active == True
                ).all()
            else:
                # 如果没有指定分组，获取所有活跃订阅
                subscriptions = Subscription.query.filter(
                    Subscription.is_active == True
                ).all()
            
            # 过滤出有节点配置的订阅
            affected_subscriptions = []
            for subscription in subscriptions:
                if subscription.order and subscription.order.node_configs:
                    # 检查是否有节点配置关联到这个面板
                    panel = XUIPanel.query.get(panel_id)
                    if panel:
                        panel_host = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                        for node_config in subscription.order.node_configs:
                            if (node_config.is_active and 
                                node_config.server_address == panel_host):
                                affected_subscriptions.append(subscription)
                                break
            
            return affected_subscriptions
            
        except Exception as e:
            logger.error(f"获取受影响订阅失败: {e}")
            return []
    
    def _get_panel_traffic_data(self, xui_client: XUIClient) -> Optional[Dict[str, Dict]]:
        """从面板获取所有客户端的流量数据"""
        try:
            # 获取所有入站规则和客户端统计
            inbounds = xui_client.get_inbounds()
            if not inbounds:
                return None
            
            traffic_data = {}
            for inbound in inbounds:
                client_stats = inbound.get('clientStats', [])
                for client_stat in client_stats:
                    email = client_stat.get('email')
                    if email:
                        traffic_data[email] = {
                            'upload_bytes': client_stat.get('up', 0),
                            'download_bytes': client_stat.get('down', 0),
                            'total_bytes': client_stat.get('total', 0)
                        }
            
            return traffic_data
            
        except Exception as e:
            logger.error(f"获取面板流量数据失败: {e}")
            return None
    
    def _update_subscription_baseline(self, subscription: Subscription, panel_traffic_data: Dict[str, Dict]) -> bool:
        """更新单个订阅的流量基准"""
        try:
            # 获取订阅的客户端邮箱列表
            client_emails = self._get_subscription_client_emails(subscription)
            if not client_emails:
                logger.debug(f"订阅 {subscription.id} 没有客户端邮箱")
                return False
            
            # 累计该订阅在此面板的流量
            total_upload = 0
            total_download = 0
            total_traffic = 0
            found_clients = 0
            
            for client_email in client_emails:
                if client_email in panel_traffic_data:
                    traffic = panel_traffic_data[client_email]
                    total_upload += traffic['upload_bytes']
                    total_download += traffic['download_bytes']
                    total_traffic += traffic['total_bytes']
                    found_clients += 1
                    logger.debug(f"找到客户端 {client_email} 在面板的流量: {traffic['total_bytes']} bytes")
            
            if found_clients == 0:
                logger.debug(f"订阅 {subscription.id} 在此面板没有找到流量数据")
                return False
            
            # 获取或创建流量基准记录
            baseline = SubscriptionTrafficBaseline.get_or_create_baseline(subscription.id)
            
            # 累加到基准中
            baseline.add_baseline_traffic(total_upload, total_download, total_traffic)
            
            logger.info(f"订阅 {subscription.id} 基准更新: +{total_traffic} bytes (总基准: {baseline.baseline_total_bytes} bytes)")
            return True
            
        except Exception as e:
            logger.error(f"更新订阅 {subscription.id} 基准失败: {e}")
            return False
    
    def _get_subscription_client_emails(self, subscription: Subscription) -> List[str]:
        """获取订阅的客户端邮箱列表"""
        client_emails = []
        
        try:
            order = subscription.order
            if not order:
                return client_emails
            
            # 从节点配置获取客户端邮箱
            if order.node_configs:
                for config in order.node_configs:
                    if config.is_active and config.client_email:
                        client_emails.append(config.client_email)
            else:
                # 如果没有节点配置，使用生成的客户端邮箱
                if order.customer_email:
                    generated_email = order.customer_remarks or f"{order.customer_email}_{order.order_id}"
                    client_emails.append(generated_email)
            
            return client_emails
            
        except Exception as e:
            logger.error(f"获取订阅 {subscription.id} 客户端邮箱失败: {e}")
            return []
    
    def get_subscription_total_traffic_with_baseline(self, subscription_id: int) -> Optional[Dict]:
        """
        获取订阅的总流量（包含基准）
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            Dict: 包含实时流量、基准流量和总流量的数据
        """
        try:
            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                return None
            
            # 获取基准流量
            baseline = SubscriptionTrafficBaseline.query.filter_by(
                subscription_id=subscription_id
            ).first()
            
            baseline_upload = baseline.baseline_upload_bytes if baseline else 0
            baseline_download = baseline.baseline_download_bytes if baseline else 0
            baseline_total = baseline.baseline_total_bytes if baseline else 0
            
            # 获取实时流量（从当前活跃面板）
            from services.traffic_stats_service import TrafficStatsService
            traffic_service = TrafficStatsService()
            
            # 这里需要实现获取实时流量的逻辑
            # 暂时返回基准数据，实际使用时需要集成实时流量获取
            
            return {
                'subscription_id': subscription_id,
                'baseline_upload_bytes': baseline_upload,
                'baseline_download_bytes': baseline_download,
                'baseline_total_bytes': baseline_total,
                'realtime_upload_bytes': 0,  # TODO: 实现实时流量获取
                'realtime_download_bytes': 0,
                'realtime_total_bytes': 0,
                'total_upload_bytes': baseline_upload,  # + realtime_upload
                'total_download_bytes': baseline_download,  # + realtime_download
                'total_traffic_bytes': baseline_total,  # + realtime_total
                'last_baseline_update': baseline.last_updated if baseline else None
            }
            
        except Exception as e:
            logger.error(f"获取订阅 {subscription_id} 总流量失败: {e}")
            return None
