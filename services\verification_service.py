"""
验证码服务
"""
import logging
from typing import Tuple, Optional, Dict, Any
from datetime import datetime
from flask import request
from models import db, VerificationCode, VerificationCodeType
from .email_service import email_service

logger = logging.getLogger(__name__)


class VerificationService:
    """验证码服务类"""
    
    def __init__(self):
        self.default_valid_minutes = 10  # 默认有效期10分钟
        self.cooldown_minutes = 1  # 发送冷却时间1分钟
        self.max_daily_sends = 10  # 每日最大发送次数

    def send_verification_code(self, email: str, code_type: str, 
                             valid_minutes: Optional[int] = None) -> Tuple[bool, str, Optional[Dict]]:
        """
        发送验证码
        
        Args:
            email: 邮箱地址
            code_type: 验证码类型
            valid_minutes: 有效期（分钟）
            
        Returns:
            Tuple: (是否成功, 消息, 额外信息)
        """
        try:
            email = email.lower().strip()
            
            # 检查邮箱格式
            if not self._is_valid_email(email):
                return False, '邮箱格式不正确', None
            
            # 检查发送频率限制
            can_send, remaining_seconds = VerificationCode.can_send_new_code(
                email, code_type, self.cooldown_minutes
            )
            
            if not can_send:
                return False, f'发送过于频繁，请 {remaining_seconds} 秒后再试', {
                    'remaining_seconds': remaining_seconds
                }
            
            # 检查每日发送限制
            if not self._check_daily_limit(email, code_type):
                return False, '今日发送次数已达上限，请明天再试', None
            
            # 使验证码有效期
            if valid_minutes is None:
                valid_minutes = self.default_valid_minutes
            
            # 创建新的验证码
            verification_code = VerificationCode(
                email=email,
                code_type=code_type,
                valid_minutes=valid_minutes,
                ip_address=self._get_client_ip(),
                user_agent=request.headers.get('User-Agent', '') if request else ''
            )
            
            # 保存到数据库
            db.session.add(verification_code)
            db.session.commit()
            
            # 发送邮件
            email_sent = email_service.send_verification_code_email(
                email, verification_code.code, code_type
            )
            
            if email_sent:
                logger.info(f"验证码发送成功: {email}, 类型: {code_type}")
                return True, '验证码已发送，请查收邮件', {
                    'code_id': verification_code.id,
                    'expires_at': verification_code.expires_at.isoformat()
                }
            else:
                # 邮件发送失败，删除验证码记录
                db.session.delete(verification_code)
                db.session.commit()
                return False, '邮件发送失败，请稍后重试', None
                
        except Exception as e:
            logger.error(f"发送验证码失败: {e}")
            db.session.rollback()
            return False, '系统错误，请稍后重试', None

    def verify_code(self, email: str, code: str, code_type: str, 
                   auto_cleanup: bool = True) -> Tuple[bool, str, Optional[Dict]]:
        """
        验证验证码
        
        Args:
            email: 邮箱地址
            code: 验证码
            code_type: 验证码类型
            auto_cleanup: 是否自动清理已使用的验证码
            
        Returns:
            Tuple: (是否验证成功, 消息, 验证码信息)
        """
        try:
            email = email.lower().strip()
            
            # 查找有效的验证码
            verification_code = VerificationCode.get_valid_code(email, code_type)
            
            if not verification_code:
                return False, '验证码不存在或已过期', None
            
            # 验证验证码
            success, message = verification_code.verify(code, self._get_client_ip())
            
            # 保存验证结果
            db.session.commit()
            
            # 自动清理已使用的验证码
            if success and auto_cleanup:
                self._cleanup_used_codes(email, code_type)
            
            if success:
                logger.info(f"验证码验证成功: {email}, 类型: {code_type}")
                return True, message, {
                    'code_id': verification_code.id,
                    'used_at': verification_code.used_at.isoformat() if verification_code.used_at else None
                }
            else:
                logger.warning(f"验证码验证失败: {email}, 类型: {code_type}, 原因: {message}")
                return False, message, {
                    'code_id': verification_code.id,
                    'attempts': verification_code.attempts,
                    'max_attempts': verification_code.max_attempts
                }
                
        except Exception as e:
            logger.error(f"验证验证码失败: {e}")
            db.session.rollback()
            return False, '系统错误，请稍后重试', None

    def get_code_status(self, email: str, code_type: str) -> Optional[Dict]:
        """
        获取验证码状态
        
        Args:
            email: 邮箱地址
            code_type: 验证码类型
            
        Returns:
            Dict: 验证码状态信息
        """
        try:
            email = email.lower().strip()
            verification_code = VerificationCode.get_valid_code(email, code_type)
            
            if verification_code:
                return verification_code.to_dict()
            
            return None
            
        except Exception as e:
            logger.error(f"获取验证码状态失败: {e}")
            return None

    def cleanup_expired_codes(self, days_old: int = 7) -> int:
        """
        清理过期的验证码
        
        Args:
            days_old: 清理多少天前的记录
            
        Returns:
            int: 清理的记录数
        """
        try:
            count = VerificationCode.cleanup_expired(days_old)
            db.session.commit()
            logger.info(f"清理了 {count} 个过期验证码")
            return count
            
        except Exception as e:
            logger.error(f"清理过期验证码失败: {e}")
            db.session.rollback()
            return 0

    def _is_valid_email(self, email: str) -> bool:
        """验证邮箱格式"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

    def _get_client_ip(self) -> Optional[str]:
        """获取客户端IP地址"""
        if not request:
            return None
        
        # 检查代理头
        if request.headers.get('X-Forwarded-For'):
            return request.headers.get('X-Forwarded-For').split(',')[0].strip()
        elif request.headers.get('X-Real-IP'):
            return request.headers.get('X-Real-IP')
        else:
            return request.remote_addr

    def _check_daily_limit(self, email: str, code_type: str) -> bool:
        """检查每日发送限制"""
        try:
            from datetime import date
            today = date.today()
            
            # 查询今日发送次数
            today_count = VerificationCode.query.filter(
                VerificationCode.email == email,
                VerificationCode.code_type == code_type,
                db.func.date(VerificationCode.created_at) == today
            ).count()
            
            return today_count < self.max_daily_sends
            
        except Exception as e:
            logger.error(f"检查每日发送限制失败: {e}")
            return True  # 出错时允许发送

    def _cleanup_used_codes(self, email: str, code_type: str):
        """清理已使用的验证码"""
        try:
            used_codes = VerificationCode.query.filter_by(
                email=email,
                code_type=code_type,
                is_used=True
            ).all()
            
            for code in used_codes:
                db.session.delete(code)
            
            if used_codes:
                logger.debug(f"清理了 {len(used_codes)} 个已使用的验证码")
                
        except Exception as e:
            logger.error(f"清理已使用验证码失败: {e}")

    def get_verification_stats(self) -> Dict[str, Any]:
        """获取验证码统计信息"""
        try:
            from datetime import date, timedelta
            
            today = date.today()
            yesterday = today - timedelta(days=1)
            week_ago = today - timedelta(days=7)
            
            stats = {
                'total_codes': VerificationCode.query.count(),
                'today_sent': VerificationCode.query.filter(
                    db.func.date(VerificationCode.created_at) == today
                ).count(),
                'yesterday_sent': VerificationCode.query.filter(
                    db.func.date(VerificationCode.created_at) == yesterday
                ).count(),
                'week_sent': VerificationCode.query.filter(
                    VerificationCode.created_at >= week_ago
                ).count(),
                'success_rate': 0,
                'by_type': {}
            }
            
            # 计算成功率
            total_attempts = VerificationCode.query.filter(
                VerificationCode.attempts > 0
            ).count()
            
            successful_verifications = VerificationCode.query.filter(
                VerificationCode.is_used == True
            ).count()
            
            if total_attempts > 0:
                stats['success_rate'] = round((successful_verifications / total_attempts) * 100, 2)
            
            # 按类型统计
            for code_type in [VerificationCodeType.REGISTER, VerificationCodeType.LOGIN, 
                            VerificationCodeType.RESET_PASSWORD, VerificationCodeType.EMAIL_CHANGE]:
                type_count = VerificationCode.query.filter_by(code_type=code_type).count()
                stats['by_type'][code_type] = type_count
            
            return stats
            
        except Exception as e:
            logger.error(f"获取验证码统计失败: {e}")
            return {}


# 创建全局实例
verification_service = VerificationService()
