"""
流量统计相关模型
"""
from datetime import datetime
from . import db


class TrafficStats(db.Model):
    """流量统计模型 - 按分组统计用户订阅流量"""
    __tablename__ = 'traffic_stats'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, index=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id', ondelete='CASCADE'), nullable=False, index=True)
    group_id = db.Column(db.Integer, db.<PERSON>ey('xui_panel_groups.id'), nullable=True, index=True)

    # 流量统计（字节）
    upload_bytes = db.Column(db.BigInteger, nullable=False, default=0)
    download_bytes = db.Column(db.BigInteger, nullable=False, default=0)
    total_bytes = db.Column(db.BigInteger, nullable=False, default=0)

    # 时间字段
    recorded_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, index=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    # 关联关系 - 修改为级联删除
    user = db.relationship('User', backref='traffic_stats', lazy=True)
    subscription = db.relationship('Subscription', backref=db.backref('traffic_stats', cascade='all, delete-orphan'), lazy=True)
    group = db.relationship('XUIPanelGroup', backref='traffic_stats', lazy=True)

    def __repr__(self):
        return f'<TrafficStats user_id={self.user_id} group_id={self.group_id} total_gb={self.total_gb:.2f}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'subscription_id': self.subscription_id,
            'group_id': self.group_id,
            'upload_bytes': self.upload_bytes,
            'download_bytes': self.download_bytes,
            'total_bytes': self.total_bytes,
            'upload_gb': self.upload_gb,
            'download_gb': self.download_gb,
            'total_gb': self.total_gb,
            'upload_mb': self.upload_mb,
            'download_mb': self.download_mb,
            'total_mb': self.total_mb,
            'recorded_at': self.recorded_at.isoformat() if self.recorded_at else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    @property
    def upload_gb(self):
        """上传流量（GB）"""
        return round(self.upload_bytes / (1024 * 1024 * 1024), 3)

    @property
    def download_gb(self):
        """下载流量（GB）"""
        return round(self.download_bytes / (1024 * 1024 * 1024), 3)

    @property
    def total_gb(self):
        """总流量（GB）"""
        return round(self.total_bytes / (1024 * 1024 * 1024), 3)

    @property
    def upload_mb(self):
        """上传流量（MB）"""
        return round(self.upload_bytes / (1024 * 1024), 2)

    @property
    def download_mb(self):
        """下载流量（MB）"""
        return round(self.download_bytes / (1024 * 1024), 2)

    @property
    def total_mb(self):
        """总流量（MB）"""
        return round(self.total_bytes / (1024 * 1024), 2)
