<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}节点商城{% endblock %}</title>
    <!-- Bootstrap 5.3.x 最新版本 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    <!-- Vue.js 3 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <meta name="description" content="专业的节点商城，提供稳定高速的网络节点服务">
    <meta name="keywords" content="节点,VPN,网络加速,代理服务">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('shop.shop_index') }}">
                <i class="bi bi-cloud-arrow-up me-2" style="font-size: 1.5rem;"></i>
                <span class="fw-bold">节点商城</span>
            </a>
            <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link px-3 rounded" href="{{ url_for('shop.shop_index') }}">
                            <i class="bi bi-shop me-1"></i> 商店
                        </a>
                    </li>
                    {% if session.user_id %}
                    <li class="nav-item">
                        <a class="nav-link px-3 rounded" href="{{ url_for('user.dashboard') }}">
                            <i class="bi bi-person-circle me-1"></i> 用户中心
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link px-3 rounded" href="{{ url_for('user.subscriptions') }}">
                            <i class="bi bi-collection me-1"></i> 我的订阅
                        </a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link px-3 rounded" href="{{ url_for('shop.my_orders') }}">
                            <i class="bi bi-list-ul me-1"></i> 我的订单
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if session.user_id %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle px-3 rounded d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-2"></i>
                                <span>{{ session.username }}</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end shadow border-0">
                                <li><a class="dropdown-item py-2" href="{{ url_for('user.dashboard') }}">
                                    <i class="bi bi-person-circle me-2 text-primary"></i> 用户中心
                                </a></li>
                                <li><a class="dropdown-item py-2" href="{{ url_for('user.subscriptions') }}">
                                    <i class="bi bi-collection me-2 text-info"></i> 我的订阅
                                </a></li>
                                <li><a class="dropdown-item py-2" href="{{ url_for('user.profile') }}">
                                    <i class="bi bi-person-gear me-2 text-success"></i> 个人资料
                                </a></li>
                                {% if session.role == 'admin' %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item py-2" href="{{ url_for('admin.dashboard') }}">
                                        <i class="bi bi-speedometer2 me-2 text-primary"></i> 管理后台
                                    </a></li>
                                {% endif %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item py-2" href="{{ url_for('auth.logout') }}">
                                    <i class="bi bi-box-arrow-right me-2 text-danger"></i> 退出登录
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link px-3 rounded" href="{{ url_for('auth.login') }}">
                                <i class="bi bi-person me-1"></i> 登录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link px-3 rounded btn btn-outline-light ms-2" href="{{ url_for('auth.register') }}">
                                <i class="bi bi-person-plus me-1"></i> 注册
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <!-- 消息提示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show shadow-sm border-0" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- 主要内容 -->
    <main id="app" class="fade-in">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="footer py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4">
                    <h5 class="fw-bold mb-3">
                        <i class="bi bi-cloud-arrow-up me-2 text-primary"></i>节点商城
                    </h5>
                    <p class="text-muted mb-3">专业的网络节点服务提供商，致力于为用户提供稳定、高速、安全的网络连接体验。</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-muted"><i class="bi bi-wechat fs-5"></i></a>
                        <a href="#" class="text-muted"><i class="bi bi-qq fs-5"></i></a>
                        <a href="#" class="text-muted"><i class="bi bi-telegram fs-5"></i></a>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <h6 class="fw-bold mb-3">服务支持</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="#" class="text-muted text-decoration-none"><i class="bi bi-chevron-right me-1"></i>使用教程</a></li>
                        <li class="mb-2"><a href="#" class="text-muted text-decoration-none"><i class="bi bi-chevron-right me-1"></i>常见问题</a></li>
                        <li class="mb-2"><a href="#" class="text-muted text-decoration-none"><i class="bi bi-chevron-right me-1"></i>技术支持</a></li>
                        <li class="mb-2"><a href="#" class="text-muted text-decoration-none"><i class="bi bi-chevron-right me-1"></i>服务条款</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4">
                    <h6 class="fw-bold mb-3">联系我们</h6>
                    <div class="text-muted">
                        <p class="mb-2">
                            <i class="bi bi-envelope me-2 text-primary"></i>
                            <EMAIL>
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-telegram me-2 text-primary"></i>
                            @nodestore_support
                        </p>
                        <p class="mb-2">
                            <i class="bi bi-clock me-2 text-primary"></i>
                            7×24小时在线服务
                        </p>
                    </div>
                </div>
            </div>
            <hr class="my-4">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <small class="text-muted">&copy; 2024 节点商城. 保留所有权利.</small>
                </div>
                <div class="col-md-6 text-md-end">
                    <small class="text-muted">
                        <a href="#" class="text-muted text-decoration-none me-3">隐私政策</a>
                        <a href="#" class="text-muted text-decoration-none">服务协议</a>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Vue.js 应用初始化 -->
    <script>
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    loading: false,
                    message: ''
                }
            },
            methods: {
                showLoading() {
                    this.loading = true;
                },
                hideLoading() {
                    this.loading = false;
                },
                showMessage(msg, type = 'success') {
                    this.message = msg;
                    // 可以在这里添加更多的消息处理逻辑
                }
            },
            mounted() {
                // 页面加载完成后的初始化
                console.log('节点商城应用已启动');
            }
        }).mount('#app');

        // 全局工具函数
        window.utils = {
            formatPrice: (price) => {
                return '¥' + parseFloat(price).toFixed(2);
            },
            formatDate: (date) => {
                return new Date(date).toLocaleDateString('zh-CN');
            }
        };
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
