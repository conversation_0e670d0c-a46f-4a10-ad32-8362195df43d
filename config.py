"""
节点销售和管理系统配置文件
"""
import os

class Config:
    """基础配置类"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here'

    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///node_sales.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # X-UI 面板配置已迁移到数据库
    # 现在通过管理界面或ConfigService管理面板配置
    # 如需添加面板，请使用管理界面或直接在数据库中操作
    #
    # 迁移说明：
    # 1. 原有的XUI_PANELS配置已移动到数据库的xui_panels表
    # 2. 使用ConfigService统一管理配置
    # 3. 支持运行时动态添加/修改面板配置
    # 4. 保持向后兼容性，如果数据库为空会使用环境变量作为回退配置

    # X-UI API路径配置
    XUI_LOGIN_PATH = '/login'
    XUI_INBOUNDS_PATH = '/panel/inbounds'  # HTML页面路径，用于备用解析
    XUI_INBOUNDS_API_PATH = '/panel/inbound/list'  # POST API端点，用于获取入站规则
    XUI_ADD_CLIENT_PATH = '/panel/inbound/addClient'
    XUI_UPDATE_USER_PATH = '/panel/api/inbounds/update'
    XUI_DELETE_CLIENT_PATH = '/panel/inbound/{inbound_id}/delClient/{client_uuid}'  # 删除客户端API路径模板
    XUI_TRAFFIC_PATH = '/panel/api/inbounds/getClientTraffics'

    # 负载均衡配置
    XUI_LOAD_BALANCE_STRATEGY = os.environ.get('XUI_LOAD_BALANCE_STRATEGY') or 'round_robin'  # round_robin, least_clients, priority
    XUI_AUTO_FAILOVER = os.environ.get('XUI_AUTO_FAILOVER', 'true').lower() in ['true', 'on', '1']

    # 邮件配置
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER')

    # 请求配置
    REQUEST_TIMEOUT = 30
    MAX_RETRIES = 3

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = 'node_sales.log'

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False

class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'  # 使用内存数据库
    WTF_CSRF_ENABLED = False

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
