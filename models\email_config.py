"""
邮件配置数据模型
"""
from datetime import datetime
from . import db
from cryptography.fernet import Fernet
import os
import base64


class EmailConfig(db.Model):
    """邮件配置模型"""
    __tablename__ = 'email_configs'

    id = db.Column(db.Integer, primary_key=True)
    
    # 基本配置
    name = db.Column(db.String(100), nullable=False, default='默认邮件配置')
    smtp_server = db.Column(db.String(255), nullable=False)
    smtp_port = db.Column(db.Integer, nullable=False, default=587)
    use_tls = db.Column(db.Boolean, nullable=False, default=True)
    use_ssl = db.Column(db.Boolean, nullable=False, default=False)
    
    # 认证信息
    username = db.Column(db.String(255), nullable=False)
    password_encrypted = db.Column(db.Text, nullable=False)  # 加密存储
    
    # 发送者信息
    default_sender = db.Column(db.String(255), nullable=True)
    sender_name = db.Column(db.String(100), nullable=True)
    
    # 状态和配置
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    is_default = db.Column(db.Boolean, nullable=False, default=False)
    
    # 限制配置
    daily_limit = db.Column(db.Integer, nullable=False, default=1000)  # 每日发送限制
    rate_limit_per_minute = db.Column(db.Integer, nullable=False, default=10)  # 每分钟发送限制
    
    # 时间戳
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_used_at = db.Column(db.DateTime, nullable=True)
    
    # 统计信息
    total_sent = db.Column(db.Integer, nullable=False, default=0)
    total_failed = db.Column(db.Integer, nullable=False, default=0)

    def __init__(self, **kwargs):
        # 处理密码加密
        if 'password' in kwargs:
            password = kwargs.pop('password')
            kwargs['password_encrypted'] = self._encrypt_password(password)
        super(EmailConfig, self).__init__(**kwargs)

    @staticmethod
    def _get_encryption_key():
        """获取加密密钥"""
        # 从环境变量获取密钥，如果不存在则生成一个
        key = os.environ.get('EMAIL_ENCRYPTION_KEY')
        if not key:
            # 生成新密钥（生产环境应该预先设置）
            key = base64.urlsafe_b64encode(os.urandom(32)).decode()
            os.environ['EMAIL_ENCRYPTION_KEY'] = key
        return key.encode()

    def _encrypt_password(self, password):
        """加密密码"""
        if not password:
            return ''
        
        key = self._get_encryption_key()
        f = Fernet(key)
        return f.encrypt(password.encode()).decode()

    def _decrypt_password(self):
        """解密密码"""
        if not self.password_encrypted:
            return ''
        
        try:
            key = self._get_encryption_key()
            f = Fernet(key)
            return f.decrypt(self.password_encrypted.encode()).decode()
        except Exception:
            return ''

    @property
    def password(self):
        """获取解密后的密码"""
        return self._decrypt_password()

    @password.setter
    def password(self, value):
        """设置密码（自动加密）"""
        self.password_encrypted = self._encrypt_password(value)

    def update_stats(self, success=True):
        """更新统计信息"""
        if success:
            self.total_sent = (self.total_sent or 0) + 1
        else:
            self.total_failed = (self.total_failed or 0) + 1
        self.last_used_at = datetime.utcnow()

    def to_dict(self):
        """转换为字典（不包含敏感信息）"""
        return {
            'id': self.id,
            'name': self.name,
            'smtp_server': self.smtp_server,
            'smtp_port': self.smtp_port,
            'use_tls': self.use_tls,
            'use_ssl': self.use_ssl,
            'username': self.username,
            'default_sender': self.default_sender,
            'sender_name': self.sender_name,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'daily_limit': self.daily_limit,
            'rate_limit_per_minute': self.rate_limit_per_minute,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_used_at': self.last_used_at.isoformat() if self.last_used_at else None,
            'total_sent': self.total_sent,
            'total_failed': self.total_failed
        }

    @classmethod
    def get_default_config(cls):
        """获取默认邮件配置"""
        return cls.query.filter_by(is_active=True, is_default=True).first()

    @classmethod
    def get_active_configs(cls):
        """获取所有活跃的邮件配置"""
        return cls.query.filter_by(is_active=True).order_by(cls.is_default.desc(), cls.id.asc()).all()

    @classmethod
    def get_all_configs(cls):
        """获取所有邮件配置（包括非活跃的）"""
        return cls.query.order_by(cls.is_default.desc(), cls.is_active.desc(), cls.id.asc()).all()

    @classmethod
    def get_single_config(cls):
        """获取唯一的邮件配置（单配置模式）"""
        # 优先返回默认配置
        config = cls.query.filter_by(is_default=True, is_active=True).first()
        if config:
            return config

        # 如果没有默认配置，返回最新的活跃配置
        config = cls.query.filter_by(is_active=True).order_by(cls.created_at.desc()).first()
        if config:
            return config

        # 如果没有活跃配置，返回最新的配置
        return cls.query.order_by(cls.created_at.desc()).first()

    @classmethod
    def create_or_update_config(cls, **kwargs):
        """创建或更新唯一的邮件配置（单配置模式）"""
        from . import db

        # 获取现有配置
        existing_config = cls.get_single_config()

        if existing_config:
            # 更新现有配置
            for key, value in kwargs.items():
                if hasattr(existing_config, key) and key != 'id':
                    setattr(existing_config, key, value)
            existing_config.updated_at = datetime.utcnow()
            existing_config.is_default = True
            existing_config.is_active = True

            # 删除其他配置（确保只有一个配置）
            other_configs = cls.query.filter(cls.id != existing_config.id).all()
            for config in other_configs:
                db.session.delete(config)

            db.session.commit()
            return existing_config
        else:
            # 创建新配置
            kwargs['is_default'] = True
            kwargs['is_active'] = True
            new_config = cls(**kwargs)
            db.session.add(new_config)
            db.session.commit()
            return new_config

    def __repr__(self):
        return f'<EmailConfig {self.name}>'
