{% extends "base.html" %}

{% block title %}管理员登录 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-danger text-white text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-shield-lock"></i> 管理员登录
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.admin_login') }}">
                        <div class="mb-3">
                            <label for="username" class="form-label">管理员用户名/邮箱</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-box-arrow-in-right"></i> 管理员登录
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        {% if show_register_link %}
                        首次访问？ 
                        <a href="{{ url_for('admin.admin_register') }}" class="text-decoration-none">创建管理员账户</a>
                        <br>
                        {% endif %}
                        <a href="{{ url_for('shop.shop_index') }}" class="text-decoration-none">返回商店</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle"></i> 安全提醒</h6>
                <ul class="mb-0">
                    <li>管理员账户拥有系统最高权限</li>
                    <li>请使用强密码保护账户安全</li>
                    <li>不要在公共场所登录管理后台</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
