{% extends "base.html" %}

{% block title %}续费配置管理 - 管理员面板{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-arrow-clockwise"></i> 续费配置管理
                    </h5>
                    <button type="button" class="btn btn-primary" onclick="saveConfig()">
                        <i class="bi bi-save"></i> 保存配置
                    </button>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>说明：</strong>设置不同续费时长的折扣百分比。用户续费时将根据原订单价格和设置的折扣计算最终价格。
                    </div>

                    <form id="renewalConfigForm">
                        <div class="row">
                            {% for config in pricing_configs %}
                            <div class="col-md-4 mb-4">
                                <div class="card border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h6 class="mb-0">
                                            <i class="bi bi-calendar-check"></i>
                                            {{ config.duration_months }}个月续费
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label class="form-label">折扣百分比 (%)</label>
                                            <div class="input-group">
                                                <input type="number" 
                                                       class="form-control" 
                                                       name="discount_{{ config.duration_months }}"
                                                       value="{{ config.discount_percentage }}"
                                                       min="0" 
                                                       max="100" 
                                                       step="0.1"
                                                       data-duration="{{ config.duration_months }}">
                                                <span class="input-group-text">%</span>
                                            </div>
                                            <div class="form-text">
                                                设置0表示无折扣，设置20表示8折优惠
                                            </div>
                                        </div>
                                        
                                        <div class="form-check">
                                            <input class="form-check-input" 
                                                   type="checkbox" 
                                                   name="active_{{ config.duration_months }}"
                                                   data-duration="{{ config.duration_months }}"
                                                   {% if config.is_active %}checked{% endif %}>
                                            <label class="form-check-label">
                                                启用此续费选项
                                            </label>
                                        </div>
                                        
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                <strong>示例：</strong><br>
                                                原价100元/月，{{ config.duration_months }}个月续费<br>
                                                折扣后价格：{{ "%.2f"|format((config.duration_months * 100 * (100 - config.discount_percentage) / 100)) }}元
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </form>

                    <div class="mt-4">
                        <h6>当前配置预览</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>续费时长</th>
                                        <th>折扣百分比</th>
                                        <th>状态</th>
                                        <th>最后更新</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for config in pricing_configs %}
                                    <tr>
                                        <td>{{ config.duration_months }}个月</td>
                                        <td>{{ config.discount_percentage }}%</td>
                                        <td>
                                            {% if config.is_active %}
                                                <span class="badge bg-success">启用</span>
                                            {% else %}
                                                <span class="badge bg-secondary">禁用</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ config.updated_at.strftime('%Y-%m-%d %H:%M:%S') if config.updated_at else '-' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载提示模态框 -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">保存中...</span>
                </div>
                <div class="mt-2">保存配置中...</div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function saveConfig() {
    // 收集表单数据
    const configs = [];
    const form = document.getElementById('renewalConfigForm');
    
    // 获取所有折扣输入框
    const discountInputs = form.querySelectorAll('input[name^="discount_"]');
    
    discountInputs.forEach(input => {
        const duration = parseInt(input.dataset.duration);
        const discount = parseFloat(input.value) || 0;
        const activeCheckbox = form.querySelector(`input[name="active_${duration}"]`);
        const isActive = activeCheckbox ? activeCheckbox.checked : true;
        
        configs.push({
            duration_months: duration,
            discount_percentage: discount,
            is_active: isActive
        });
    });
    
    // 显示加载提示
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    // 发送请求
    fetch('/admin/api/renewal-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            configs: configs
        })
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        
        if (data.success) {
            // 显示成功消息
            showAlert('success', data.message || '配置保存成功');
            
            // 刷新页面以显示最新数据
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showAlert('danger', data.message || '保存失败');
        }
    })
    .catch(error => {
        loadingModal.hide();
        console.error('保存配置失败:', error);
        showAlert('danger', '保存配置时发生错误');
    });
}

function showAlert(type, message) {
    // 创建警告框
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 实时计算示例价格
document.addEventListener('DOMContentLoaded', function() {
    const discountInputs = document.querySelectorAll('input[name^="discount_"]');
    
    discountInputs.forEach(input => {
        input.addEventListener('input', function() {
            updateExample(this);
        });
    });
});

function updateExample(input) {
    const duration = parseInt(input.dataset.duration);
    const discount = parseFloat(input.value) || 0;
    const basePrice = 100; // 示例基础价格
    
    const totalPrice = duration * basePrice;
    const discountedPrice = totalPrice * (100 - discount) / 100;
    
    // 更新示例文本
    const card = input.closest('.card-body');
    const exampleText = card.querySelector('small');
    if (exampleText) {
        exampleText.innerHTML = `
            <strong>示例：</strong><br>
            原价${basePrice}元/月，${duration}个月续费<br>
            折扣后价格：${discountedPrice.toFixed(2)}元
        `;
    }
}
</script>
{% endblock %}
