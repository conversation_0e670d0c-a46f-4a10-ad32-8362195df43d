"""
数据库迁移脚本：添加订阅流量基准表
解决面板删除导致的流量丢失问题
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
import logging

logger = logging.getLogger(__name__)


def create_subscription_traffic_baseline_table():
    """创建订阅流量基准表"""
    try:
        from models import db
        from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
        from models.subscription import Subscription

        # 创建表
        db.create_all()
        logger.info("订阅流量基准表创建成功")

        # 为现有订阅初始化基准记录（可选）
        existing_subscriptions = Subscription.query.filter_by(is_active=True).all()

        initialized_count = 0
        for subscription in existing_subscriptions:
            # 检查是否已有基准记录
            existing_baseline = SubscriptionTrafficBaseline.query.filter_by(
                subscription_id=subscription.id
            ).first()

            if not existing_baseline:
                # 创建初始基准记录（基准为0）
                baseline = SubscriptionTrafficBaseline(subscription_id=subscription.id)
                db.session.add(baseline)
                initialized_count += 1

        if initialized_count > 0:
            db.session.commit()
            logger.info(f"为 {initialized_count} 个现有订阅初始化了流量基准记录")
        else:
            logger.info("没有需要初始化基准的订阅")

        return True

    except Exception as e:
        logger.error(f"创建订阅流量基准表失败: {e}")
        try:
            from models import db
            db.session.rollback()
        except:
            pass
        return False


def rollback_subscription_traffic_baseline_table():
    """回滚：删除订阅流量基准表"""
    try:
        from models import db
        from models.subscription_traffic_baseline import SubscriptionTrafficBaseline

        # 删除表
        SubscriptionTrafficBaseline.__table__.drop(db.engine)
        logger.info("订阅流量基准表删除成功")
        return True

    except Exception as e:
        logger.error(f"删除订阅流量基准表失败: {e}")
        return False


if __name__ == '__main__':
    # 直接运行此脚本进行迁移
    from app import create_app
    
    app = create_app()
    with app.app_context():
        print("开始创建订阅流量基准表...")
        if create_subscription_traffic_baseline_table():
            print("✅ 迁移成功完成")
        else:
            print("❌ 迁移失败")
