"""
定时任务调度服务 - 管理流量统计等定时任务
"""
import logging
import atexit
from datetime import datetime
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.triggers.cron import CronTrigger
from services.traffic_stats_service import TrafficStatsService
from services.expiration_service import ExpirationService

logger = logging.getLogger(__name__)

class SchedulerService:
    """定时任务调度服务类"""
    
    def __init__(self, app=None):
        self.scheduler = None
        self.traffic_stats_service = None
        self.expiration_service = None
        self.app = app
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """初始化Flask应用"""
        self.app = app
        
        # 在应用上下文中初始化服务
        with app.app_context():
            self.traffic_stats_service = TrafficStatsService()
            self.expiration_service = ExpirationService()
            self._init_scheduler()
            self._register_jobs()
            
        # 注册应用关闭时的清理函数
        atexit.register(self.shutdown)
    
    def _init_scheduler(self):
        """初始化调度器"""
        try:
            self.scheduler = BackgroundScheduler(
                timezone='Asia/Shanghai',
                job_defaults={
                    'coalesce': True,  # 合并错过的任务
                    'max_instances': 1,  # 每个任务最多只能有一个实例运行
                    'misfire_grace_time': 300  # 错过任务的宽限时间（秒）
                }
            )
            
            logger.info("定时任务调度器初始化成功")
            
        except Exception as e:
            logger.error(f"初始化定时任务调度器失败: {e}")
            raise
    
    def _register_jobs(self):
        """注册定时任务"""
        try:
            # 流量统计任务 - 每10分钟执行一次（增加间隔避免重叠）
            self.scheduler.add_job(
                func=self._collect_traffic_stats_job,
                trigger=IntervalTrigger(minutes=5),
                id='traffic_stats_collection',
                name='流量统计收集任务',
                replace_existing=True,
                max_instances=1,  # 确保只有一个实例运行
                coalesce=True     # 合并错过的任务
            )
            
            # 清理旧数据任务 - 每天凌晨2点执行
            self.scheduler.add_job(
                func=self._cleanup_old_data_job,
                trigger=CronTrigger(hour=2, minute=0),
                id='cleanup_old_data',
                name='清理旧数据任务',
                replace_existing=True
            )
            
            # 到期订阅清理任务 - 每小时执行一次
            self.scheduler.add_job(
                func=self._process_expired_subscriptions_job,
                trigger=IntervalTrigger(hours=1),
                id='expired_subscriptions_cleanup',
                name='到期订阅清理任务',
                replace_existing=True
            )

            # 验证码清理任务 - 每天凌晨3点执行
            self.scheduler.add_job(
                func=self._cleanup_verification_codes_job,
                trigger=CronTrigger(hour=3, minute=0),
                id='cleanup_verification_codes',
                name='验证码清理任务',
                replace_existing=True
            )

            # 即将到期订阅通知任务 - 每天上午9点执行
            self.scheduler.add_job(
                func=self._check_expiring_subscriptions_job,
                trigger=CronTrigger(hour=9, minute=0),
                id='check_expiring_subscriptions',
                name='即将到期订阅通知任务',
                replace_existing=True
            )

            # 流量耗尽订阅清理任务 - 每30分钟执行一次
            self.scheduler.add_job(
                func=self._process_traffic_exhausted_subscriptions_job,
                trigger=IntervalTrigger(minutes=30),
                id='traffic_exhausted_subscriptions_cleanup',
                name='流量耗尽订阅清理任务',
                replace_existing=True
            )

            logger.info("定时任务注册成功")
            
        except Exception as e:
            logger.error(f"注册定时任务失败: {e}")
            raise
    
    def start(self):
        """启动调度器"""
        try:
            if self.scheduler and not self.scheduler.running:
                self.scheduler.start()
                logger.info("定时任务调度器启动成功")
                
                # 记录已注册的任务
                jobs = self.scheduler.get_jobs()
                for job in jobs:
                    logger.info(f"已注册任务: {job.name} (ID: {job.id})")
            
        except Exception as e:
            logger.error(f"启动定时任务调度器失败: {e}")
    
    def shutdown(self):
        """关闭调度器"""
        try:
            if self.scheduler and self.scheduler.running:
                self.scheduler.shutdown(wait=False)
                logger.info("定时任务调度器已关闭")
                
        except Exception as e:
            logger.error(f"关闭定时任务调度器失败: {e}")
    
    def _collect_traffic_stats_job(self):
        """流量统计收集任务"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return

            with self.app.app_context():
                logger.info("开始执行流量统计收集任务...")
                start_time = datetime.now()

                # 设置任务超时时间（8分钟）
                import signal

                def timeout_handler(signum, frame):
                    raise TimeoutError("流量统计收集任务超时")

                # 在Windows上signal.alarm不可用，所以使用try-except
                try:
                    signal.signal(signal.SIGALRM, timeout_handler)
                    signal.alarm(480)  # 8分钟超时
                except (AttributeError, OSError):
                    # Windows系统不支持SIGALRM，跳过超时设置
                    pass

                try:
                    success = self.traffic_stats_service.collect_all_traffic_stats()
                finally:
                    # 取消超时设置
                    try:
                        signal.alarm(0)
                    except (AttributeError, OSError):
                        pass

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                if success:
                    logger.info(f"流量统计收集任务完成，耗时: {duration:.2f}秒")
                else:
                    logger.warning(f"流量统计收集任务部分失败，耗时: {duration:.2f}秒")

                # 如果任务执行时间过长，记录警告
                if duration > 300:  # 5分钟
                    logger.warning(f"流量统计收集任务执行时间过长: {duration:.2f}秒，建议检查网络连接或优化任务")

        except TimeoutError as e:
            logger.error(f"流量统计收集任务超时: {e}")
        except Exception as e:
            logger.error(f"流量统计收集任务执行失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
    
    def _cleanup_old_data_job(self):
        """清理旧数据任务"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return
                
            with self.app.app_context():
                logger.info("开始执行清理旧数据任务...")
                start_time = datetime.now()
                
                # 清理90天前的流量统计数据
                success = self.traffic_stats_service.cleanup_old_stats(days=90)
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                if success:
                    logger.info(f"清理旧数据任务完成，耗时: {duration:.2f}秒")
                else:
                    logger.warning(f"清理旧数据任务失败，耗时: {duration:.2f}秒")
                    
        except Exception as e:
            logger.error(f"清理旧数据任务执行失败: {e}")
    
    def _process_expired_subscriptions_job(self):
        """到期订阅处理任务"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return
                
            with self.app.app_context():
                logger.info("开始执行到期订阅处理任务...")
                start_time = datetime.now()
                
                stats = self.expiration_service.process_expired_subscriptions()
                
                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()
                
                # 记录任务执行结果
                if stats['total_expired'] > 0:
                    logger.info(f"到期订阅处理任务完成 - 处理了 {stats['total_expired']} 个到期订阅，"
                               f"成功删除 {stats['successfully_deleted']} 个，"
                               f"失败 {stats['failed_deletions']} 个，耗时: {duration:.2f}秒")
                else:
                    logger.info(f"到期订阅处理任务完成 - 没有找到到期订阅，耗时: {duration:.2f}秒")
                
                # 如果有错误，记录错误信息
                if stats['errors']:
                    logger.error(f"到期订阅处理任务存在错误: {stats['errors']}")
                    
        except Exception as e:
            logger.error(f"到期订阅处理任务执行失败: {e}")

    def _cleanup_verification_codes_job(self):
        """验证码清理任务"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return

            with self.app.app_context():
                logger.info("开始执行验证码清理任务...")
                start_time = datetime.now()

                from services.verification_service import verification_service
                cleaned_count = verification_service.cleanup_expired_codes(days_old=7)

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                logger.info(f"验证码清理任务完成 - 清理了 {cleaned_count} 个过期验证码，耗时: {duration:.2f}秒")

        except Exception as e:
            logger.error(f"验证码清理任务执行失败: {e}")

    def _check_expiring_subscriptions_job(self):
        """即将到期订阅检查任务"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return

            with self.app.app_context():
                logger.info("开始执行即将到期订阅检查任务...")
                start_time = datetime.now()

                results = self.expiration_service.check_and_notify_expiring_subscriptions()

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                logger.info(f"即将到期订阅检查任务完成 - 检查 {results['total_checked']} 个订阅，"
                           f"发送 {results['warnings_sent']} 个警告，"
                           f"发送 {results['expiry_notifications_sent']} 个到期通知，"
                           f"耗时: {duration:.2f}秒")

                if results['errors']:
                    logger.error(f"即将到期订阅检查任务存在错误: {results['errors']}")

        except Exception as e:
            logger.error(f"即将到期订阅检查任务执行失败: {e}")

    def _process_traffic_exhausted_subscriptions_job(self):
        """流量耗尽订阅处理任务"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return

            with self.app.app_context():
                logger.info("开始执行流量耗尽订阅处理任务...")
                start_time = datetime.now()

                stats = self.expiration_service.process_traffic_exhausted_subscriptions()

                end_time = datetime.now()
                duration = (end_time - start_time).total_seconds()

                # 记录任务执行结果
                if stats['total_exhausted'] > 0:
                    logger.info(f"流量耗尽订阅处理任务完成 - 处理了 {stats['total_exhausted']} 个流量耗尽订阅，"
                               f"成功删除 {stats['successfully_deleted']} 个，"
                               f"失败 {stats['failed_deletions']} 个，耗时: {duration:.2f}秒")
                else:
                    logger.info(f"流量耗尽订阅处理任务完成 - 没有找到流量耗尽订阅，耗时: {duration:.2f}秒")

                # 如果有错误，记录错误信息
                if stats['errors']:
                    logger.error(f"流量耗尽订阅处理任务存在错误: {stats['errors']}")

        except Exception as e:
            logger.error(f"流量耗尽订阅处理任务执行失败: {e}")

    def get_job_status(self):
        """获取任务状态"""
        try:
            if not self.scheduler:
                return {'status': 'not_initialized', 'jobs': []}
            
            jobs = []
            for job in self.scheduler.get_jobs():
                try:
                    next_run_time = getattr(job, 'next_run_time', None)
                    jobs.append({
                        'id': job.id,
                        'name': job.name,
                        'next_run_time': next_run_time.isoformat() if next_run_time else None,
                        'trigger': str(job.trigger)
                    })
                except Exception as e:
                    logger.warning(f"获取任务 {job.id} 信息失败: {e}")
                    jobs.append({
                        'id': job.id,
                        'name': getattr(job, 'name', 'Unknown'),
                        'next_run_time': None,
                        'trigger': 'Unknown'
                    })
            
            return {
                'status': 'running' if self.scheduler.running else 'stopped',
                'jobs': jobs
            }
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {e}")
            return {'status': 'error', 'jobs': []}
    
    def trigger_traffic_stats_collection(self):
        """手动触发流量统计收集"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return False
                
            with self.app.app_context():
                logger.info("手动触发流量统计收集...")
                return self.traffic_stats_service.collect_all_traffic_stats()
                
        except Exception as e:
            logger.error(f"手动触发流量统计收集失败: {e}")
            return False
    
    def trigger_expired_subscriptions_cleanup(self):
        """手动触发到期订阅清理"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return {'success': False, 'error': 'Flask应用未初始化'}
                
            with self.app.app_context():
                logger.info("手动触发到期订阅清理...")
                stats = self.expiration_service.process_expired_subscriptions()
                return {'success': True, 'stats': stats}
                
        except Exception as e:
            logger.error(f"手动触发到期订阅清理失败: {e}")
            return {'success': False, 'error': str(e)}

    def trigger_traffic_exhausted_cleanup(self):
        """手动触发流量耗尽订阅清理"""
        try:
            if not self.app:
                logger.error("Flask应用未初始化")
                return {'success': False, 'error': 'Flask应用未初始化'}

            with self.app.app_context():
                logger.info("手动触发流量耗尽订阅清理...")
                stats = self.expiration_service.process_traffic_exhausted_subscriptions()
                return {'success': True, 'stats': stats}

        except Exception as e:
            logger.error(f"手动触发流量耗尽订阅清理失败: {e}")
            return {'success': False, 'error': str(e)}

# 全局调度器实例
scheduler_service = SchedulerService()
