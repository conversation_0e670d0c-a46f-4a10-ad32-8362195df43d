"""
订阅流量基准模型 - 用于记录已删除面板的历史流量累积
"""
from datetime import datetime
from . import db


class SubscriptionTrafficBaseline(db.Model):
    """订阅流量基准模型 - 记录历史累积流量，解决面板删除导致的流量丢失问题"""
    __tablename__ = 'subscription_traffic_baselines'

    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id', ondelete='CASCADE'), 
                               nullable=False, unique=True, index=True)
    
    # 历史累积流量基准（字节）- 来自已删除面板的流量贡献
    baseline_upload_bytes = db.Column(db.BigInteger, nullable=False, default=0)
    baseline_download_bytes = db.Column(db.BigInteger, nullable=False, default=0)
    baseline_total_bytes = db.Column(db.BigInteger, nullable=False, default=0)
    
    # 软删除支持（可选）
    is_deleted = db.Column(db.<PERSON>, nullable=False, default=False)
    deleted_at = db.Column(db.DateTime, nullable=True)

    # 元数据
    last_updated = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)

    # 关联关系 - 修复级联删除配置
    subscription = db.relationship('Subscription',
                                 backref=db.backref('traffic_baseline',
                                                   cascade='all, delete-orphan',
                                                   uselist=False),
                                 lazy=True)
    
    def __repr__(self):
        return f'<SubscriptionTrafficBaseline subscription_id={self.subscription_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'subscription_id': self.subscription_id,
            'baseline_upload_bytes': self.baseline_upload_bytes,
            'baseline_download_bytes': self.baseline_download_bytes,
            'baseline_total_bytes': self.baseline_total_bytes,
            'baseline_upload_mb': round(self.baseline_upload_bytes / (1024**2), 2),
            'baseline_download_mb': round(self.baseline_download_bytes / (1024**2), 2),
            'baseline_total_mb': round(self.baseline_total_bytes / (1024**2), 2),
            'last_updated': self.last_updated.isoformat() if self.last_updated else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def add_baseline_traffic(self, upload_bytes: int, download_bytes: int, total_bytes: int):
        """累加基准流量"""
        # 确保字段不为None，如果为None则初始化为0
        if self.baseline_upload_bytes is None:
            self.baseline_upload_bytes = 0
        if self.baseline_download_bytes is None:
            self.baseline_download_bytes = 0
        if self.baseline_total_bytes is None:
            self.baseline_total_bytes = 0

        self.baseline_upload_bytes += upload_bytes
        self.baseline_download_bytes += download_bytes
        self.baseline_total_bytes += total_bytes
        self.last_updated = datetime.utcnow()
    
    def soft_delete(self):
        """软删除基准记录"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()

    def restore(self):
        """恢复软删除的基准记录"""
        self.is_deleted = False
        self.deleted_at = None
        self.last_updated = datetime.utcnow()

    @classmethod
    def get_or_create_baseline(cls, subscription_id: int, include_deleted: bool = False):
        """获取或创建订阅的流量基准记录"""
        query = cls.query.filter_by(subscription_id=subscription_id)
        if not include_deleted:
            query = query.filter_by(is_deleted=False)

        baseline = query.first()
        if not baseline:
            baseline = cls(
                subscription_id=subscription_id,
                baseline_upload_bytes=0,
                baseline_download_bytes=0,
                baseline_total_bytes=0
            )
            db.session.add(baseline)
        return baseline

    @classmethod
    def get_active_baseline(cls, subscription_id: int):
        """获取活跃的（未删除的）基准记录"""
        return cls.query.filter_by(
            subscription_id=subscription_id,
            is_deleted=False
        ).first()
