"""
商店购买路由
"""
from flask import Blueprint, request, render_template, redirect, url_for, flash, session
from models import db, Product, Order, User, Coupon
from utils.order_service import OrderService
import logging
import uuid
import time

logger = logging.getLogger(__name__)

shop_bp = Blueprint('shop', __name__)

def get_order_service():
    """获取OrderService实例，确保在Flask应用上下文中创建"""
    return OrderService()

@shop_bp.route('/')
def shop_index():
    """商店首页"""
    try:
        products = Product.query.filter_by(is_active=True).all()
        return render_template('shop/index.html', products=products)
    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        flash('获取产品列表失败', 'error')
        return render_template('shop/index.html', products=[])

@shop_bp.route('/buy/<int:product_id>')
def buy_product(product_id):
    """购买产品页面"""
    try:
        product = Product.query.get_or_404(product_id)

        if not product.is_active:
            flash('产品已下架', 'error')
            return redirect(url_for('shop.shop_index'))

        # 检查库存
        if product.stock_count == 0:
            flash('产品库存不足', 'error')
            return redirect(url_for('shop.shop_index'))

        return render_template('shop/buy.html', product=product)
    except Exception as e:
        logger.error(f"获取产品详情失败: {e}")
        flash('获取产品详情失败', 'error')
        return redirect(url_for('shop.shop_index'))

@shop_bp.route('/purchase', methods=['POST'])
def purchase_product():
    """购买产品"""
    try:
        # 验证必需字段
        customer_email = request.form.get('customer_email')
        product_id = request.form.get('product_id')

        if not customer_email:
            flash('请填写邮箱地址', 'error')
            return redirect(url_for('shop.buy_product', product_id=product_id))

        customer_name = request.form.get('customer_name', '')
        payment_method = request.form.get('payment_method', 'manual')
        customer_remarks = request.form.get('customer_remarks', '')
        test_mode = request.form.get('test_mode') == 'on'  # 检测测试模式
        coupon_code = request.form.get('coupon_code', '').strip()

        # 从产品获取配置
        if product_id:
            product = Product.query.get(int(product_id))
            if not product:
                flash('产品不存在', 'error')
                return redirect(url_for('shop.shop_index'))

            if not product.is_active:
                flash('产品已下架', 'error')
                return redirect(url_for('shop.shop_index'))

            # 检查库存
            if product.stock_count == 0:
                flash('产品库存不足', 'error')
                return redirect(url_for('shop.shop_index'))

            # 从产品获取配置
            node_type = product.node_type.value
            duration_days = product.duration_days
            traffic_limit_gb = product.traffic_limit_gb
            original_price = product.price # Use original_price for calculations
            final_price = original_price
            applied_coupon_code = None
            discount_amount = 0.0

            # Coupon Validation and Price Adjustment
            if coupon_code:
                coupon = Coupon.query.filter_by(code=coupon_code).first()
                if coupon and coupon.is_available():
                    discount_percentage = coupon.discount_percentage
                    discount_amount = original_price * (discount_percentage / 100.0)
                    discount_amount = round(discount_amount, 2)
                    final_price = original_price - discount_amount
                    final_price = round(final_price, 2)
                    if final_price < 0: # Ensure price doesn't go negative
                        final_price = 0.0
                    applied_coupon_code = coupon.code

                    # 显示使用次数信息
                    remaining = coupon.get_remaining_uses()
                    if remaining is not None:
                        flash(f"优惠码 '{coupon.code}' 已应用，优惠 {discount_amount} 元！剩余使用次数：{remaining-1}", "success")
                    else:
                        flash(f"优惠码 '{coupon.code}' 已应用，优惠 {discount_amount} 元!", "success")
                else:
                    if coupon:
                        # 优惠券存在但不可用（可能是使用次数已用完）
                        flash(f"优惠码 '{coupon_code}' 已达到使用上限或已停用。", "error")
                    else:
                        flash(f"优惠码 '{coupon_code}' 不存在。", "error")
                    # It's important to redirect back to the buy page if coupon is invalid
                    return redirect(url_for('shop.buy_product', product_id=product_id))

            price = final_price # This will be the price passed to create_order

            # 测试模式下强制免费和测试支付方式
            # This should override any coupon calculations if test_mode is active
            if test_mode:
                price = 0
                discount_amount = original_price # In test mode, discount is total original price
                # 保留真实的优惠券代码，但添加测试模式标记
                if not applied_coupon_code:
                    applied_coupon_code = "TESTMODE" # Indicate test mode activation when no coupon
                payment_method = 'test'
                logger.info(f"测试模式：产品 {product_id} 价格设为免费，支付方式设为测试，优惠券代码: {applied_coupon_code}")
        else:
            flash('产品ID缺失', 'error')
            return redirect(url_for('shop.shop_index'))
        
        # 检查用户是否已登录
        user_id = session.get('user_id')
        if user_id:
            # 验证用户是否存在
            user = User.query.get(user_id)
            if user:
                logger.info(f"已登录用户 {user.username} (ID: {user_id}) 购买产品 {product_id}")
            else:
                # 用户不存在，清除session
                session.pop('user_id', None)
                user_id = None

        # 生成支付ID
        payment_id = f"PAY{int(time.time())}{uuid.uuid4().hex[:6].upper()}"

        # 创建订单
        order_service = get_order_service()
        success, order = order_service.create_order(
            customer_email=customer_email,
            customer_name=customer_name,
            node_type=node_type,
            duration_days=duration_days,
            traffic_limit_gb=traffic_limit_gb,
            price=price, # This is now final_price (or 0 if test_mode)
            payment_method=payment_method,
            payment_id=payment_id,
            applied_coupon_code=applied_coupon_code,
            discount_amount=discount_amount,
            customer_remarks=customer_remarks,
            test_mode=test_mode,
            user_id=user_id  # 传递用户ID
        )

        if not success:
            flash('订单创建失败', 'error')
            return redirect(url_for('shop.buy_product', product_id=product_id))

        # 如果是产品订单，关联产品并减少库存
        if product_id and product:
            order.product_id = int(product_id)
            if product.stock_count > 0:
                product.stock_count -= 1
            product.sold_count += 1
            db.session.commit()

        # 自动处理订单（如果是免费或测试订单）
        # The condition `price == 0` correctly handles test_mode or a 100% coupon
        if price == 0 or payment_method == 'test':
            process_success, message = order_service.process_order(order.order_id)
            if process_success:
                if test_mode: # Check test_mode specifically for flash message
                    flash('测试模式：节点分配成功！', 'success')
                # Check if a coupon was applied for non-test free orders
                elif applied_coupon_code and original_price > 0 : # original_price check for 100% discount case
                    flash(f"优惠码 '{applied_coupon_code}' 应用成功，订单已处理！节点配置已发送到您的邮箱。", "success")
                else: # Standard free order (e.g. product price was 0 initially)
                    flash('购买成功，节点配置已发送到您的邮箱', 'success')
                return render_template('shop/success.html', order=order)
            else:
                flash(f'订单创建成功但处理失败: {message}', 'error')
                return render_template('shop/order_status.html', order=order)
        else:
            # 需要支付的订单
            flash('订单创建成功，请完成支付', 'info')
            return render_template('shop/payment.html', order=order, payment_id=payment_id)

    except Exception as e:
        logger.error(f"购买产品失败: {e}")
        db.session.rollback()
        flash('购买失败', 'error')
        return redirect(url_for('shop.shop_index'))

@shop_bp.route('/orders/<order_id>')
def order_status(order_id):
    """查询订单状态页面"""
    try:
        order = Order.query.filter_by(order_id=order_id).first()
        if not order:
            flash('订单不存在', 'error')
            return redirect(url_for('shop.shop_index'))

        return render_template('shop/order_status.html', order=order)
    except Exception as e:
        logger.error(f"查询订单状态失败: {e}")
        flash('查询订单状态失败', 'error')
        return redirect(url_for('shop.shop_index'))

@shop_bp.route('/my-orders', methods=['GET', 'POST'])
def my_orders():
    """我的订单页面"""
    if request.method == 'GET':
        return render_template('shop/my_orders.html')

    try:
        email = request.form.get('email')
        if not email:
            flash('请输入邮箱地址', 'error')
            return render_template('shop/my_orders.html')

        orders = Order.query.filter_by(customer_email=email).order_by(Order.created_at.desc()).all()
        return render_template('shop/my_orders.html', orders=orders, email=email)
    except Exception as e:
        logger.error(f"根据邮箱查询订单失败: {e}")
        flash('查询订单失败', 'error')
        return render_template('shop/my_orders.html')
