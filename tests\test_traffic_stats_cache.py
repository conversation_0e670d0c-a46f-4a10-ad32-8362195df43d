import unittest
from unittest.mock import patch, MagicMock, call
from datetime import datetime, timedelta

# Assuming models and services are accessible, adjust path if necessary
# from app import db # If using Flask-SQLAlchemy's db for session mocking
from models import User, Subscription, TrafficStats, XUIPanelGroup, Order, OrderStatus, Product, XUIPanel, GroupMembership
from services.traffic_stats_service import TrafficStatsService
from multi_xui_manager import MultiXUIManager, LoadBalanceStrategy # Added LoadBalanceStrategy
from xui_client import XUIClient
from services.config_service import config_service # For mocking panel configs

# Mock db object if needed for db.session.add, commit, etc.
# This is a simplified mock; a more robust one might be needed depending on interactions.
class MockDbSession:
    def __init__(self):
        self.added_objects = []
        self.committed = False
        self.rollbacked = False
    def add(self, obj):
        self.added_objects.append(obj)
    def commit(self):
        self.committed = True
    def rollback(self):
        self.rollbacked = True
    def query(self, model): # Very basic query mock
        return MagicMock()

mock_db_session = MockDbSession()

class TestTrafficStatsCaching(unittest.TestCase):

    def setUp(self):
        # Reset mock states or perform common setup
        mock_db_session.added_objects = []
        mock_db_session.committed = False
        mock_db_session.rollbacked = False

        # Mock config_service for MultiXUIManager panel loading
        self.mock_panels_config = {
            'panel_A': {
                'base_url': 'http://panelA.com', 'username': 'admin', 'password': 'password',
                'path_prefix': '/', 'name': 'Panel A', 'region': 'US',
                'max_clients': 100, 'priority': 1, 'panel_id': 1
            },
            'panel_B': {
                'base_url': 'http://panelB.com', 'username': 'admin', 'password': 'password',
                'path_prefix': '/', 'name': 'Panel B', 'region': 'EU',
                'max_clients': 100, 'priority': 1, 'panel_id': 2
            }
        }
        self.config_service_patcher = patch('multi_xui_manager.config_service')
        self.mock_config_service = self.config_service_patcher.start()
        self.mock_config_service.get_xui_panels.return_value = self.mock_panels_config
        self.mock_config_service.get_load_balance_strategy.return_value = LoadBalanceStrategy.ROUND_ROBIN.value
        self.mock_config_service.get_auto_failover.return_value = True


    def tearDown(self):
        self.config_service_patcher.stop()
        patch.stopall() # Stops all patches started with patch.start()

    def _create_mock_panel(self, panel_id, name, base_url='http://test.com'):
        panel = MagicMock(spec=XUIPanel)
        panel.id = panel_id
        panel.name = name
        panel.base_url = base_url
        panel.path_prefix = '/'
        panel.username = 'admin'
        panel.password = 'admin'
        panel.status = MagicMock(value='active') # Mocking Enum .value
        panel.max_clients = 1000
        panel.priority = 1
        return panel

    def _create_mock_group(self, group_id, name, panels_list):
        group = MagicMock(spec=XUIPanelGroup)
        group.id = group_id
        group.name = name
        group.memberships = []
        for panel_mock in panels_list:
            membership = MagicMock(spec=GroupMembership)
            membership.panel = panel_mock
            group.memberships.append(membership)
        return group

    def _create_mock_product(self, product_id, group_id):
        product = MagicMock(spec=Product)
        product.id = product_id
        product.target_group_id = group_id
        return product

    def _create_mock_order(self, order_id, user_id, product_mock, client_email="<EMAIL>", customer_remarks=None):
        order = MagicMock(spec=Order)
        order.order_id = order_id # important for generated client email
        order.user_id = user_id
        order.product = product_mock
        order.product_id = product_mock.id
        order.status = OrderStatus.COMPLETED
        order.customer_email = client_email # used if customer_remarks is None
        order.customer_remarks = customer_remarks # often used as the client email in XUI
        order.node_configs = [] # Assuming simple case without specific node_configs for now
        return order

    def _create_mock_subscription(self, sub_id, order_mock, group_id_mock):
        sub = MagicMock(spec=Subscription)
        sub.id = sub_id
        sub.order = order_mock
        sub.user_id = order_mock.user_id
        sub.group_id = group_id_mock
        sub.is_active = True
        sub.expires_at = datetime.utcnow() + timedelta(days=30)
        return sub

    @patch('services.traffic_stats_service.db', new_callable=lambda: MagicMock(session=mock_db_session))
    @patch('multi_xui_manager.XUIClient') # Patch XUIClient where it's imported in multi_xui_manager
    def test_collect_all_traffic_stats_caches_panel_data(self, MockXUIClient, mock_db_module):
        # Setup Mocks for XUIClient
        mock_xui_instance = MockXUIClient.return_value
        mock_xui_instance.login.return_value = True

        # Define what get_all_client_traffic returns for panel_A
        panel_A_client_data = [
            {'email': '<EMAIL>', 'up': 100, 'down': 200, 'total': 0, 'enable':True, 'expiryTime':0}, # total here is limit
            {'email': '<EMAIL>', 'up': 150, 'down': 250, 'total': 0, 'enable':True, 'expiryTime':0}
        ]
        # We need to ensure this mock is associated with the panel_A client
        # This is tricky because XUIClient is instantiated inside MultiXUIManager
        # For now, we'll make the mock general. A more robust way is to make side_effect conditional on base_url
        mock_xui_instance.get_all_client_traffic.return_value = panel_A_client_data

        # Mock Models
        panel_A_mock = self._create_mock_panel(panel_id=1, name='PanelA_Test', base_url='http://panelA.com')
        group_1_mock = self._create_mock_group(group_id=10, name='Group1_Test', panels_list=[panel_A_mock])
        product_1_mock = self._create_mock_product(product_id=100, group_id=group_1_mock.id)

        # User 1 on Panel A (via remarks)
        order1_remarks = "<EMAIL>"
        order1 = self._create_mock_order(order_id="O1", user_id=1, product_mock=product_1_mock, customer_remarks=order1_remarks)
        sub1 = self._create_mock_subscription(sub_id=1001, order_mock=order1, group_id_mock=group_1_mock.id)

        # User 2 on Panel A (via remarks)
        order2_remarks = "<EMAIL>"
        order2 = self._create_mock_order(order_id="O2", user_id=2, product_mock=product_1_mock, customer_remarks=order2_remarks)
        sub2 = self._create_mock_subscription(sub_id=1002, order_mock=order2, group_id_mock=group_1_mock.id)

        active_subscriptions = [sub1, sub2]

        # Patch database queries for subscriptions
        # Ensure Subscription.query correctly returns the mock that has the chainable methods.
        mock_subscription_query = MagicMock()
        mock_subscription_query.filter.return_value.join.return_value.filter.return_value.all.return_value = active_subscriptions

        # Patch models.Subscription directly if Subscription.query is a class attribute
        # Or patch where db.session.query(Subscription) is called if that's the pattern.
        # The current code uses Subscription.query.filter(...)
        services_traffic_stats_subscription_patcher = patch('services.traffic_stats_service.Subscription', MagicMock(query=mock_subscription_query))
        mock_Subscription_in_service = services_traffic_stats_subscription_patcher.start()


        # Patch XUIPanelGroup.query.get for MultiXUIManager.from_group
        # This needs to be active when MultiXUIManager.from_group is called.
        models_xuipanelgroup_query_patcher = patch('models.XUIPanelGroup.query')
        mock_group_query_in_models = models_xuipanelgroup_query_patcher.start()
        mock_group_query_in_models.get.return_value = group_1_mock

        # Also need to patch XUIPanel.query if MultiXUIManager directly queries panels (it does during group loading)
        models_xuipanel_query_patcher = patch('models.XUIPanel.query')
        mock_panel_query_in_models = models_xuipanel_query_patcher.start()
        # Assuming the group loading iterates through memberships and panel is already mocked.

        traffic_service = TrafficStatsService()
        traffic_service.collect_all_traffic_stats()

        # Assertions
        self.assertEqual(mock_xui_instance.get_all_client_traffic.call_count, 1,
                         "XUIClient.get_all_client_traffic should be called once per panel.")

        # Assert that TrafficStats were added
        self.assertEqual(len(mock_db_session.added_objects), 2)
        # Ensure added_objects are TrafficStats instances if other objects could be added
        added_stats = [obj for obj in mock_db_session.added_objects if isinstance(obj, MagicMock) and obj._spec_class == TrafficStats]
        self.assertEqual(len(added_stats), 2)


        stat1 = next(s for s in added_stats if s.subscription_id == sub1.id)
        stat2 = next(s for s in added_stats if s.subscription_id == sub2.id)

        self.assertEqual(stat1.upload_bytes, 100)
        self.assertEqual(stat1.download_bytes, 200)
        self.assertEqual(stat2.upload_bytes, 150)
        self.assertEqual(stat2.download_bytes, 250)

        services_traffic_stats_subscription_patcher.stop()
        models_xuipanelgroup_query_patcher.stop()
        models_xuipanel_query_patcher.stop()


    @patch('multi_xui_manager.XUIClient')
    def test_multi_xui_manager_get_client_traffic_summary_caches_data(self, MockXUIClient):
        mock_xui_panel_A_client = MockXUIClient.return_value
        mock_xui_panel_A_client.login.return_value = True

        panel_A_clients_data = [
            {'email': '<EMAIL>', 'up': 10, 'down': 20, 'total': 0, 'enable':True, 'expiryTime':0, 'inbound_id':1, 'inbound_port':1000, 'inbound_protocol':'vless'},
            {'email': '<EMAIL>', 'up': 30, 'down': 40, 'total': 0, 'enable':True, 'expiryTime':0, 'inbound_id':1, 'inbound_port':1000, 'inbound_protocol':'vless'}
        ]
        mock_xui_panel_A_client.get_all_client_traffic.return_value = panel_A_clients_data

        self.mock_config_service.get_xui_panels.return_value = {
            k: v for k, v in self.mock_panels_config.items() if k == 'panel_A'
        }

        panel_cache = {}
        manager = MultiXUIManager(panel_cache=panel_cache, group_id=None)

        with patch.object(manager, 'check_panel_health', return_value=True):
            summary1 = manager.get_client_traffic_summary('<EMAIL>')
            summary2 = manager.get_client_traffic_summary('<EMAIL>')

        mock_xui_panel_A_client.get_all_client_traffic.assert_called_once()

        self.assertIsNotNone(summary1)
        self.assertEqual(summary1['total_up'], 10)
        self.assertEqual(summary1['total_down'], 20)

        self.assertIsNotNone(summary2)
        self.assertEqual(summary2['total_up'], 30)
        self.assertEqual(summary2['total_down'], 40)

        self.assertIn('panel_A', panel_cache)
        self.assertEqual(panel_cache['panel_A'], panel_A_clients_data)

if __name__ == '__main__':
    unittest.main()
