{% extends "base.html" %}

{% block title %}订阅详情 - {{ subscription.product_name }} - 节点商城{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">
                        <i class="bi bi-{{ 'check-circle text-success' if subscription.is_active else 'x-circle text-danger' }} me-2"></i>
                        {{ subscription.product_name }}
                    </h2>
                    <p class="text-muted mb-0">订单号：{{ subscription.order_id }}</p>
                </div>
                <div>
                    <a href="{{ url_for('user.subscriptions') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-1"></i> 返回列表
                    </a>
                    <!-- 移除流量刷新按钮 - 现在流量数据直接从数据库读取 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 状态卡片 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    {% if subscription.is_expired %}
                                        <div class="bg-danger text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                            <i class="bi bi-x-circle" style="font-size: 1.5rem;"></i>
                                        </div>
                                    {% elif subscription.is_active %}
                                        <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                            <i class="bi bi-check-circle" style="font-size: 1.5rem;"></i>
                                        </div>
                                    {% else %}
                                        <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                            <i class="bi bi-pause-circle" style="font-size: 1.5rem;"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <div>
                                    <h5 class="mb-1 fw-bold">
                                        {% if subscription.is_expired %}
                                            订阅已过期
                                        {% elif subscription.is_active %}
                                            订阅正常运行
                                        {% else %}
                                            订阅已停用
                                        {% endif %}
                                    </h5>
                                    <p class="text-muted mb-0">
                                        {% if subscription.expires_at %}
                                            到期时间：{{ subscription.expires_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            永久有效
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 text-md-end">
                            {% if subscription.is_expired %}
                                <span class="badge bg-danger px-4 py-3 fs-6">已过期</span>
                            {% elif subscription.is_active %}
                                <span class="badge bg-success px-4 py-3 fs-6">活跃中</span>
                            {% else %}
                                <span class="badge bg-secondary px-4 py-3 fs-6">已停用</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 基本信息 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-info-circle text-primary me-2"></i>
                        基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="border-start border-primary border-3 ps-3">
                                <small class="text-muted">协议类型</small>
                                <div class="fw-bold">{{ subscription.node_type.upper() }}</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border-start border-success border-3 ps-3">
                                <small class="text-muted">有效期</small>
                                <div class="fw-bold">{{ subscription.duration_days }} 天</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border-start border-info border-3 ps-3">
                                <small class="text-muted">节点数量</small>
                                <div class="fw-bold">{{ subscription.node_count }} 个</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border-start border-warning border-3 ps-3">
                                <small class="text-muted">购买价格</small>
                                <div class="fw-bold text-primary">¥{{ subscription.price }}</div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="border-start border-secondary border-3 ps-3">
                                <small class="text-muted">购买时间</small>
                                <div class="fw-bold">{{ subscription.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流量统计 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-graph-up text-success me-2"></i>
                        流量统计
                    </h5>
                </div>
                <div class="card-body">
                    <!-- 流量进度条 -->
                    <div class="mb-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="fw-semibold">流量使用情况</span>
                            <span class="fw-bold text-primary">{{ subscription.traffic_stats.usage_percentage }}%</span>
                        </div>
                        <div class="progress" style="height: 12px;">
                            <div class="progress-bar 
                                {% if subscription.traffic_stats.usage_percentage >= 90 %}bg-danger
                                {% elif subscription.traffic_stats.usage_percentage >= 70 %}bg-warning
                                {% else %}bg-success{% endif %}" 
                                 role="progressbar" 
                                 style="width: {{ subscription.traffic_stats.usage_percentage }}%"
                                 aria-valuenow="{{ subscription.traffic_stats.usage_percentage }}" 
                                 aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted">已用 {{ subscription.traffic_stats.total_traffic_mb }}MB</small>
                            <small class="text-muted">总计 {{ subscription.traffic_stats.traffic_limit_mb }}MB</small>
                        </div>
                    </div>

                    <!-- 详细统计 -->
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="fw-bold text-success fs-5">{{ subscription.traffic_stats.total_up_mb }}MB</div>
                                <small class="text-muted">上传流量</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-3 bg-light rounded">
                                <div class="fw-bold text-info fs-5">{{ subscription.traffic_stats.total_down_mb }}MB</div>
                                <small class="text-muted">下载流量</small>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="text-center p-3 bg-primary bg-opacity-10 rounded">
                                <div class="fw-bold text-primary fs-4">{{ subscription.traffic_stats.remaining_mb }}MB</div>
                                <small class="text-muted">剩余流量</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 节点配置 -->
    {% if subscription.configs %}
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-server text-info me-2"></i>
                        节点配置 ({{ subscription.configs|length }})
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 px-4 py-3">节点信息</th>
                                    <th class="border-0 px-4 py-3">服务器地址</th>
                                    <th class="border-0 px-4 py-3">端口/协议</th>
                                    <th class="border-0 px-4 py-3">状态</th>
                                    <th class="border-0 px-4 py-3">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for config in subscription.configs %}
                                <tr>
                                    <td class="px-4 py-3">
                                        <div>
                                            <div class="fw-semibold">节点 #{{ loop.index }}</div>
                                            <small class="text-muted">{{ config.client_email }}</small>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <code class="bg-light px-2 py-1 rounded">{{ config.server_address }}</code>
                                    </td>
                                    <td class="px-4 py-3">
                                        <div>
                                            <span class="badge bg-primary">{{ config.server_port }}</span>
                                            <span class="badge bg-secondary ms-1">{{ config.protocol.upper() }}</span>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        <span class="badge bg-{{ 'success' if config.is_active else 'secondary' }}">
                                            {{ '活跃' if config.is_active else '停用' }}
                                        </span>
                                    </td>
                                    <td class="px-4 py-3">
                                        {% if config.vless_config %}
                                        <button class="btn btn-sm btn-outline-primary" 
                                                onclick="copyConfig('{{ config.vless_config }}')">
                                            <i class="bi bi-clipboard me-1"></i> 复制配置
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // 复制配置
    function copyConfig(config) {
        navigator.clipboard.writeText(config).then(function() {
            showNotification('配置已复制到剪贴板', 'success');
        }, function(err) {
            showNotification('复制失败，请手动复制', 'error');
        });
    }
    
    // 移除流量刷新功能 - 现在流量数据直接从数据库读取，无需手动刷新
    
    // 显示通知
    function showNotification(message, type) {
        // 这里可以使用Bootstrap的Toast或其他通知组件
        alert(message);
    }
</script>
{% endblock %}
