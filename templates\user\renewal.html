{% extends "base.html" %}

{% block title %}订阅续费 - 用户中心{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-arrow-clockwise text-primary"></i>
                    订阅续费
                </h2>
                <a href="{{ url_for('user.dashboard') }}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> 返回用户中心
                </a>
            </div>

            <!-- 当前订阅信息 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> 当前订阅信息
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>订单号：</strong>{{ subscription.order.order_id }}</p>
                            <p><strong>套餐类型：</strong>{{ subscription.order.node_type.value.upper() }}</p>
                            <p><strong>流量限制：</strong>{{ subscription.order.traffic_limit_gb }}GB</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>原始价格：</strong>¥{{ "%.2f"|format(subscription.order.price) }}</p>
                            <p><strong>到期时间：</strong>
                                <span class="text-success">
                                    {{ subscription.expires_at.strftime('%Y-%m-%d %H:%M:%S') if subscription.expires_at else '永久' }}
                                </span>
                            </p>
                            <p><strong>订阅状态：</strong>
                                {% if subscription.is_active %}
                                    <span class="badge bg-success">活跃</span>
                                {% else %}
                                    <span class="badge bg-danger">已失效</span>
                                {% endif %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 续费选项 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-credit-card"></i> 选择续费套餐
                    </h5>
                </div>
                <div class="card-body">
                    {% if renewal_options %}
                        <div class="row">
                            {% for option in renewal_options %}
                                {% if option.is_active %}
                                <div class="col-md-4 mb-4">
                                    <div class="card border-primary h-100 renewal-option" data-duration="{{ option.duration_months }}">
                                        <div class="card-header bg-primary text-white text-center">
                                            <h6 class="mb-0">{{ option.duration_months }}个月续费</h6>
                                            {% if option.discount_percentage > 0 %}
                                                <small class="badge bg-warning text-dark">
                                                    {{ option.discount_percentage }}%折扣
                                                </small>
                                            {% endif %}
                                        </div>
                                        <div class="card-body text-center">
                                            <div class="mb-3">
                                                {% if option.discount_percentage > 0 %}
                                                    <div class="text-muted text-decoration-line-through">
                                                        原价：¥{{ "%.2f"|format(option.price_info.base_price) }}
                                                    </div>
                                                    <div class="h4 text-primary">
                                                        ¥{{ "%.2f"|format(option.price_info.final_price) }}
                                                    </div>
                                                    <div class="text-success">
                                                        节省：¥{{ "%.2f"|format(option.price_info.discount_amount) }}
                                                    </div>
                                                {% else %}
                                                    <div class="h4 text-primary">
                                                        ¥{{ "%.2f"|format(option.price_info.final_price) }}
                                                    </div>
                                                {% endif %}
                                            </div>
                                            
                                            <div class="mb-3">
                                                <small class="text-muted">
                                                    平均每月：¥{{ "%.2f"|format(option.price_info.final_price / option.duration_months) }}
                                                </small>
                                            </div>
                                            
                                            <button type="button" 
                                                    class="btn btn-primary btn-renewal"
                                                    data-duration="{{ option.duration_months }}"
                                                    data-price="{{ option.price_info.final_price }}"
                                                    data-discount="{{ option.discount_percentage }}">
                                                选择续费
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            暂时没有可用的续费选项，请联系客服。
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 确认续费模态框 -->
<div class="modal fade" id="renewalConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认续费</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    请确认您的续费信息：
                </div>
                
                <div class="row">
                    <div class="col-6"><strong>续费时长：</strong></div>
                    <div class="col-6" id="confirm-duration"></div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>续费价格：</strong></div>
                    <div class="col-6" id="confirm-price"></div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>折扣优惠：</strong></div>
                    <div class="col-6" id="confirm-discount"></div>
                </div>
                <div class="row">
                    <div class="col-6"><strong>新到期时间：</strong></div>
                    <div class="col-6" id="confirm-new-expiry"></div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>注意：</strong>续费后将立即生效，订阅到期时间将延长相应月数。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmRenewalBtn">
                    <i class="bi bi-credit-card"></i> 确认续费
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 支付处理模态框 -->
<div class="modal fade" id="paymentModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">处理中...</span>
                </div>
                <h5>正在处理续费...</h5>
                <p class="text-muted">请稍候，正在为您处理续费订单</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let selectedDuration = null;
let selectedPrice = null;
let selectedDiscount = null;

document.addEventListener('DOMContentLoaded', function() {
    // 续费按钮点击事件
    document.querySelectorAll('.btn-renewal').forEach(btn => {
        btn.addEventListener('click', function() {
            selectedDuration = parseInt(this.dataset.duration);
            selectedPrice = parseFloat(this.dataset.price);
            selectedDiscount = parseFloat(this.dataset.discount);
            
            showRenewalConfirm();
        });
    });
    
    // 确认续费按钮
    document.getElementById('confirmRenewalBtn').addEventListener('click', function() {
        processRenewal();
    });
});

function showRenewalConfirm() {
    // 填充确认信息
    document.getElementById('confirm-duration').textContent = selectedDuration + '个月';
    document.getElementById('confirm-price').textContent = '¥' + selectedPrice.toFixed(2);
    document.getElementById('confirm-discount').textContent = selectedDiscount > 0 ? selectedDiscount + '%折扣' : '无折扣';
    
    // 计算新的到期时间
    const currentExpiry = new Date('{{ subscription.expires_at.strftime("%Y-%m-%dT%H:%M:%S") if subscription.expires_at else "" }}');
    const newExpiry = new Date(currentExpiry.getTime() + selectedDuration * 30 * 24 * 60 * 60 * 1000);
    document.getElementById('confirm-new-expiry').textContent = newExpiry.toLocaleString('zh-CN');
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('renewalConfirmModal'));
    modal.show();
}

function processRenewal() {
    // 隐藏确认模态框
    const confirmModal = bootstrap.Modal.getInstance(document.getElementById('renewalConfirmModal'));
    confirmModal.hide();
    
    // 显示支付处理模态框
    const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
    paymentModal.show();
    
    // 创建续费订单
    fetch('/renewal/api/renewal/create-order', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            duration_months: selectedDuration
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 订单创建成功，处理支付
            return processPayment(data.order.order_id);
        } else {
            throw new Error(data.message || '创建订单失败');
        }
    })
    .catch(error => {
        paymentModal.hide();
        showAlert('danger', error.message || '续费失败');
    });
}

function processPayment(orderId) {
    // 处理支付
    return fetch('/renewal/api/renewal/process-payment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            order_id: orderId
        })
    })
    .then(response => response.json())
    .then(data => {
        const paymentModal = bootstrap.Modal.getInstance(document.getElementById('paymentModal'));
        paymentModal.hide();
        
        if (data.success) {
            // 续费成功
            showAlert('success', data.message || '续费成功！');
            
            // 跳转到成功页面
            setTimeout(() => {
                window.location.href = '/renewal/renewal/success?order_id=' + orderId;
            }, 2000);
        } else {
            throw new Error(data.message || '支付失败');
        }
    });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);
    
    // 自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
