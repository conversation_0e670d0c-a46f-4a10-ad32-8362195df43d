{% extends "base.html" %}

{% block title %}首页 - 节点商城{% endblock %}

{% block content %}
<!-- 英雄区域 -->
<section class="bg-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <h1 class="display-4 fw-bold">高速稳定的网络节点</h1>
                <p class="lead">提供全球优质节点服务，让您畅享无界网络体验</p>
                <a href="{{ url_for('shop') }}" class="btn btn-light btn-lg">
                    <i class="bi bi-cart"></i> 立即购买
                </a>
            </div>
            <div class="col-lg-6">
                <div class="text-center">
                    <i class="bi bi-globe" style="font-size: 8rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 特性介绍 -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col">
                <h2 class="fw-bold">为什么选择我们？</h2>
                <p class="text-muted">专业的技术团队，为您提供最优质的服务</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="bi bi-lightning-charge text-warning" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">高速稳定</h5>
                        <p class="card-text text-muted">采用优质线路，保证网络速度和稳定性</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="bi bi-shield-check text-success" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">安全可靠</h5>
                        <p class="card-text text-muted">采用先进加密技术，保护您的隐私安全</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body text-center">
                        <i class="bi bi-headset text-info" style="font-size: 3rem;"></i>
                        <h5 class="card-title mt-3">24/7支持</h5>
                        <p class="card-text text-muted">专业客服团队，随时为您解决问题</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 热门套餐 -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col">
                <h2 class="fw-bold">热门套餐</h2>
                <p class="text-muted">选择最适合您的套餐方案</p>
            </div>
        </div>
        <div class="row">
            {% for product in featured_products %}
            <div class="col-md-4 mb-4">
                <div class="card product-card h-100 border-0 shadow">
                    <div class="card-body text-center">
                        <h5 class="card-title">{{ product.name }}</h5>
                        <p class="card-text text-muted">{{ product.description }}</p>
                        <div class="price mb-3">
                            {% if product.price == 0 %}
                                <span class="text-success">免费</span>
                            {% else %}
                                ¥{{ "%.1f"|format(product.price) }}
                            {% endif %}
                        </div>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success"></i> {{ product.duration_days }}天有效期</li>
                            <li><i class="bi bi-check-circle text-success"></i> {{ product.traffic_limit_gb }}GB流量</li>
                            <li><i class="bi bi-check-circle text-success"></i> {{ product.node_type.value.upper() }}协议</li>
                        </ul>
                        <a href="{{ url_for('buy_product', product_id=product.id) }}" class="btn btn-primary">
                            <i class="bi bi-cart-plus"></i> 立即购买
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <a href="{{ url_for('shop') }}" class="btn btn-outline-primary">
                查看更多套餐 <i class="bi bi-arrow-right"></i>
            </a>
        </div>
    </div>
</section>

<!-- 使用说明 -->
<section class="py-5">
    <div class="container">
        <div class="row text-center mb-5">
            <div class="col">
                <h2 class="fw-bold">使用说明</h2>
                <p class="text-muted">简单三步，即可开始使用</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 text-center mb-4">
                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <span class="h3 mb-0">1</span>
                </div>
                <h5 class="mt-3">选择套餐</h5>
                <p class="text-muted">根据您的需求选择合适的套餐</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <span class="h3 mb-0">2</span>
                </div>
                <h5 class="mt-3">完成支付</h5>
                <p class="text-muted">支持多种支付方式，安全便捷</p>
            </div>
            <div class="col-md-4 text-center mb-4">
                <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <span class="h3 mb-0">3</span>
                </div>
                <h5 class="mt-3">开始使用</h5>
                <p class="text-muted">获取配置信息，下载客户端即可使用</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}
