"""
多X-UI面板管理器
支持负载均衡、故障转移和统一管理
"""
import logging
import random
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from services.config_service import config_service
from xui_client import XUIClient

logger = logging.getLogger(__name__)

class LoadBalanceStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    LEAST_CLIENTS = "least_clients"
    PRIORITY = "priority"
    RANDOM = "random"

@dataclass
class PanelStatus:
    """面板状态信息"""
    panel_id: str
    is_online: bool
    last_check: float
    client_count: int
    response_time: float
    error_count: int
    last_error: Optional[str] = None

class MultiXUIManager:
    """多X-UI面板管理器"""
    
    def __init__(self, group_id: Optional[int] = None, panel_cache: Optional[Dict] = None):
        # 如果指定了分组ID，则从数据库获取该分组的面板
        if group_id is not None:
            try:
                from models import db, XUIPanelGroup, XUIPanel
                group = XUIPanelGroup.query.get(group_id)
                if group:
                    self.panels = {}
                    for membership in group.memberships:
                        panel = membership.panel
                        if panel and panel.status.value == 'active':
                            panel_id = f"panel_{panel.id}"
                            self.panels[panel_id] = {
                                'base_url': panel.base_url,
                                'path_prefix': panel.path_prefix,
                                'username': panel.username,
                                'password': panel.password,
                                'name': panel.name,
                                'region': panel.region,
                                'max_clients': panel.max_clients,
                                'priority': panel.priority,
                                'panel_id': panel.id
                            }
                    logger.info(f"从分组 {group.name} (ID: {group_id}) 加载了 {len(self.panels)} 个面板")
                else:
                    logger.warning(f"分组ID {group_id} 不存在，使用默认面板配置")
                    self.panels = config_service.get_xui_panels()
            except Exception as e:
                logger.error(f"从分组加载面板失败: {e}")
                self.panels = config_service.get_xui_panels()
        else:
            # 否则使用配置服务获取所有面板
            self.panels = config_service.get_xui_panels()
        
        self.clients: Dict[str, XUIClient] = {}
        self.panel_status: Dict[str, PanelStatus] = {}
        self.current_panel_index = 0
        self.load_balance_strategy = LoadBalanceStrategy(config_service.get_load_balance_strategy())
        self.auto_failover = config_service.get_auto_failover()
        self.panel_data_cache = panel_cache if panel_cache is not None else {}

        # 初始化所有面板客户端
        self._initialize_clients()
    
    def _initialize_clients(self):
        """初始化所有面板的客户端"""
        for panel_id, panel_config in self.panels.items():
            try:
                client = XUIClient(
                    base_url=panel_config['base_url'],
                    username=panel_config['username'],
                    password=panel_config['password']
                )
                # 设置路径前缀
                client.path_prefix = panel_config['path_prefix']
                
                self.clients[panel_id] = client
                self.panel_status[panel_id] = PanelStatus(
                    panel_id=panel_id,
                    is_online=False,
                    last_check=0,
                    client_count=0,
                    response_time=0,
                    error_count=0
                )
                
                logger.info(f"初始化面板客户端: {panel_id} ({panel_config['name']})")
                
            except Exception as e:
                logger.error(f"初始化面板 {panel_id} 失败: {e}")
    
    @classmethod
    def from_group(cls, group_id: int, panel_cache: Optional[Dict] = None) -> 'MultiXUIManager':
        """从指定分组创建管理器实例"""
        return cls(group_id=group_id, panel_cache=panel_cache)

    def clear_panel_cache(self, panel_id: Optional[str] = None):
        """Clears the cache for a specific panel or all panels."""
        if panel_id:
            if panel_id in self.panel_data_cache:
                del self.panel_data_cache[panel_id]
                logger.info(f"Cache cleared for panel {panel_id}")
        else:
            self.panel_data_cache.clear()
            logger.info("Panel data cache cleared for all panels.")
    
    def check_panel_health(self, panel_id: str) -> bool:
        """检查指定面板的健康状态"""
        if panel_id not in self.clients:
            return False
        
        try:
            start_time = time.time()
            client = self.clients[panel_id]
            
            # 尝试登录测试连接
            success = client.login()
            response_time = time.time() - start_time
            
            status = self.panel_status[panel_id]
            status.last_check = time.time()
            status.response_time = response_time
            
            if success:
                status.is_online = True
                status.error_count = 0
                status.last_error = None
                logger.debug(f"面板 {panel_id} 健康检查通过，响应时间: {response_time:.2f}s")
            else:
                status.is_online = False
                status.error_count += 1
                status.last_error = "登录失败"
                logger.warning(f"面板 {panel_id} 健康检查失败: 登录失败")
            
            return success
            
        except Exception as e:
            status = self.panel_status[panel_id]
            status.is_online = False
            status.error_count += 1
            status.last_error = str(e)
            status.last_check = time.time()
            logger.error(f"面板 {panel_id} 健康检查异常: {e}")
            return False
    
    def check_all_panels_health(self) -> Dict[str, bool]:
        """检查所有面板的健康状态"""
        results = {}
        for panel_id in self.panels.keys():
            results[panel_id] = self.check_panel_health(panel_id)
        return results
    
    def get_available_panels(self) -> List[str]:
        """获取可用的面板列表"""
        available = []
        logger.info(f"检查 {len(self.panel_status)} 个面板的可用性")

        for panel_id, status in self.panel_status.items():
            logger.debug(f"面板 {panel_id}: 在线={status.is_online}, 上次检查={time.time() - status.last_check:.1f}秒前")

            if status.is_online or time.time() - status.last_check > 300:  # 5分钟重新检查
                if self.check_panel_health(panel_id):
                    available.append(panel_id)
                    logger.info(f"面板 {panel_id} 可用")
                else:
                    logger.warning(f"面板 {panel_id} 健康检查失败")
            else:
                logger.debug(f"面板 {panel_id} 跳过检查（最近检查过且离线）")

        logger.info(f"找到 {len(available)} 个可用面板: {available}")
        return available
    
    def select_panel(self, strategy: LoadBalanceStrategy = None) -> Optional[str]:
        """根据负载均衡策略选择面板"""
        if strategy is None:
            strategy = self.load_balance_strategy
        
        available_panels = self.get_available_panels()
        if not available_panels:
            logger.error("没有可用的X-UI面板")
            return None
        
        if strategy == LoadBalanceStrategy.ROUND_ROBIN:
            # 轮询策略
            panel_id = available_panels[self.current_panel_index % len(available_panels)]
            self.current_panel_index += 1
            return panel_id
        
        elif strategy == LoadBalanceStrategy.PRIORITY:
            # 优先级策略
            sorted_panels = sorted(available_panels, 
                                 key=lambda x: self.panels[x]['priority'])
            return sorted_panels[0]
        
        elif strategy == LoadBalanceStrategy.LEAST_CLIENTS:
            # 最少客户端策略
            sorted_panels = sorted(available_panels,
                                 key=lambda x: self.panel_status[x].client_count)
            return sorted_panels[0]
        
        elif strategy == LoadBalanceStrategy.RANDOM:
            # 随机策略
            return random.choice(available_panels)
        
        else:
            return available_panels[0]
    
    def add_client_to_any_panel(self, client_email: str, client_id: str = None,
                               traffic_limit_gb: int = 100, expiry_days: int = 30,
                               preferred_panel: str = None) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """在任意可用面板中添加客户端"""

        logger.info(f"开始添加客户端: {client_email}")
        logger.info(f"客户端ID: {client_id}")
        logger.info(f"流量限制: {traffic_limit_gb}GB")
        logger.info(f"有效期: {expiry_days}天")
        logger.info(f"首选面板: {preferred_panel}")

        # 如果指定了首选面板，先尝试使用
        if preferred_panel and preferred_panel in self.clients:
            logger.info(f"尝试使用首选面板: {preferred_panel}")
            if self.check_panel_health(preferred_panel):
                success, client_data = self._add_client_to_panel(
                    preferred_panel, client_email, client_id, traffic_limit_gb, expiry_days
                )
                if success:
                    logger.info(f"成功在首选面板 {preferred_panel} 中添加客户端")
                    return True, client_data, preferred_panel
                else:
                    logger.warning(f"在首选面板 {preferred_panel} 中添加客户端失败")
            else:
                logger.warning(f"首选面板 {preferred_panel} 健康检查失败")
        
        # 尝试其他可用面板
        max_retries = len(self.panels)
        for attempt in range(max_retries):
            panel_id = self.select_panel()
            if not panel_id:
                break
            
            success, client_data = self._add_client_to_panel(
                panel_id, client_email, client_id, traffic_limit_gb, expiry_days
            )
            
            if success:
                logger.info(f"成功在面板 {panel_id} 中添加客户端: {client_email}")
                return True, client_data, panel_id
            else:
                logger.warning(f"在面板 {panel_id} 中添加客户端失败，尝试下一个面板")
                # 标记面板为不可用
                self.panel_status[panel_id].is_online = False
                self.panel_status[panel_id].error_count += 1
        
        logger.error(f"在所有面板中添加客户端 {client_email} 都失败了")
        return False, None, None

    def get_all_client_traffic(self) -> Dict[str, List[Dict]]:
        """获取所有面板的客户端流量统计 (使用缓存)"""
        all_traffic_results = {} # Renamed to avoid conflict

        for panel_id, client_obj in self.clients.items(): # client renamed to client_obj
            cache_key = panel_id
            try:
                if cache_key in self.panel_data_cache:
                    cached_data = self.panel_data_cache[cache_key]
                    if cached_data is not None:
                        all_traffic_results[panel_id] = cached_data
                        logger.info(f"Cache hit for panel {panel_id} in get_all_client_traffic. Clients: {len(cached_data)}")
                        continue
                    else:
                        logger.debug(f"Cache contained None for panel {panel_id} in get_all_client_traffic. Will attempt to fetch.")

                if self.check_panel_health(panel_id):
                    logger.debug(f"Cache miss or None for panel {panel_id} in get_all_client_traffic. Fetching.")
                    fetched_data = client_obj.get_all_client_traffic() # Use client_obj
                    if fetched_data is not None:
                        self.panel_data_cache[cache_key] = fetched_data
                        all_traffic_results[panel_id] = fetched_data
                        logger.info(f"Fetched and cached all client traffic for panel {panel_id} in get_all_client_traffic. Clients: {len(fetched_data)}")
                    else:
                        self.panel_data_cache[cache_key] = None
                        logger.warning(f"Panel {panel_id} returned no data in get_all_client_traffic. Storing None in cache.")
                        all_traffic_results[panel_id] = []
                else:
                    logger.warning(f"Panel {panel_id} health check failed in get_all_client_traffic. Skipping.")
                    all_traffic_results[panel_id] = []
            except Exception as e:
                logger.error(f"Error processing panel {panel_id} in get_all_client_traffic: {e}", exc_info=True)
                all_traffic_results[panel_id] = []

        return all_traffic_results

    def get_client_traffic_summary(self, client_email: str) -> Optional[Dict]:
        """获取指定客户端在所有面板中的流量汇总"""
        total_up = 0
        total_down = 0
        total_traffic = 0
        panel_details = []

        for panel_id, client_obj in self.clients.items(): # client renamed to client_obj
            try:
                if not self.check_panel_health(panel_id):
                    logger.warning(f"Panel {panel_id} is not healthy, skipping for client {client_email}.")
                    continue

                panel_all_clients_traffic = None
                cache_key = panel_id

                if cache_key in self.panel_data_cache:
                    panel_all_clients_traffic = self.panel_data_cache[cache_key]
                    if panel_all_clients_traffic is not None:
                        logger.debug(f"Cache hit for panel {panel_id} (data found) for {client_email}")
                    else:
                        logger.debug(f"Cache hit for panel {panel_id} (None found) for {client_email}, will try fetching.")
                        # Fall through to fetch if cached as None

                if panel_all_clients_traffic is None: # Cache miss or cached as None
                    logger.debug(f"Cache miss or None for panel {panel_id} for {client_email}. Fetching all clients traffic.")
                    fetched_traffic_data = client_obj.get_all_client_traffic() # Use client_obj
                    if fetched_traffic_data is not None:
                        self.panel_data_cache[cache_key] = fetched_traffic_data
                        panel_all_clients_traffic = fetched_traffic_data
                        logger.info(f"Fetched and cached all client traffic for panel {panel_id}. Clients: {len(fetched_traffic_data)}")
                    else:
                        self.panel_data_cache[cache_key] = None
                        logger.warning(f"Failed to fetch all client traffic for panel {panel_id}. Storing None in cache.")

                if panel_all_clients_traffic: # If data was found in cache or fetched successfully
                    for traffic_data_item in panel_all_clients_traffic: # renamed traffic_data to traffic_data_item
                        if traffic_data_item.get('email') == client_email:
                            up = traffic_data_item.get('up', 0)
                            down = traffic_data_item.get('down', 0)
                            actual_traffic = up + down

                            total_up += up
                            total_down += down
                            total_traffic += actual_traffic

                            panel_details.append({
                                'panel_id': panel_id,
                                'panel_name': self.panels[panel_id]['name'],
                                'up': up,
                                'down': down,
                                'total': actual_traffic,
                                'traffic_limit': traffic_data_item.get('total', 0),
                                'inbound_id': traffic_data_item.get('inbound_id'),
                                'inbound_port': traffic_data_item.get('inbound_port'),
                                'inbound_protocol': traffic_data_item.get('inbound_protocol'),
                                'enable': traffic_data_item.get('enable', True),
                                'expiryTime': traffic_data_item.get('expiryTime', 0)
                            })
                            logger.debug(f"Found client {client_email} in panel {panel_id} data. Up: {up}, Down: {down}")
                            break
                else:
                    logger.warning(f"No traffic data (cached or fetched) for panel {panel_id} to process for client {client_email}")

            except Exception as e:
                logger.error(f"Error processing panel {panel_id} for client {client_email} in get_client_traffic_summary: {e}", exc_info=True)

        if panel_details:
            return {
                'email': client_email,
                'total_up': total_up,
                'total_down': total_down,
                'total_traffic': total_traffic,  # 现在是实际使用的流量
                'panel_count': len(panel_details),
                'panel_details': panel_details
            }
        logger.debug(f"No panel details found for client {client_email} across all panels.") # Changed to debug from warning
        return None

    def get_traffic_statistics(self) -> Dict:
        """获取所有面板的流量统计汇总 (使用缓存)"""
        stats_summary = { # Renamed to avoid conflict
            'total_clients': 0,
            'total_up': 0,
            'total_down': 0,
            'total_traffic': 0,
            'panel_stats': {},
            'client_count_by_panel': {},
            'top_clients': []
        }
        all_clients_aggregated_data = {} # Renamed

        for panel_id, client_obj in self.clients.items(): # client renamed to client_obj
            panel_all_clients_data = None # Renamed
            cache_key = panel_id

            try:
                if cache_key in self.panel_data_cache:
                    panel_all_clients_data = self.panel_data_cache[cache_key]
                    if panel_all_clients_data is not None:
                         logger.debug(f"Cache hit for panel {panel_id} (data found) in get_traffic_statistics.")
                    else:
                        logger.debug(f"Cache hit for panel {panel_id} (None found) in get_traffic_statistics. Will try fetching.")
                        # Fall through

                if panel_all_clients_data is None: # Cache miss or cached as None
                    if self.check_panel_health(panel_id):
                        logger.debug(f"Cache miss or None for panel {panel_id} in get_traffic_statistics. Fetching.")
                        fetched_data = client_obj.get_all_client_traffic() # Use client_obj
                        if fetched_data is not None:
                            self.panel_data_cache[cache_key] = fetched_data
                            panel_all_clients_data = fetched_data
                            logger.info(f"Fetched/cached all client traffic for panel {panel_id} in get_traffic_statistics. Clients: {len(fetched_data)}")
                        else:
                            self.panel_data_cache[cache_key] = None
                            logger.warning(f"Failed to fetch traffic for panel {panel_id} in get_traffic_statistics. Storing None.")
                    else:
                        logger.warning(f"Panel {panel_id} health check failed in get_traffic_statistics. Skipping.")
                        continue

                if panel_all_clients_data:
                    current_panel_up = 0 # Renamed
                    current_panel_down = 0 # Renamed
                    current_panel_traffic = 0 # Renamed

                    for client_stat_item in panel_all_clients_data: # Renamed
                        email = client_stat_item.get('email')
                        up = client_stat_item.get('up', 0)
                        down = client_stat_item.get('down', 0)
                        actual_traffic = up + down

                        current_panel_up += up
                        current_panel_down += down
                        current_panel_traffic += actual_traffic

                        if email:
                            if email not in all_clients_aggregated_data:
                                all_clients_aggregated_data[email] = {
                                    'email': email, 'total_up': 0, 'total_down': 0,
                                    'total_traffic': 0, 'panel_count': 0
                                }

                            all_clients_aggregated_data[email]['total_up'] += up
                            all_clients_aggregated_data[email]['total_down'] += down
                            all_clients_aggregated_data[email]['total_traffic'] += actual_traffic
                            all_clients_aggregated_data[email]['panel_count'] += 1

                    stats_summary['panel_stats'][panel_id] = {
                        'name': self.panels[panel_id]['name'],
                        'client_count': len(panel_all_clients_data),
                        'total_up': current_panel_up,
                        'total_down': current_panel_down,
                        'total_traffic': current_panel_traffic
                    }
                    stats_summary['client_count_by_panel'][panel_id] = len(panel_all_clients_data)
            except Exception as e:
                logger.error(f"Error processing panel {panel_id} in get_traffic_statistics: {e}", exc_info=True)

        overall_total_up = 0 # Renamed
        overall_total_down = 0 # Renamed
        overall_total_traffic = 0 # Renamed
        for client_data_val in all_clients_aggregated_data.values(): # Renamed
            overall_total_up += client_data_val['total_up']
            overall_total_down += client_data_val['total_down']
            overall_total_traffic += client_data_val['total_traffic']

        stats_summary['total_up'] = overall_total_up
        stats_summary['total_down'] = overall_total_down
        stats_summary['total_traffic'] = overall_total_traffic
        stats_summary['total_clients'] = len(all_clients_aggregated_data)

        sorted_clients_list = sorted(all_clients_aggregated_data.values(), # Renamed
                              key=lambda x_item: x_item['total_traffic'], # Renamed
                              reverse=True)
        stats_summary['top_clients'] = sorted_clients_list[:10]

        return stats_summary
    
    def _add_client_to_panel(self, panel_id: str, client_email: str, client_id: str = None,
                           traffic_limit_gb: int = 100, expiry_days: int = 30) -> Tuple[bool, Optional[Dict]]:
        """在指定面板中添加客户端"""
        try:
            logger.info(f"尝试在面板 {panel_id} 中添加客户端 {client_email}")

            if panel_id not in self.clients:
                logger.error(f"面板 {panel_id} 的客户端不存在")
                return False, None

            client = self.clients[panel_id]

            # 获取入站规则
            logger.info(f"获取面板 {panel_id} 的入站规则")
            inbounds = client.get_inbounds()
            if not inbounds:
                logger.error(f"面板 {panel_id} 无法获取入站规则")
                return False, None

            logger.info(f"面板 {panel_id} 有 {len(inbounds)} 个入站规则")

            # 选择第一个可用的入站规则
            target_inbound = inbounds[0]
            inbound_id = target_inbound.get('id')

            logger.info(f"选择入站规则 {inbound_id} (端口: {target_inbound.get('port')}, 协议: {target_inbound.get('protocol')})")

            # 添加客户端
            success, client_data = client.add_client_to_inbound(
                inbound_id=inbound_id,
                client_email=client_email,
                client_id=client_id,
                traffic_limit_gb=traffic_limit_gb,
                expiry_days=expiry_days
            )
            
            if success:
                # 更新面板状态
                self.panel_status[panel_id].client_count += 1
                
                # 添加面板信息到客户端数据
                if client_data:
                    client_data['panel_id'] = panel_id
                    client_data['panel_name'] = self.panels[panel_id]['name']
                    client_data['server_address'] = client.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                    client_data['server_port'] = target_inbound.get('port', 443)
                    
                    # 生成VLESS配置
                    vless_config = client.generate_vless_config(
                        target_inbound, client_data.get('id'), client_email
                    )
                    client_data['vless_config'] = vless_config
            
            return success, client_data
            
        except Exception as e:
            logger.error(f"在面板 {panel_id} 中添加客户端时发生错误: {e}")
            return False, None
    
    def get_client_traffic_from_all_panels(self, client_email: str) -> Optional[Dict]:
        """从所有面板获取客户端流量信息"""
        for panel_id, client in self.clients.items():
            if not self.panel_status[panel_id].is_online:
                continue
            
            try:
                traffic_info = client.get_client_traffic(client_email)
                if traffic_info:
                    traffic_info['panel_id'] = panel_id
                    traffic_info['panel_name'] = self.panels[panel_id]['name']
                    return traffic_info
            except Exception as e:
                logger.debug(f"从面板 {panel_id} 获取流量信息失败: {e}")
                continue
        
        return None
    
    def get_panel_statistics(self) -> Dict[str, Any]:
        """获取所有面板的统计信息"""
        stats = {
            'total_panels': len(self.panels),
            'online_panels': sum(1 for status in self.panel_status.values() if status.is_online),
            'total_clients': sum(status.client_count for status in self.panel_status.values()),
            'panels': {}
        }
        
        for panel_id, panel_config in self.panels.items():
            status = self.panel_status[panel_id]
            stats['panels'][panel_id] = {
                'name': panel_config['name'],
                'region': panel_config['region'],
                'base_url': panel_config['base_url'],
                'is_online': status.is_online,
                'client_count': status.client_count,
                'response_time': status.response_time,
                'error_count': status.error_count,
                'last_error': status.last_error,
                'last_check': status.last_check,
                'priority': panel_config['priority'],
                'max_clients': panel_config['max_clients']
            }
        
        return stats
