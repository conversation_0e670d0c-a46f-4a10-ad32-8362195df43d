#!/usr/bin/env python3
"""
创建管理员账户脚本
"""
from flask import Flask
from models import db, User, UserRole
from werkzeug.security import generate_password_hash
import os

def create_admin():
    # 创建Flask应用
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///node_sales.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = 'your-secret-key-here'

    # 初始化数据库
    db.init_app(app)

    with app.app_context():
        try:
            # 检查是否已存在管理员
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User(
                    username='admin',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin123'),
                    role=UserRole.ADMIN,
                    is_active=True
                )
                db.session.add(admin)
                db.session.commit()
                print('✅ 管理员账户创建成功: admin/admin123')
            else:
                print('ℹ️  管理员账户已存在')
                # 更新密码
                admin.password_hash = generate_password_hash('admin123')
                db.session.commit()
                print('✅ 管理员密码已重置为: admin123')
        except Exception as e:
            print(f'❌ 创建管理员失败: {e}')

if __name__ == '__main__':
    create_admin()
