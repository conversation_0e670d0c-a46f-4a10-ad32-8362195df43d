#!/usr/bin/env python3
"""
调试续费功能
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User, Order, Subscription, RenewalPricing
from services.renewal_service import RenewalService

def debug_renewal():
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 调试续费功能 ===")
            
            # 1. 查找用户
            user = User.query.filter_by(username='tizzyt').first()
            if not user:
                print("用户不存在")
                return
            
            print(f"用户: {user.username} (ID: {user.id})")
            
            # 2. 测试续费服务
            renewal_service = RenewalService()
            
            # 3. 获取用户活跃订阅
            print("\n获取用户活跃订阅...")
            subscription = renewal_service.get_user_active_subscription(user.id)
            if not subscription:
                print("没有活跃订阅")
                return
            
            print(f"活跃订阅: {subscription.id}")
            print(f"订单ID: {subscription.order_id}")
            print(f"到期时间: {subscription.expires_at}")
            
            # 4. 获取订单信息
            order = subscription.order
            if not order:
                print("订阅没有关联订单")
                return
            
            print(f"订单: {order.order_id}")
            print(f"价格: {order.price}")
            print(f"时长: {order.duration_days}天")
            
            # 5. 测试续费价格计算
            print("\n测试续费价格计算...")
            for duration in [3, 6, 12]:
                try:
                    price_info = renewal_service.calculate_renewal_price(subscription, duration)
                    print(f"  {duration}个月续费:")
                    print(f"    基础价格: ¥{price_info['base_price']}")
                    print(f"    折扣: {price_info['discount_percentage']}%")
                    print(f"    最终价格: ¥{price_info['final_price']}")
                except Exception as e:
                    print(f"    计算{duration}个月续费价格失败: {e}")
                    import traceback
                    traceback.print_exc()
            
        except Exception as e:
            print(f"调试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    debug_renewal()
