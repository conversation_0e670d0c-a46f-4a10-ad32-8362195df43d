"""Add usage tracking fields to coupon model

Revision ID: 79486a057917
Revises: 7481dea6eef6
Create Date: 2025-06-07 17:42:04.972197

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '79486a057917'
down_revision = '7481dea6eef6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('coupons', schema=None) as batch_op:
        batch_op.add_column(sa.Column('max_uses', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('used_count', sa.Integer(), nullable=False, server_default='0'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('coupons', schema=None) as batch_op:
        batch_op.drop_column('used_count')
        batch_op.drop_column('max_uses')

    # ### end Alembic commands ###
