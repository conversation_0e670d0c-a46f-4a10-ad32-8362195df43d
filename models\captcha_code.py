"""
图形验证码数据模型
"""
from datetime import datetime, timedelta
from . import db
import random
import string


class CaptchaCode(db.Model):
    """图形验证码模型"""
    __tablename__ = 'captcha_codes'

    id = db.Column(db.Integer, primary_key=True)
    
    # 验证码信息
    code = db.Column(db.String(10), nullable=False)  # 验证码文本
    session_id = db.Column(db.String(255), nullable=False, index=True)  # 会话ID
    
    # 状态信息
    is_used = db.Column(db.<PERSON>, nullable=False, default=False)  # 是否已使用
    is_expired = db.Column(db.<PERSON><PERSON><PERSON>, nullable=False, default=False)  # 是否已过期
    
    # 时间信息
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=False)  # 过期时间
    used_at = db.Column(db.DateTime, nullable=True)  # 使用时间
    
    # 安全信息
    ip_address = db.Column(db.String(45), nullable=True)  # 请求IP地址
    user_agent = db.Column(db.Text, nullable=True)  # 用户代理
    attempts = db.Column(db.Integer, nullable=False, default=0)  # 验证尝试次数
    max_attempts = db.Column(db.Integer, nullable=False, default=5)  # 最大尝试次数

    def __init__(self, session_id, valid_minutes=10, **kwargs):
        """
        初始化图形验证码
        
        Args:
            session_id: 会话ID
            valid_minutes: 有效期（分钟）
        """
        super(CaptchaCode, self).__init__(**kwargs)
        self.session_id = session_id
        self.code = self._generate_code()
        self.expires_at = datetime.utcnow() + timedelta(minutes=valid_minutes)

    def _generate_code(self):
        """生成验证码"""
        # 生成4位数字和字母混合验证码，避免容易混淆的字符
        chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'
        return ''.join(random.choices(chars, k=4))

    @property
    def is_valid(self):
        """检查验证码是否有效"""
        now = datetime.utcnow()
        return (
            not self.is_used and 
            not self.is_expired and 
            now < self.expires_at and
            self.attempts < self.max_attempts
        )

    def check_code(self, input_code):
        """
        检查验证码是否正确（不标记为已使用，用于前端预验证）

        Args:
            input_code: 输入的验证码

        Returns:
            tuple: (是否验证成功, 错误信息)
        """
        # 检查是否已过期
        if datetime.utcnow() > self.expires_at:
            return False, '验证码已过期'

        # 检查是否已使用
        if self.is_used:
            return False, '验证码已使用'

        # 检查尝试次数
        if self.attempts >= self.max_attempts:
            return False, '验证码尝试次数过多，已失效'

        # 验证验证码（不区分大小写）
        if input_code.upper() != self.code.upper():
            return False, '验证码错误'

        return True, '验证码正确'

    def verify(self, input_code, ip_address=None):
        """
        验证验证码（会标记为已使用，用于真正的验证）

        Args:
            input_code: 输入的验证码
            ip_address: 请求IP地址

        Returns:
            tuple: (是否验证成功, 错误信息)
        """
        self.attempts += 1

        # 检查是否已过期
        if datetime.utcnow() > self.expires_at:
            self.is_expired = True
            return False, '验证码已过期'

        # 检查是否已使用
        if self.is_used:
            return False, '验证码已使用'

        # 检查尝试次数
        if self.attempts > self.max_attempts:
            self.is_expired = True
            return False, '验证码尝试次数过多，已失效'

        # 验证验证码（不区分大小写）
        if input_code.upper() != self.code.upper():
            return False, f'验证码错误，还可尝试 {self.max_attempts - self.attempts} 次'

        # 验证成功
        self.is_used = True
        self.used_at = datetime.utcnow()
        if ip_address:
            self.ip_address = ip_address

        return True, '验证成功'

    def mark_expired(self):
        """标记为已过期"""
        self.is_expired = True

    @classmethod
    def get_valid_code(cls, session_id):
        """
        获取有效的验证码
        
        Args:
            session_id: 会话ID
            
        Returns:
            CaptchaCode: 有效的验证码对象，如果没有则返回None
        """
        now = datetime.utcnow()
        return cls.query.filter(
            cls.session_id == session_id,
            cls.is_used == False,
            cls.is_expired == False,
            cls.expires_at > now,
            cls.attempts < cls.max_attempts
        ).order_by(cls.created_at.desc()).first()

    @classmethod
    def cleanup_expired(cls, days_old=1):
        """
        清理过期的验证码
        
        Args:
            days_old: 清理多少天前的记录
            
        Returns:
            int: 清理的记录数
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        expired_codes = cls.query.filter(
            db.or_(
                cls.expires_at < datetime.utcnow(),
                cls.created_at < cutoff_date,
                cls.is_used == True,
                cls.is_expired == True
            )
        )
        
        count = expired_codes.count()
        expired_codes.delete()
        
        return count

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'session_id': self.session_id,
            'is_used': self.is_used,
            'is_expired': self.is_expired,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'used_at': self.used_at.isoformat() if self.used_at else None,
            'attempts': self.attempts,
            'max_attempts': self.max_attempts,
            'is_valid': self.is_valid
        }

    def __repr__(self):
        return f'<CaptchaCode {self.session_id}>'
