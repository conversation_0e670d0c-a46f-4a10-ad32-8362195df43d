/* 节点商城 - 自定义样式 */

/* CSS变量定义 - 中国风配色方案 */
:root {
    --primary-color: #1890ff;
    --primary-hover: #40a9ff;
    --primary-active: #096dd9;
    --secondary-color: #f5222d;
    --success-color: #52c41a;
    --warning-color: #fa8c16;
    --info-color: #13c2c2;
    --light-color: #f5f5f5;
    --dark-color: #262626;
    --border-color: #d9d9d9;
    --text-color: #262626;
    --text-secondary: #8c8c8c;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.15);
    --border-radius: 6px;
    --border-radius-lg: 8px;
}

/* 字体优化 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'PingFang SC', 'Microsoft YaHei', <PERSON>m<PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fafafa;
}

/* 重写Bootstrap主色调 */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-primary:active {
    background-color: var(--primary-active);
    border-color: var(--primary-active);
}

.bg-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
}

/* 导航栏优化 */
.navbar {
    box-shadow: var(--shadow-light);
    backdrop-filter: blur(10px);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
    border-radius: var(--border-radius);
    margin: 0 2px;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

/* 卡片优化 */
.card {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    transition: all 0.3s ease;
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    font-weight: 600;
}

/* 产品卡片特殊样式 */
.product-card {
    position: relative;
    overflow: hidden;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.product-card:hover::before {
    left: 100%;
}

/* 价格显示优化 */
.price {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--secondary-color);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 统计卡片渐变背景 */
.stats-card-primary {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.stats-card-success {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.stats-card-info {
    background: linear-gradient(135deg, #13c2c2 0%, #36cfc9 100%);
}

.stats-card-warning {
    background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
}

/* 紫色主题样式 */
.bg-purple {
    background: linear-gradient(135deg, #722ed1 0%, #9254de 100%) !important;
}

.text-purple {
    color: #722ed1 !important;
}

.btn-purple {
    background-color: #722ed1;
    border-color: #722ed1;
    color: white;
    transition: all 0.3s ease;
}

.btn-purple:hover {
    background-color: #9254de;
    border-color: #9254de;
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-medium);
}

.btn-purple:active {
    background-color: #531dab;
    border-color: #531dab;
    color: white;
}

/* 表单优化 */
.form-control {
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(24, 144, 255, 0.25);
}

/* 页脚优化 */
.footer {
    background: linear-gradient(135deg, #f5f5f5 0%, #fafafa 100%);
    border-top: 1px solid var(--border-color);
}

/* 动画类 */
.fade-in {
    animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from { transform: translateY(40px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.slide-in-left {
    animation: slideInLeft 0.7s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.slide-in-right {
    animation: slideInRight 0.7s ease-out;
}

@keyframes slideInRight {
    from { transform: translateX(50px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.scale-in {
    animation: scaleIn 0.5s ease-out;
}

@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* 悬停动画 */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* 按钮动画 */
.btn-animated {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-animated::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-animated:hover::before {
    left: 100%;
}

/* 脉冲动画 */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* 摇摆动画 */
.wobble {
    animation: wobble 1s ease-in-out;
}

@keyframes wobble {
    0% { transform: translateX(0%); }
    15% { transform: translateX(-25px) rotate(-5deg); }
    30% { transform: translateX(20px) rotate(3deg); }
    45% { transform: translateX(-15px) rotate(-3deg); }
    60% { transform: translateX(10px) rotate(2deg); }
    75% { transform: translateX(-5px) rotate(-1deg); }
    100% { transform: translateX(0%); }
}

/* 响应式优化 */
@media (max-width: 1200px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
}

@media (max-width: 992px) {
    .display-5 {
        font-size: 2rem;
    }

    .stats-card h2 {
        font-size: 1.5rem;
    }

    .hero-icon {
        font-size: 6rem !important;
    }
}

@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.1rem;
    }

    .navbar-brand span {
        display: none;
    }

    .price {
        font-size: 1.5rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .display-5 {
        font-size: 1.75rem;
    }

    .lead {
        font-size: 1rem;
    }

    .hero-section {
        padding: 3rem 0 !important;
    }

    .hero-icon {
        font-size: 4rem !important;
    }

    .stats-card h2 {
        font-size: 1.25rem;
    }

    .stats-card .card-body {
        padding: 1.5rem !important;
    }

    .feature-icon {
        width: 60px !important;
        height: 60px !important;
    }

    .feature-icon i {
        font-size: 1.5rem !important;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .container-fluid.vh-100 {
        min-height: 100vh;
        height: auto;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }

    .display-5 {
        font-size: 1.5rem;
    }

    .hero-section {
        padding: 2rem 0 !important;
    }

    .hero-icon {
        font-size: 3rem !important;
    }

    .stats-card h2 {
        font-size: 1.1rem;
    }

    .stats-card .card-body {
        padding: 1rem !important;
    }

    .stats-card .rounded-circle {
        width: 50px !important;
        height: 50px !important;
    }

    .stats-card .rounded-circle i {
        font-size: 1.25rem !important;
    }

    .feature-icon {
        width: 50px !important;
        height: 50px !important;
    }

    .feature-icon i {
        font-size: 1.25rem !important;
    }

    .card-body {
        padding: 1rem !important;
    }

    .btn {
        font-size: 0.875rem;
    }

    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .price {
        font-size: 1.25rem;
    }

    .notification {
        min-width: 280px;
        right: 10px;
        top: 10px;
    }

    .tooltip-custom::before {
        font-size: 0.75rem;
        padding: 6px 10px;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .hover-lift:hover {
        transform: none;
        box-shadow: var(--shadow-light);
    }

    .hover-scale:hover {
        transform: none;
    }

    .btn-animated::before {
        display: none;
    }

    .card:hover {
        transform: none;
    }

    .nav-link:hover {
        background-color: transparent;
        transform: none;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000;
        --text-secondary: #000;
        --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
        --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.4);
    }

    .card {
        border: 2px solid var(--border-color);
    }

    .btn {
        border-width: 2px;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .fade-in,
    .slide-up,
    .slide-in-left,
    .slide-in-right,
    .scale-in {
        animation: none;
    }

    .pulse {
        animation: none;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .footer,
    .btn,
    .alert {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .text-primary,
    .text-success,
    .text-info,
    .text-warning,
    .text-danger {
        color: #000 !important;
    }

    .bg-primary,
    .bg-success,
    .bg-info,
    .bg-warning,
    .bg-danger {
        background: #fff !important;
        color: #000 !important;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s infinite;
}

@keyframes dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}

.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 成功提示样式 */
.success-icon {
    color: var(--success-color);
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    80% { transform: translateY(-5px); }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    min-width: 300px;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-medium);
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.notification.show {
    transform: translateX(0);
}

.notification.hide {
    transform: translateX(400px);
}

/* 进度条 */
.progress-bar-animated {
    background: linear-gradient(45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent);
    background-size: 1rem 1rem;
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% { background-position: 1rem 0; }
    100% { background-position: 0 0; }
}

/* 骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 4px;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* 工具提示增强 */
.tooltip-custom {
    position: relative;
    cursor: help;
}

.tooltip-custom::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--dark-color);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip-custom::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--dark-color);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip-custom:hover::before,
.tooltip-custom:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}
