#!/usr/bin/env python3
"""
重置用户密码
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, User
from werkzeug.security import generate_password_hash

def reset_password():
    app = create_app()
    
    with app.app_context():
        try:
            # 查找用户
            user = User.query.filter_by(username='tizzyt').first()
            if user:
                # 重置密码为123456
                user.password_hash = generate_password_hash('123456')
                db.session.commit()
                print(f"用户 {user.username} 的密码已重置为: 123456")
            else:
                print("用户不存在")
                
        except Exception as e:
            print(f"重置密码失败: {e}")

if __name__ == "__main__":
    reset_password()
