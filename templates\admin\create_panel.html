{% extends "base.html" %}

{% block title %}添加面板 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <h1>
                <i class="bi bi-plus-circle"></i> 添加X-UI面板
            </h1>
        </div>
    </div>
    
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">面板配置</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.create_panel') }}">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">面板名称 *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="region" class="form-label">区域</label>
                                    <input type="text" class="form-control" id="region" name="region" value="default">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="base_url" class="form-label">面板地址 *</label>
                                    <input type="url" class="form-control" id="base_url" name="base_url" 
                                           placeholder="http://example.com:54321" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="path_prefix" class="form-label">路径前缀</label>
                                    <input type="text" class="form-control" id="path_prefix" name="path_prefix" value="/">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">用户名 *</label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">密码 *</label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="max_clients" class="form-label">最大客户端数</label>
                                    <input type="number" class="form-control" id="max_clients" name="max_clients" value="1000">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">优先级</label>
                                    <input type="number" class="form-control" id="priority" name="priority" value="1">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="active">运行中</option>
                                        <option value="maintenance">维护中</option>
                                        <option value="inactive">停用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.panels') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> 返回面板列表
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> 添加面板
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 配置说明</h6>
                <ul class="mb-0">
                    <li>面板地址格式：http://ip:port 或 https://domain.com</li>
                    <li>路径前缀通常为 / 或 /path/</li>
                    <li>用户名和密码为X-UI面板的登录凭据</li>
                    <li>优先级数字越小优先级越高</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
