{% extends "base.html" %}

{% block title %}支付订单 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-warning text-dark">
                    <h4 class="mb-0">
                        <i class="bi bi-credit-card"></i> 完成支付
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h5><i class="bi bi-info-circle"></i> 订单已创建</h5>
                        <p class="mb-0">
                            您的订单 <strong>{{ order.order_id }}</strong> 已创建成功，请完成支付以激活服务。
                        </p>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>订单信息</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <table class="table table-borderless mb-0">
                                        <tr>
                                            <td><strong>订单号：</strong></td>
                                            <td>{{ order.order_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>支付ID：</strong></td>
                                            <td>{{ payment_id }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>协议类型：</strong></td>
                                            <td>{{ order.node_type.value.upper() }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>有效期：</strong></td>
                                            <td>{{ order.duration_days }} 天</td>
                                        </tr>
                                        <tr>
                                            <td><strong>流量：</strong></td>
                                            <td>{{ order.traffic_limit_gb }}GB</td>
                                        </tr>
                                        <tr>
                                            <td><strong>原价：</strong></td>
                                            <td>¥{{ "%.2f"|format(order.price + (order.discount_amount if order.discount_amount else 0)) }}</td>
                                        </tr>
                                        {% if order.applied_coupon_code %}
                                        <tr>
                                            <td><strong>优惠码：</strong></td>
                                            <td>{{ order.applied_coupon_code }}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>优惠金额：</strong></td>
                                            <td>¥{{ "%.2f"|format(order.discount_amount if order.discount_amount else 0) }}</td>
                                        </tr>
                                        {% endif %}
                                        <tr>
                                            <td><strong>应付金额：</strong></td>
                                            <td class="text-primary fw-bold">¥{{ "%.2f"|format(order.price) }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>支付方式</h5>
                            <div class="card">
                                <div class="card-body">
                                    {% if order.payment_method == 'manual' %}
                                    <h6><i class="bi bi-bank"></i> 手动转账</h6>
                                    <p>请通过以下方式完成支付：</p>
                                    <ul>
                                        <li><strong>支付宝：</strong> <EMAIL></li>
                                        <li><strong>微信：</strong> wxpay123456</li>
                                        <li><strong>银行卡：</strong> 6222 0000 0000 0000</li>
                                    </ul>
                                    <div class="alert alert-warning">
                                        <small>
                                            <i class="bi bi-exclamation-triangle"></i>
                                            转账时请备注订单号：<strong>{{ order.order_id }}</strong>
                                        </small>
                                    </div>
                                    {% elif order.payment_method == 'alipay' %}
                                    <h6><i class="bi bi-alipay"></i> 支付宝支付</h6>
                                    <p>请扫描下方二维码完成支付</p>
                                    <div class="text-center">
                                        <div class="bg-light p-4 d-inline-block">
                                            <i class="bi bi-qr-code" style="font-size: 6rem;"></i>
                                            <p class="mt-2">支付宝二维码</p>
                                        </div>
                                    </div>
                                    {% elif order.payment_method == 'wechat' %}
                                    <h6><i class="bi bi-wechat"></i> 微信支付</h6>
                                    <p>请扫描下方二维码完成支付</p>
                                    <div class="text-center">
                                        <div class="bg-light p-4 d-inline-block">
                                            <i class="bi bi-qr-code" style="font-size: 6rem;"></i>
                                            <p class="mt-2">微信支付二维码</p>
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <h6>支付完成后</h6>
                        <p class="text-muted">
                            支付完成后，系统将自动处理您的订单并发送节点配置到您的邮箱。<br>
                            如果长时间未收到配置信息，请联系客服。
                        </p>
                        
                        <div class="mt-4">
                            <a href="{{ url_for('shop.order_status', order_id=order.order_id) }}" class="btn btn-primary me-2">
                                <i class="bi bi-eye"></i> 查看订单状态
                            </a>
                            <a href="{{ url_for('shop.shop_index') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> 返回商店
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支付说明 -->
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 支付说明</h6>
                <ul class="mb-0">
                    <li>支付完成后，订单将在1-5分钟内自动处理</li>
                    <li>节点配置信息将发送到您的邮箱：{{ order.customer_email }}</li>
                    <li>如遇问题，请保存订单号并联系客服</li>
                    <li>支持7天无理由退款</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
