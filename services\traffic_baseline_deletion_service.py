"""
流量基准删除策略服务
管理订阅删除时流量基准数据的处理方式
"""
import logging
from typing import Optional, Dict, List
from datetime import datetime

from models import db
from models.subscription_traffic_baseline import SubscriptionTrafficBaseline

logger = logging.getLogger(__name__)


class TrafficBaselineDeletionService:
    """流量基准删除策略服务"""
    
    # 删除策略配置
    DELETION_STRATEGY_HARD = "hard"  # 物理删除，数据不可恢复
    DELETION_STRATEGY_SOFT = "soft"  # 软删除，数据可恢复
    
    def __init__(self, deletion_strategy: str = DELETION_STRATEGY_HARD):
        """
        初始化删除策略服务
        
        Args:
            deletion_strategy: 删除策略，'hard' 或 'soft'
        """
        self.deletion_strategy = deletion_strategy
        logger.info(f"流量基准删除策略设置为: {deletion_strategy}")
    
    def handle_subscription_deletion(self, subscription_id: int) -> Dict:
        """
        处理订阅删除时的流量基准数据
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            Dict: 处理结果
        """
        try:
            baseline = SubscriptionTrafficBaseline.query.filter_by(
                subscription_id=subscription_id
            ).first()
            
            if not baseline:
                return {
                    'success': True,
                    'action': 'no_action',
                    'message': f'订阅 {subscription_id} 没有流量基准数据'
                }
            
            if self.deletion_strategy == self.DELETION_STRATEGY_SOFT:
                return self._soft_delete_baseline(baseline)
            else:
                return self._hard_delete_baseline(baseline)
                
        except Exception as e:
            logger.error(f"处理订阅 {subscription_id} 流量基准删除失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _soft_delete_baseline(self, baseline: SubscriptionTrafficBaseline) -> Dict:
        """软删除流量基准"""
        try:
            baseline.soft_delete()
            # 不在这里提交事务，让调用方统一管理事务

            logger.info(f"软删除订阅 {baseline.subscription_id} 的流量基准: "
                       f"{baseline.baseline_total_bytes / (1024**2):.2f} MB")

            return {
                'success': True,
                'action': 'soft_delete',
                'subscription_id': baseline.subscription_id,
                'baseline_total_mb': round(baseline.baseline_total_bytes / (1024**2), 2),
                'message': '流量基准已软删除，数据可恢复'
            }

        except Exception as e:
            logger.error(f"软删除流量基准失败: {e}")
            raise
    
    def _hard_delete_baseline(self, baseline: SubscriptionTrafficBaseline) -> Dict:
        """硬删除流量基准"""
        try:
            subscription_id = baseline.subscription_id
            baseline_total_mb = round(baseline.baseline_total_bytes / (1024**2), 2)

            db.session.delete(baseline)
            # 不在这里提交事务，让调用方统一管理事务

            logger.info(f"硬删除订阅 {subscription_id} 的流量基准: {baseline_total_mb} MB")

            return {
                'success': True,
                'action': 'hard_delete',
                'subscription_id': subscription_id,
                'baseline_total_mb': baseline_total_mb,
                'message': '流量基准已永久删除'
            }

        except Exception as e:
            logger.error(f"硬删除流量基准失败: {e}")
            raise
    
    def restore_baseline(self, subscription_id: int) -> Dict:
        """
        恢复软删除的流量基准
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            Dict: 恢复结果
        """
        try:
            baseline = SubscriptionTrafficBaseline.query.filter_by(
                subscription_id=subscription_id,
                is_deleted=True
            ).first()
            
            if not baseline:
                return {
                    'success': False,
                    'error': f'订阅 {subscription_id} 没有可恢复的流量基准数据'
                }
            
            baseline.restore()
            db.session.commit()
            
            logger.info(f"恢复订阅 {subscription_id} 的流量基准: "
                       f"{baseline.baseline_total_bytes / (1024**2):.2f} MB")
            
            return {
                'success': True,
                'subscription_id': subscription_id,
                'baseline_total_mb': round(baseline.baseline_total_bytes / (1024**2), 2),
                'message': '流量基准已恢复'
            }
            
        except Exception as e:
            logger.error(f"恢复流量基准失败: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_deleted_baselines(self, limit: int = 100) -> List[Dict]:
        """
        获取已软删除的流量基准列表
        
        Args:
            limit: 返回数量限制
            
        Returns:
            List[Dict]: 已删除的基准列表
        """
        try:
            deleted_baselines = SubscriptionTrafficBaseline.query.filter_by(
                is_deleted=True
            ).order_by(SubscriptionTrafficBaseline.deleted_at.desc()).limit(limit).all()
            
            return [
                {
                    'subscription_id': baseline.subscription_id,
                    'baseline_total_mb': round(baseline.baseline_total_bytes / (1024**2), 2),
                    'deleted_at': baseline.deleted_at.isoformat() if baseline.deleted_at else None,
                    'can_restore': True
                }
                for baseline in deleted_baselines
            ]
            
        except Exception as e:
            logger.error(f"获取已删除基准列表失败: {e}")
            return []
    
    def cleanup_old_deleted_baselines(self, days: int = 30) -> Dict:
        """
        清理超过指定天数的软删除基准数据
        
        Args:
            days: 保留天数
            
        Returns:
            Dict: 清理结果
        """
        try:
            from datetime import timedelta
            cutoff_date = datetime.utcnow() - timedelta(days=days)
            
            old_baselines = SubscriptionTrafficBaseline.query.filter(
                SubscriptionTrafficBaseline.is_deleted == True,
                SubscriptionTrafficBaseline.deleted_at < cutoff_date
            ).all()
            
            if not old_baselines:
                return {
                    'success': True,
                    'cleaned_count': 0,
                    'message': f'没有超过 {days} 天的已删除基准数据'
                }
            
            cleaned_count = len(old_baselines)
            total_mb = sum(baseline.baseline_total_bytes for baseline in old_baselines) / (1024**2)
            
            for baseline in old_baselines:
                db.session.delete(baseline)
            
            db.session.commit()
            
            logger.info(f"清理了 {cleaned_count} 个超过 {days} 天的已删除基准，"
                       f"释放 {total_mb:.2f} MB 的基准数据")
            
            return {
                'success': True,
                'cleaned_count': cleaned_count,
                'total_mb_cleaned': round(total_mb, 2),
                'message': f'成功清理 {cleaned_count} 个过期的已删除基准'
            }
            
        except Exception as e:
            logger.error(f"清理过期已删除基准失败: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': str(e)
            }
    
    @classmethod
    def get_strategy_from_config(cls) -> str:
        """从配置获取删除策略"""
        # {{CHENGQI:
        # Action: Modified
        # Timestamp: 2025-01-27 15:30:00 +08:00
        # Task_ID: P4-LD-006
        # Principle_Applied: KISS - 简化配置，默认使用硬删除策略
        # Language: Python
        # Description: 设置为自动硬删除模式，用户删除订阅时自动删除流量基准
        # }}

        # 默认使用硬删除策略（自动删除）
        import os
        return os.getenv('TRAFFIC_BASELINE_DELETION_STRATEGY', cls.DELETION_STRATEGY_HARD)


# 全局实例
traffic_baseline_deletion_service = TrafficBaselineDeletionService(
    deletion_strategy=TrafficBaselineDeletionService.get_strategy_from_config()
)
