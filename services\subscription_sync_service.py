"""
订阅同步服务 - 处理分组面板变更时的订阅节点同步
"""
import logging
import uuid
from typing import List, Dict
from datetime import datetime
from sqlalchemy import and_, or_
from models import db, XUIPanelGroup, XUIPanel, Subscription, Order, NodeConfig, OrderStatus
from multi_xui_manager import MultiXUIManager

logger = logging.getLogger(__name__)

class SubscriptionSyncService:
    """订阅同步服务类"""
    
    def __init__(self):
        self.xui_manager = None
    
    def sync_group_subscriptions_for_new_panel(self, group_id: int, panel_id: int) -> Dict:
        """
        为分组中新增的面板同步所有活跃订阅

        Args:
            group_id: 分组ID
            panel_id: 新增的面板ID

        Returns:
            同步结果统计
        """
        try:
            
            # 获取分组和面板信息
            group = XUIPanelGroup.query.get(group_id)
            panel = XUIPanel.query.get(panel_id)
            
            if not group or not panel:
                error_msg = f"分组或面板不存在: group_id={group_id}, panel_id={panel_id}"
                return {'success': False, 'error': error_msg}

            # 获取该分组的所有活跃订阅
            active_subscriptions = self._get_group_active_subscriptions(group_id)

            if not active_subscriptions:
                return {
                    'success': True,
                    'affected_subscriptions': 0,
                    'success_count': 0,
                    'failed_count': 0,
                    'message': '该分组没有活跃订阅'
                }
            
            # 初始化X-UI管理器（仅包含目标面板）
            self.xui_manager = MultiXUIManager(group_id=None)
            
            # 批量处理订阅
            success_count = 0
            failed_count = 0
            failed_details = []
            
            for subscription in active_subscriptions:
                try:
                    result = self._add_panel_node_for_subscription(
                        subscription, panel
                    )
                    if result['success']:
                        success_count += 1
                    else:
                        failed_count += 1
                        failed_details.append({
                            'subscription_id': subscription.id,
                            'order_id': subscription.order.order_id,
                            'error': result['error']
                        })

                except Exception as e:
                    failed_count += 1
                    error_msg = f"处理订阅 {subscription.id} 时发生错误: {str(e)}"
                    logger.error(error_msg)
                    failed_details.append({
                        'subscription_id': subscription.id,
                        'error': error_msg
                    })

            # 提交数据库事务
            db.session.commit()

            logger.info(f"分组 {group.name} 新增面板 {panel.name} 的订阅同步完成: "
                       f"成功 {success_count}, 失败 {failed_count}")

            return {
                'success': True,
                'affected_subscriptions': len(active_subscriptions),
                'success_count': success_count,
                'failed_count': failed_count,
                'failed_details': failed_details
            }
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"同步过程发生错误: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def _get_group_active_subscriptions(self, group_id: int) -> List:
        """获取分组的所有活跃订阅"""
        try:
            # {{CHENGQI:
            # Action: Modified
            # Timestamp: 2025-06-09 20:50:00 +08:00
            # Task_ID: P4-LD-002
            # Principle_Applied: 修复外键歧义问题，明确指定JOIN条件
            # Language: Python
            # Description: 修复Subscription和Order之间的JOIN歧义，明确使用order_id关系
            # }}

            # 查询该分组的所有活跃订阅，明确指定JOIN条件避免歧义
            subscriptions = db.session.query(Subscription).join(
                Order, Subscription.order_id == Order.id
            ).filter(
                and_(
                    Subscription.group_id == group_id,
                    Subscription.is_active == True,
                    Order.status == OrderStatus.COMPLETED,
                    or_(
                        Order.expires_at.is_(None),
                        Order.expires_at > datetime.utcnow()
                    )
                )
            ).all()

            return subscriptions

        except Exception as e:
            logger.error(f"获取分组 {group_id} 的活跃订阅失败: {e}")
            return []
    
    def _add_panel_node_for_subscription(self, subscription, panel) -> Dict:
        """为单个订阅在指定面板中添加节点"""
        try:
            order = subscription.order
            if not order:
                return {'success': False, 'error': '订阅没有关联的订单'}
            
            # 检查是否已经在该面板中有节点（数据库检查）
            existing_node = NodeConfig.query.filter(
                and_(
                    NodeConfig.order_id == order.id,
                    NodeConfig.server_address == panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                )
            ).first()

            if existing_node:
                return {'success': True, 'message': '该面板已有节点，跳过'}

            # 生成客户端信息
            client_id = str(uuid.uuid4())
            # 使用与创建订阅时一致的客户端邮箱格式
            client_email = f"{order.customer_email}_{order.order_id}"

            # 检查XUI面板中是否已存在该客户端
            from xui_client import XUIClient
            try:
                # 创建XUI客户端进行检查
                xui_client = XUIClient(
                    base_url=panel.base_url,
                    username=panel.username,
                    password=panel.password,
                    path_prefix=panel.path_prefix
                )

                # 检查客户端是否已存在
                if xui_client.client_exists_by_email(client_email):
                    logger.info(f"客户端 {client_email} 已存在于面板 {panel.name}，跳过添加")
                    return {'success': True, 'message': '客户端已存在于XUI面板，跳过'}

            except Exception as e:
                logger.warning(f"检查客户端是否存在时发生错误: {e}，继续尝试添加")
            
            # 现在添加客户端到XUI面板
            try:

                # 获取入站规则
                inbounds = xui_client.get_inbounds()
                if not inbounds:
                    logger.error(f"面板 {panel.name} 无法获取入站规则")
                    return {'success': False, 'error': '无法获取面板入站规则'}

                # 选择第一个可用的入站规则
                target_inbound = inbounds[0]
                inbound_id = target_inbound.get('id')

                logger.info(f"在面板 {panel.name} 中使用入站规则 {inbound_id}")

                # 添加客户端
                success, client_data = xui_client.add_client_to_inbound(
                    inbound_id=inbound_id,
                    client_email=client_email,
                    client_id=client_id,
                    traffic_limit_gb=order.traffic_limit_gb,
                    expiry_days=order.duration_days
                )

                if success and client_data:
                    # 添加面板信息到客户端数据
                    client_data['panel_id'] = panel.id
                    client_data['panel_name'] = panel.name
                    client_data['server_address'] = panel.base_url.replace('http://', '').replace('https://', '').split(':')[0]
                    client_data['server_port'] = target_inbound.get('port', 443)

                    # 生成VLESS配置
                    vless_config = xui_client.generate_vless_config(
                        target_inbound, client_data.get('id'), client_email
                    )
                    client_data['vless_config'] = vless_config

            except Exception as e:
                logger.error(f"在面板 {panel.name} 中添加客户端时发生错误: {e}")
                return {'success': False, 'error': f'面板操作失败: {str(e)}'}
            
            if success and client_data:
                # {{CHENGQI:
                # Action: Modified
                # Timestamp: 2025-06-09 21:00:00 +08:00
                # Task_ID: P4-LD-002
                # Principle_Applied: 修复NodeConfig字段问题，移除不存在的字段
                # Language: Python
                # Description: 移除sync_source和sync_log_id字段，使用正确的inbound_id
                # }}

                # 创建新的节点配置记录
                node_config = NodeConfig(
                    order_id=order.id,
                    xui_inbound_id=inbound_id,  # 使用实际的入站ID
                    client_id=client_id,
                    client_email=client_email,
                    server_address=client_data.get('server_address', ''),
                    server_port=client_data.get('server_port', 443),
                    protocol=order.node_type.value,
                    vless_config=client_data.get('vless_config', '')
                )

                db.session.add(node_config)

                logger.info(f"成功为订阅 {subscription.id} 在面板 {panel.name} 中创建节点")
                return {'success': True, 'node_config_id': node_config.id}
            else:
                error_msg = "在面板中创建客户端失败"
                logger.warning(f"为订阅 {subscription.id} 在面板 {panel.name} 中创建节点失败")
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"添加节点时发生错误: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    


    def sync_group_all_panels(self, group_id: int) -> Dict:
        """
        同步分组中所有面板的订阅
        这是为手动同步功能专门设计的方法

        Args:
            group_id: 分组ID

        Returns:
            同步结果统计
        """
        try:
            # {{CHENGQI:
            # Action: Added
            # Timestamp: 2025-06-09 20:45:00 +08:00
            # Task_ID: P4-LD-002
            # Principle_Applied: DRY - 避免重复代码，创建专门的全面板同步方法
            # Language: Python
            # Description: 添加专门用于手动同步的方法，处理分组中所有面板的订阅同步
            # }}

            # 获取分组信息
            group = XUIPanelGroup.query.get(group_id)
            if not group:
                return {'success': False, 'error': f'分组不存在: group_id={group_id}'}

            # 获取分组中的所有面板
            panels = group.panels
            if not panels:
                return {
                    'success': True,
                    'message': '分组中没有面板',
                    'affected_subscriptions': 0,
                    'success_count': 0,
                    'failed_count': 0
                }

            # 获取该分组的所有活跃订阅
            active_subscriptions = self._get_group_active_subscriptions(group_id)
            if not active_subscriptions:
                return {
                    'success': True,
                    'message': '该分组没有活跃订阅',
                    'affected_subscriptions': 0,
                    'success_count': 0,
                    'failed_count': 0
                }

            logger.info(f"开始同步分组 {group.name}: {len(active_subscriptions)} 个订阅, {len(panels)} 个面板")

            # 统计结果
            total_success = 0
            total_failed = 0
            panel_results = []

            # 为每个面板执行同步
            for panel in panels:
                try:
                    panel_success = 0
                    panel_failed = 0

                    # 为每个订阅在当前面板中创建节点
                    for subscription in active_subscriptions:
                        try:
                            result = self._add_panel_node_for_subscription(
                                subscription, panel
                            )
                            if result['success']:
                                panel_success += 1
                                total_success += 1
                            else:
                                panel_failed += 1
                                total_failed += 1
                                logger.warning(f"订阅 {subscription.id} 在面板 {panel.name} 同步失败: {result.get('error', '未知错误')}")
                        except Exception as e:
                            panel_failed += 1
                            total_failed += 1
                            logger.error(f"订阅 {subscription.id} 在面板 {panel.name} 同步异常: {e}")

                    panel_results.append({
                        'panel_name': panel.name,
                        'panel_id': panel.id,
                        'success_count': panel_success,
                        'failed_count': panel_failed
                    })

                    logger.info(f"面板 {panel.name} 同步完成: 成功 {panel_success}, 失败 {panel_failed}")

                except Exception as e:
                    logger.error(f"面板 {panel.name} 同步过程异常: {e}")
                    panel_results.append({
                        'panel_name': panel.name,
                        'panel_id': panel.id,
                        'success_count': 0,
                        'failed_count': len(active_subscriptions),
                        'error': str(e)
                    })
                    total_failed += len(active_subscriptions)

            # 提交数据库事务
            db.session.commit()

            logger.info(f"分组 {group.name} 全面板同步完成: 总成功 {total_success}, 总失败 {total_failed}")

            return {
                'success': True,
                'group_name': group.name,
                'affected_subscriptions': len(active_subscriptions),
                'panel_count': len(panels),
                'success_count': total_success,
                'failed_count': total_failed,
                'panel_results': panel_results
            }

        except Exception as e:
            db.session.rollback()
            error_msg = f"全面板同步过程发生错误: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
