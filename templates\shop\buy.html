{% extends "base.html" %}

{% block title %}购买 {{ product.name }} - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-cart-plus"></i> 购买套餐
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 产品信息 -->
                        <div class="col-md-6">
                            <h5>套餐详情</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">{{ product.name }}</h6>
                                    <p class="card-text">{{ product.description or '高速稳定的网络节点服务' }}</p>
                                    
                                    <ul class="list-unstyled">
                                        <li><i class="bi bi-check-circle text-success"></i> 
                                            有效期：<strong>{{ product.duration_days }}</strong> 天
                                        </li>
                                        <li><i class="bi bi-check-circle text-success"></i> 
                                            流量：<strong>{{ product.traffic_limit_gb }}GB</strong>
                                        </li>
                                        <li><i class="bi bi-check-circle text-success"></i> 
                                            协议：<strong>{{ product.node_type.value.upper() }}</strong>
                                        </li>
                                    </ul>
                                    
                                    <div class="price text-center mt-3">
                                        {% if product.price == 0 %}
                                            <span class="h3 text-success">免费</span>
                                        {% else %}
                                            <span class="h3 text-primary">¥{{ "%.2f"|format(product.price) }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 购买表单 -->
                        <div class="col-md-6">
                            <h5>购买信息</h5>
                            <form method="POST" action="{{ url_for('shop.purchase_product') }}">
                                <input type="hidden" name="product_id" value="{{ product.id }}">
                                
                                <div class="mb-3">
                                    <label for="customer_email" class="form-label">邮箱地址 *</label>
                                    <input type="email" class="form-control" id="customer_email" name="customer_email" required>
                                    <div class="form-text">节点配置将发送到此邮箱</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="customer_name" class="form-label">姓名</label>
                                    <input type="text" class="form-control" id="customer_name" name="customer_name">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="customer_remarks" class="form-label">备注名称</label>
                                    <input type="text" class="form-control" id="customer_remarks" name="customer_remarks" placeholder="可选，用于识别您的节点">
                                </div>

                                <div class="mb-3">
                                    <label for="coupon_code" class="form-label">优惠码 (可选)</label>
                                    <input type="text" class="form-control" id="coupon_code" name="coupon_code" placeholder="输入您的优惠码">
                                </div>
                                
                                <!-- 测试模式选项 -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="test_mode" name="test_mode" onchange="toggleTestMode()">
                                        <label class="form-check-label text-warning" for="test_mode">
                                            <i class="bi bi-bug"></i> 测试模式（跳过支付和邮件）
                                        </label>
                                    </div>
                                </div>

                                {% if product.price > 0 %}
                                <div class="mb-3" id="payment_section">
                                    <label for="payment_method" class="form-label">支付方式</label>
                                    <select class="form-select" id="payment_method" name="payment_method">
                                        <option value="manual">手动转账</option>
                                        <option value="alipay">支付宝</option>
                                        <option value="wechat">微信支付</option>
                                    </select>
                                </div>
                                {% else %}
                                <input type="hidden" name="payment_method" value="free">
                                {% endif %}
                                
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-credit-card"></i> 
                                        {% if product.price == 0 %}
                                            立即获取
                                        {% else %}
                                            立即购买
                                        {% endif %}
                                    </button>
                                    <a href="{{ url_for('shop.shop_index') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-arrow-left"></i> 返回商店
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 购买说明 -->
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="alert alert-info">
                <h6><i class="bi bi-info-circle"></i> 购买说明</h6>
                <ul class="mb-0">
                    <li>购买后节点配置将自动发送到您的邮箱</li>
                    <li>请确保邮箱地址正确，配置信息仅发送一次</li>
                    <li>如有问题请联系客服支持</li>
                    {% if product.price > 0 %}
                    <li>付费订单需要完成支付后才会激活</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<script>
function toggleTestMode() {
    const testMode = document.getElementById('test_mode');
    const paymentSection = document.getElementById('payment_section');
    const submitButton = document.querySelector('button[type="submit"]');
    const submitIcon = submitButton.querySelector('i');

    if (testMode.checked) {
        // 测试模式：隐藏支付选项，修改按钮文本
        if (paymentSection) {
            paymentSection.style.display = 'none';
        }
        submitButton.innerHTML = '<i class="bi bi-bug"></i> 测试分配节点';
        submitButton.className = 'btn btn-warning btn-lg';
    } else {
        // 正常模式：显示支付选项，恢复按钮文本
        if (paymentSection) {
            paymentSection.style.display = 'block';
        }
        {% if product.price == 0 %}
        submitButton.innerHTML = '<i class="bi bi-credit-card"></i> 立即获取';
        {% else %}
        submitButton.innerHTML = '<i class="bi bi-credit-card"></i> 立即购买';
        {% endif %}
        submitButton.className = 'btn btn-primary btn-lg';
    }
}
</script>
