{% extends "base.html" %}

{% block title %}商店 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-shop"></i> 节点套餐
            </h1>
            <p class="text-muted mb-5">选择最适合您的套餐方案，享受高速稳定的网络服务</p>
        </div>
    </div>

    <div class="row">
        {% for product in products %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card product-card h-100 border-0 shadow">
                {% if product.price == 0 %}
                <div class="card-header bg-success text-white text-center">
                    <i class="bi bi-gift"></i> 免费体验
                </div>
                {% elif product.product_type.value == 'yearly' %}
                <div class="card-header bg-warning text-dark text-center">
                    <i class="bi bi-star"></i> 最优惠
                </div>
                {% elif product.product_type.value == 'monthly' %}
                <div class="card-header bg-info text-white text-center">
                    <i class="bi bi-heart"></i> 热门推荐
                </div>
                {% endif %}
                
                <div class="card-body text-center">
                    <h4 class="card-title">{{ product.name }}</h4>
                    <p class="card-text text-muted">{{ product.description }}</p>
                    
                    <div class="price mb-4">
                        {% if product.price == 0 %}
                            <span class="h2 text-success">免费</span>
                        {% else %}
                            <span class="h2 text-primary">¥{{ "%.1f"|format(product.price) }}</span>
                            <small class="text-muted">
                                {% if product.product_type.value == 'monthly' %}
                                    /月
                                {% elif product.product_type.value == 'quarterly' %}
                                    /季
                                {% elif product.product_type.value == 'yearly' %}
                                    /年
                                {% endif %}
                            </small>
                        {% endif %}
                    </div>
                    
                    <ul class="list-unstyled mb-4">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <strong>{{ product.duration_days }}</strong> 天有效期
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <strong>{{ product.traffic_limit_gb }}GB</strong> 流量
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            <strong>{{ product.node_type.value.upper() }}</strong> 协议
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            多设备同时使用
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success"></i>
                            24/7 技术支持
                        </li>
                    </ul>
                    
                    {% if product.stock_count == 0 %}
                        <button class="btn btn-secondary btn-lg w-100" disabled>
                            <i class="bi bi-x-circle"></i> 暂时缺货
                        </button>
                    {% else %}
                        <a href="{{ url_for('buy_product', product_id=product.id) }}" class="btn btn-primary btn-lg w-100">
                            <i class="bi bi-cart-plus"></i> 立即购买
                        </a>
                    {% endif %}
                </div>
                
                <div class="card-footer bg-transparent text-center">
                    <small class="text-muted">
                        {% if product.stock_count == -1 %}
                            库存充足
                        {% elif product.stock_count > 0 %}
                            剩余 {{ product.stock_count }} 份
                        {% endif %}
                        | 已售 {{ product.sold_count }} 份
                    </small>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% if not products %}
    <div class="row">
        <div class="col-12 text-center py-5">
            <i class="bi bi-box text-muted" style="font-size: 4rem;"></i>
            <h3 class="text-muted mt-3">暂无商品</h3>
            <p class="text-muted">请稍后再来查看</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- 购买须知 -->
<section class="bg-light py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="text-center mb-4">购买须知</h3>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <h5><i class="bi bi-info-circle text-info"></i> 服务说明</h5>
                        <ul class="list-unstyled">
                            <li>• 购买后立即生效，配置信息将发送至您的邮箱</li>
                            <li>• 支持Windows、Mac、iOS、Android等多平台</li>
                            <li>• 流量用完或到期后服务自动停止</li>
                            <li>• 不支持P2P下载和违法用途</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <h5><i class="bi bi-shield-check text-success"></i> 售后保障</h5>
                        <ul class="list-unstyled">
                            <li>• 7x24小时技术支持</li>
                            <li>• 99.9%服务可用性保证</li>
                            <li>• 不满意7天内可申请退款</li>
                            <li>• 提供详细使用教程和客户端下载</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
