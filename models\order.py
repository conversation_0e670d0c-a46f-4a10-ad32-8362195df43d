"""
订单和节点配置相关模型
"""
from datetime import datetime
from . import db
from .enums import OrderStatus, NodeType


class Order(db.Model):
    """订单模型"""
    __tablename__ = 'orders'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.String(50), unique=True, nullable=False, index=True)

    # 关联用户和产品
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 可选，支持游客购买
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=True)  # 可选，支持自定义订单

    # 客户信息
    customer_email = db.Column(db.String(120), nullable=False, index=True)
    customer_name = db.Column(db.String(100), nullable=True)

    # 产品配置（从产品复制或自定义）
    node_type = db.Column(db.Enum(NodeType), nullable=False, default=NodeType.VLESS)
    duration_days = db.Column(db.Integer, nullable=False, default=30)  # 使用天数
    traffic_limit_gb = db.Column(db.Integer, nullable=False, default=100)  # 流量限制GB
    price = db.Column(db.Float, nullable=False)

    # 订单状态
    status = db.Column(db.Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING)
    payment_method = db.Column(db.String(50), nullable=True)
    payment_id = db.Column(db.String(100), nullable=True)

    # 订单类型
    order_type = db.Column(db.String(20), nullable=False, default='purchase')  # purchase: 新购, renewal: 续费
    parent_subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=True)  # 续费时关联的原订阅
    
    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=True)  # 节点过期时间

    # Coupon related fields
    applied_coupon_code = db.Column(db.String(80), nullable=True)
    discount_amount = db.Column(db.Float, nullable=True, default=0.0)
    
    # 备注和额外信息
    notes = db.Column(db.Text, nullable=True)
    customer_remarks = db.Column(db.String(200), nullable=True)  # 客户备注名
    
    # 关联的节点配置
    node_configs = db.relationship('NodeConfig', backref='order', lazy=True, cascade='all, delete-orphan')

    # 续费订单关联的原订阅
    parent_subscription = db.relationship('Subscription', foreign_keys=[parent_subscription_id], lazy=True)
    
    def __repr__(self):
        return f'<Order {self.order_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'customer_email': self.customer_email,
            'customer_name': self.customer_name,
            'node_type': self.node_type.value if self.node_type else None,
            'duration_days': self.duration_days,
            'traffic_limit_gb': self.traffic_limit_gb,
            'price': self.price,
            'status': self.status.value if self.status else None,
            'payment_method': self.payment_method,
            'payment_id': self.payment_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'notes': self.notes,
            'customer_remarks': self.customer_remarks,
            'applied_coupon_code': self.applied_coupon_code,
            'discount_amount': self.discount_amount,
            'node_configs': [config.to_dict() for config in self.node_configs]
        }


class NodeConfig(db.Model):
    """节点配置模型"""
    __tablename__ = 'node_configs'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)

    # X-UI 相关信息
    xui_inbound_id = db.Column(db.Integer, nullable=True)  # X-UI中的inbound ID
    client_id = db.Column(db.String(100), nullable=False)  # 客户端ID (UUID)
    client_email = db.Column(db.String(120), nullable=False)  # 客户端邮箱

    # 节点配置信息
    server_address = db.Column(db.String(100), nullable=False)
    server_port = db.Column(db.Integer, nullable=False)
    protocol = db.Column(db.String(20), nullable=False, default='vless')
    transport = db.Column(db.String(20), nullable=False, default='tcp')
    security = db.Column(db.String(20), nullable=False, default='none')

    # 协议模板相关
    protocol_template_id = db.Column(db.Integer, db.ForeignKey('protocol_templates.id'), nullable=True)
    custom_variables = db.Column(db.Text, nullable=True)  # JSON格式的节点级自定义变量

    # 流量统计
    total_traffic_gb = db.Column(db.Float, nullable=False, default=0)  # 总流量使用
    upload_traffic_gb = db.Column(db.Float, nullable=False, default=0)  # 上传流量
    download_traffic_gb = db.Column(db.Float, nullable=False, default=0)  # 下载流量

    # 状态和时间
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    last_traffic_update = db.Column(db.DateTime, nullable=True)

    # 配置字符串
    vless_config = db.Column(db.Text, nullable=True)  # 完整的vless配置字符串（兼容性保留）
    generated_config = db.Column(db.Text, nullable=True)  # 使用模板生成的配置字符串
    
    def __repr__(self):
        return f'<NodeConfig {self.client_email}>'
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'xui_inbound_id': self.xui_inbound_id,
            'client_id': self.client_id,
            'client_email': self.client_email,
            'server_address': self.server_address,
            'server_port': self.server_port,
            'protocol': self.protocol,
            'transport': self.transport,
            'security': self.security,
            'total_traffic_gb': self.total_traffic_gb,
            'upload_traffic_gb': self.upload_traffic_gb,
            'download_traffic_gb': self.download_traffic_gb,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_traffic_update': self.last_traffic_update.isoformat() if self.last_traffic_update else None,
            'vless_config': self.vless_config
        }
