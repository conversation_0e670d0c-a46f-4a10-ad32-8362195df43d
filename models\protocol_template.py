"""
协议模板模型 - 管理员自定义协议格式模板
"""
from datetime import datetime
from models import db


class ProtocolTemplate(db.Model):
    """协议模板模型"""
    __tablename__ = 'protocol_templates'

    id = db.Column(db.Integer, primary_key=True)
    
    # 模板基本信息
    name = db.Column(db.String(100), nullable=False)  # 模板名称
    description = db.Column(db.Text, nullable=True)   # 模板描述
    protocol_type = db.Column(db.String(20), nullable=False)  # 协议类型：vless, vmess, trojan, ss
    
    # 模板内容（base64解码后的原始格式）
    template_content = db.Column(db.Text, nullable=False)  # 原始模板内容
    
    # 变量定义（JSON格式）
    custom_variables = db.Column(db.Text, nullable=True)  # 自定义变量定义和默认值
    
    # 模板状态
    is_active = db.Column(db.<PERSON>, nullable=False, default=True)
    is_default = db.Column(db.<PERSON><PERSON><PERSON>, nullable=False, default=False)  # 是否为该协议的默认模板
    
    # 使用统计
    usage_count = db.Column(db.Integer, nullable=False, default=0)  # 使用次数
    
    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # 创建者
    
    # 关联关系
    node_configs = db.relationship('NodeConfig', backref='protocol_template', lazy=True)
    
    def __repr__(self):
        return f'<ProtocolTemplate {self.name}({self.protocol_type})>'
    
    def to_dict(self):
        """转换为字典格式"""
        import json
        
        custom_vars = {}
        if self.custom_variables:
            try:
                custom_vars = json.loads(self.custom_variables)
            except:
                custom_vars = {}
        
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'protocol_type': self.protocol_type,
            'template_content': self.template_content,
            'custom_variables': custom_vars,
            'is_active': self.is_active,
            'is_default': self.is_default,
            'usage_count': self.usage_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_default_template(cls, protocol_type: str):
        """获取指定协议的默认模板"""
        return cls.query.filter_by(
            protocol_type=protocol_type,
            is_default=True,
            is_active=True
        ).first()
    
    @classmethod
    def get_active_templates(cls, protocol_type: str = None):
        """获取活跃的模板"""
        query = cls.query.filter_by(is_active=True)
        if protocol_type:
            query = query.filter_by(protocol_type=protocol_type)
        return query.order_by(cls.is_default.desc(), cls.name.asc()).all()
    
    @classmethod
    def create_default_templates(cls):
        """创建默认协议模板"""
        default_templates = [
            {
                'name': 'VLESS默认模板',
                'description': 'VLESS协议的默认配置模板',
                'protocol_type': 'vless',
                'template_content': 'vless://{uuid}@{server}:{port}?type={network}&security={security}&path={path}&host={host}#{remark}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': True
            },
            {
                'name': 'VMess默认模板', 
                'description': 'VMess协议的默认配置模板',
                'protocol_type': 'vmess',
                'template_content': 'vmess://{vmess_config_base64}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': True
            },
            {
                'name': 'Trojan默认模板',
                'description': 'Trojan协议的默认配置模板', 
                'protocol_type': 'trojan',
                'template_content': 'trojan://{password}@{server}:{port}?security=tls&sni={sni}#{remark}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': True
            },
            {
                'name': 'Shadowsocks默认模板',
                'description': 'Shadowsocks协议的默认配置模板',
                'protocol_type': 'shadowsocks', 
                'template_content': 'ss://{method_password_base64}@{server}:{port}#{remark}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': True
            }
        ]
        
        for template_data in default_templates:
            existing = cls.query.filter_by(
                protocol_type=template_data['protocol_type'],
                is_default=True
            ).first()
            
            if not existing:
                template = cls(**template_data)
                db.session.add(template)
        
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e


class ProtocolVariable(db.Model):
    """协议变量模型 - 存储不同层级的变量配置"""
    __tablename__ = 'protocol_variables'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # 变量作用域
    scope_type = db.Column(db.String(20), nullable=False)  # panel, product, template, node
    scope_id = db.Column(db.Integer, nullable=False)       # 对应的ID
    
    # 变量内容（JSON格式）
    variables = db.Column(db.Text, nullable=False)  # 变量键值对
    
    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f'<ProtocolVariable {self.scope_type}:{self.scope_id}>'
    
    def to_dict(self):
        """转换为字典格式"""
        import json
        
        variables = {}
        if self.variables:
            try:
                variables = json.loads(self.variables)
            except:
                variables = {}
        
        return {
            'id': self.id,
            'scope_type': self.scope_type,
            'scope_id': self.scope_id,
            'variables': variables,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def get_variables(cls, scope_type: str, scope_id: int):
        """获取指定作用域的变量"""
        record = cls.query.filter_by(scope_type=scope_type, scope_id=scope_id).first()
        if record:
            import json
            try:
                return json.loads(record.variables)
            except:
                return {}
        return {}
    
    @classmethod
    def set_variables(cls, scope_type: str, scope_id: int, variables: dict):
        """设置指定作用域的变量"""
        import json
        
        record = cls.query.filter_by(scope_type=scope_type, scope_id=scope_id).first()
        if record:
            record.variables = json.dumps(variables, ensure_ascii=False)
            record.updated_at = datetime.utcnow()
        else:
            record = cls(
                scope_type=scope_type,
                scope_id=scope_id,
                variables=json.dumps(variables, ensure_ascii=False)
            )
            db.session.add(record)
        
        try:
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            return False
