#!/usr/bin/env python3
"""
初始化协议模板 - 确保默认模板存在
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db
from models.protocol_template import ProtocolTemplate

def init_protocol_templates():
    """初始化协议模板"""
    app = create_app()
    
    with app.app_context():
        try:
            # 检查是否已有模板
            existing_templates = ProtocolTemplate.query.count()
            
            if existing_templates > 0:
                print(f"✅ 已存在 {existing_templates} 个协议模板")
                return True
            
            print("🔄 开始创建默认协议模板...")
            
            # 创建默认模板
            ProtocolTemplate.create_default_templates()
            
            # 验证创建结果
            created_templates = ProtocolTemplate.query.count()
            print(f"✅ 成功创建 {created_templates} 个默认协议模板")
            
            # 显示创建的模板
            templates = ProtocolTemplate.query.all()
            for template in templates:
                status = "默认" if template.is_default else "普通"
                print(f"  - {template.name} ({template.protocol_type.upper()}) - {status}")
            
            return True
            
        except Exception as e:
            print(f"❌ 初始化协议模板失败: {e}")
            return False

if __name__ == '__main__':
    success = init_protocol_templates()
    sys.exit(0 if success else 1)
