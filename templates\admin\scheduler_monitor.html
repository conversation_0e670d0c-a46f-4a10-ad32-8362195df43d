{% extends "admin/base.html" %}

{% block title %}定时任务监控 - 管理后台{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-clock"></i> 定时任务监控
                    </h4>
                </div>
                <div class="card-body">
                    <!-- 任务状态概览 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">流量统计任务</h6>
                                            <p class="card-text" id="traffic-status">检查中...</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-graph-up fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">订阅清理任务</h6>
                                            <p class="card-text" id="cleanup-status">检查中...</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-trash fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">验证码清理</h6>
                                            <p class="card-text" id="verification-status">检查中...</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-shield-check fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h6 class="card-title">通知任务</h6>
                                            <p class="card-text" id="notification-status">检查中...</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-bell fs-2"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 手动操作按钮 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>手动操作</h5>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary" onclick="triggerTrafficCollection()">
                                    <i class="bi bi-play-circle"></i> 立即收集流量统计
                                </button>
                                <button type="button" class="btn btn-success" onclick="triggerCleanup()">
                                    <i class="bi bi-trash"></i> 清理过期数据
                                </button>
                                <button type="button" class="btn btn-warning" onclick="cleanupVerificationCodes()">
                                    <i class="bi bi-shield-x"></i> 清理验证码
                                </button>
                                <button type="button" class="btn btn-info" onclick="refreshStatus()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新状态
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 任务执行日志 -->
                    <div class="row">
                        <div class="col-12">
                            <h5>任务执行日志</h5>
                            <div class="card">
                                <div class="card-body">
                                    <div id="task-logs" style="height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; font-family: monospace;">
                                        <div class="text-muted">加载日志中...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 操作结果模态框 -->
<div class="modal fade" id="resultModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resultModalTitle">操作结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultModalBody">
                <!-- 结果内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let logUpdateInterval;

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    refreshStatus();
    startLogUpdates();
});

// 刷新任务状态
function refreshStatus() {
    // 这里可以添加API调用来获取实际的任务状态
    document.getElementById('traffic-status').textContent = '每10分钟执行';
    document.getElementById('cleanup-status').textContent = '每天凌晨2点';
    document.getElementById('verification-status').textContent = '每天凌晨3点';
    document.getElementById('notification-status').textContent = '每天上午9点';
}

// 触发流量统计收集
function triggerTrafficCollection() {
    showLoading('正在触发流量统计收集...');
    
    fetch('/api/verification/traffic/collect', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        showResult('流量统计收集', data.success ? '成功' : '失败', data.message);
        if (data.success) {
            addLog('手动触发流量统计收集成功');
        } else {
            addLog('手动触发流量统计收集失败: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        showResult('流量统计收集', '错误', '网络请求失败: ' + error.message);
        addLog('手动触发流量统计收集错误: ' + error.message);
    });
}

// 触发清理操作
function triggerCleanup() {
    showLoading('正在清理过期数据...');
    
    // 这里可以添加清理过期数据的API调用
    setTimeout(() => {
        hideLoading();
        showResult('数据清理', '成功', '过期数据清理完成');
        addLog('手动触发数据清理成功');
    }, 2000);
}

// 清理验证码
function cleanupVerificationCodes() {
    showLoading('正在清理验证码...');
    
    fetch('/api/verification/cleanup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        showResult('验证码清理', data.success ? '成功' : '失败', 
                  data.success ? `清理了 ${data.data.captcha_cleaned} 个图形验证码，${data.data.email_cleaned} 个邮件验证码` : data.message);
        if (data.success) {
            addLog(`验证码清理成功: 图形验证码 ${data.data.captcha_cleaned} 个，邮件验证码 ${data.data.email_cleaned} 个`);
        } else {
            addLog('验证码清理失败: ' + data.message);
        }
    })
    .catch(error => {
        hideLoading();
        showResult('验证码清理', '错误', '网络请求失败: ' + error.message);
        addLog('验证码清理错误: ' + error.message);
    });
}

// 显示加载状态
function showLoading(message) {
    // 可以添加加载指示器
    console.log(message);
}

// 隐藏加载状态
function hideLoading() {
    // 隐藏加载指示器
}

// 显示操作结果
function showResult(title, status, message) {
    document.getElementById('resultModalTitle').textContent = title + ' - ' + status;
    document.getElementById('resultModalBody').innerHTML = `
        <div class="alert alert-${status === '成功' ? 'success' : 'danger'}" role="alert">
            ${message}
        </div>
    `;
    
    const modal = new bootstrap.Modal(document.getElementById('resultModal'));
    modal.show();
}

// 添加日志条目
function addLog(message) {
    const logsContainer = document.getElementById('task-logs');
    const timestamp = new Date().toLocaleString();
    const logEntry = document.createElement('div');
    logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> ${message}`;
    
    logsContainer.insertBefore(logEntry, logsContainer.firstChild);
    
    // 限制日志条目数量
    const logEntries = logsContainer.children;
    if (logEntries.length > 100) {
        logsContainer.removeChild(logEntries[logEntries.length - 1]);
    }
}

// 开始日志更新
function startLogUpdates() {
    // 初始化一些示例日志
    addLog('定时任务监控页面已加载');
    addLog('系统运行正常');
    
    // 可以添加定期更新日志的逻辑
    logUpdateInterval = setInterval(() => {
        // 这里可以添加获取最新日志的API调用
    }, 30000); // 每30秒更新一次
}

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (logUpdateInterval) {
        clearInterval(logUpdateInterval);
    }
});
</script>
{% endblock %}
