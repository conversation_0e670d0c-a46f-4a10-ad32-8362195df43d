{% extends "base.html" %}

{% block title %}个人资料 - 节点商城{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">
                        <i class="bi bi-person-gear text-primary me-2"></i>
                        个人资料
                    </h2>
                    <p class="text-muted mb-0">管理您的账户信息和安全设置</p>
                </div>
                <div>
                    <a href="{{ url_for('user.dashboard') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-1"></i> 返回中心
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 基本信息 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-person text-primary me-2"></i>
                        基本信息
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('user.update_profile') }}">
                        <div class="mb-3">
                            <label for="username" class="form-label fw-semibold">用户名</label>
                            <input type="text" class="form-control" id="username" value="{{ user.username }}" readonly>
                            <small class="text-muted">用户名不可修改</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label fw-semibold">邮箱地址</label>
                            <input type="email" class="form-control" id="email" value="{{ user.email }}" readonly>
                            <small class="text-muted">邮箱地址不可修改</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label fw-semibold">真实姓名</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   value="{{ user.full_name or '' }}" placeholder="请输入您的真实姓名">
                        </div>
                        
                        <div class="mb-4">
                            <label for="phone" class="form-label fw-semibold">手机号码</label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   value="{{ user.phone or '' }}" placeholder="请输入您的手机号码">
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle me-1"></i> 保存修改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 账户安全 -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-shield-lock text-success me-2"></i>
                        账户安全
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('user.change_password') }}">
                        <div class="mb-3">
                            <label for="current_password" class="form-label fw-semibold">当前密码</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="current_password" 
                                       name="current_password" placeholder="请输入当前密码" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="new_password" class="form-label fw-semibold">新密码</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="new_password" 
                                       name="new_password" placeholder="请输入新密码" required minlength="6">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <small class="text-muted">密码长度至少6位</small>
                        </div>
                        
                        <div class="mb-4">
                            <label for="confirm_password" class="form-label fw-semibold">确认新密码</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" 
                                       name="confirm_password" placeholder="请再次输入新密码" required minlength="6">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning">
                                <i class="bi bi-key me-1"></i> 修改密码
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 账户统计 -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-graph-up text-info me-2"></i>
                        账户统计
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-person-check text-primary" style="font-size: 1.5rem;"></i>
                                </div>
                                <h6 class="fw-bold">账户状态</h6>
                                <span class="badge bg-{{ 'success' if user.is_active else 'danger' }} px-3 py-2">
                                    {{ '正常' if user.is_active else '已禁用' }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-calendar-check text-success" style="font-size: 1.5rem;"></i>
                                </div>
                                <h6 class="fw-bold">注册时间</h6>
                                <p class="text-muted mb-0">{{ user.created_at.strftime('%Y-%m-%d') }}</p>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-clock-history text-info" style="font-size: 1.5rem;"></i>
                                </div>
                                <h6 class="fw-bold">最后登录</h6>
                                <p class="text-muted mb-0">
                                    {% if user.last_login %}
                                        {{ user.last_login.strftime('%Y-%m-%d %H:%M') }}
                                    {% else %}
                                        从未登录
                                    {% endif %}
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-2" style="width: 60px; height: 60px;">
                                    <i class="bi bi-shield-check text-warning" style="font-size: 1.5rem;"></i>
                                </div>
                                <h6 class="fw-bold">用户角色</h6>
                                <span class="badge bg-{{ 'danger' if user.role.value == 'admin' else 'primary' }} px-3 py-2">
                                    {{ '管理员' if user.role.value == 'admin' else '普通用户' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 密码显示/隐藏切换
    function togglePassword(inputId) {
        const passwordInput = document.getElementById(inputId);
        const icon = event.target.closest('button').querySelector('i');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'bi bi-eye';
        }
    }
    
    // 密码确认验证
    document.getElementById('confirm_password').addEventListener('input', function() {
        const newPassword = document.getElementById('new_password').value;
        const confirmPassword = this.value;
        
        if (confirmPassword && newPassword !== confirmPassword) {
            this.setCustomValidity('两次输入的密码不一致');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // 新密码输入时也要重新验证确认密码
    document.getElementById('new_password').addEventListener('input', function() {
        const confirmPassword = document.getElementById('confirm_password');
        if (confirmPassword.value) {
            confirmPassword.dispatchEvent(new Event('input'));
        }
    });
</script>
{% endblock %}
