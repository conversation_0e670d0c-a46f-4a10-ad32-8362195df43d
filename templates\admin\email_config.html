{% extends "base.html" %}

{% block title %}邮件配置 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-envelope-gear"></i> 邮件配置管理
                </h1>
                <div>
                    {% if config %}
                    <button id="test-email-btn" class="btn btn-outline-info me-2">
                        <i class="bi bi-send-check"></i> 发送测试邮件
                    </button>
                    <button id="test-connection-btn" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-wifi"></i> 测试连接
                    </button>
                    <button id="edit-config-btn" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> 编辑配置
                    </button>
                    {% else %}
                    <button id="add-config-btn" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 添加邮件配置
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 邮件配置显示 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="bi bi-envelope-gear"></i> 邮件配置
                    </h5>
                </div>
                <div class="card-body">
                    {% if config %}
                    <!-- 显示当前配置 -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted">基本信息</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td class="text-muted" style="width: 120px;">配置名称:</td>
                                    <td><strong>{{ config.name }}</strong></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">SMTP服务器:</td>
                                    <td>{{ config.smtp_server }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">端口:</td>
                                    <td>
                                        {{ config.smtp_port }}
                                        {% if config.use_tls %}
                                        <small class="text-success">(TLS)</small>
                                        {% elif config.use_ssl %}
                                        <small class="text-info">(SSL)</small>
                                        {% else %}
                                        <small class="text-muted">(无加密)</small>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">用户名:</td>
                                    <td>{{ config.username }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">发送者:</td>
                                    <td>{{ config.sender_name }} &lt;{{ config.default_sender or config.username }}&gt;</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">状态:</td>
                                    <td>
                                        {% if config.is_active %}
                                        <span class="badge bg-success">启用</span>
                                        {% else %}
                                        <span class="badge bg-secondary">禁用</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">统计信息</h6>
                            <table class="table table-sm table-borderless">
                                <tr>
                                    <td class="text-muted" style="width: 120px;">成功发送:</td>
                                    <td><span class="text-success">{{ config.total_sent or 0 }}</span></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">发送失败:</td>
                                    <td><span class="text-danger">{{ config.total_failed or 0 }}</span></td>
                                </tr>
                                <tr>
                                    <td class="text-muted">每日限制:</td>
                                    <td>{{ config.daily_limit }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">每分钟限制:</td>
                                    <td>{{ config.rate_limit_per_minute }}</td>
                                </tr>
                                <tr>
                                    <td class="text-muted">最后使用:</td>
                                    <td>
                                        {% if config.last_used_at %}
                                        {{ config.last_used_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                        <span class="text-muted">从未使用</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-muted">创建时间:</td>
                                    <td>{{ config.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    {% else %}
                    <!-- 无配置时的显示 -->
                    <div class="text-center py-5">
                        <i class="bi bi-envelope-x text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">暂无邮件配置</h5>
                        <p class="text-muted">请添加邮件配置以启用邮件功能</p>
                        <button id="add-config-btn-center" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> 添加邮件配置
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 邮件统计 -->
    {% if stats %}
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-bar-chart"></i> 发送统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-success">{{ stats.total_sent or 0 }}</h4>
                            <small class="text-muted">总发送数</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-danger">{{ stats.total_failed or 0 }}</h4>
                            <small class="text-muted">失败数</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-shield-check"></i> 验证码统计
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="text-primary">{{ stats.verification_stats.today_sent or 0 }}</h4>
                            <small class="text-muted">今日验证码</small>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">{{ stats.verification_stats.success_rate or 0 }}%</h4>
                            <small class="text-muted">验证成功率</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- 邮件配置模态框 -->
<div class="modal fade" id="emailConfigModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-envelope-gear"></i>
                    <span id="modal-title">邮件配置</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="emailConfigForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="config-name" class="form-label">配置名称 *</label>
                                <input type="text" class="form-control" id="config-name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp-server" class="form-label">SMTP服务器 *</label>
                                <input type="text" class="form-control" id="smtp-server" name="smtp_server" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp-port" class="form-label">端口 *</label>
                                <input type="number" class="form-control" id="smtp-port" name="smtp_port" value="587" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">加密方式</label>
                                <div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="encryption" id="use-tls" value="tls" checked>
                                        <label class="form-check-label" for="use-tls">TLS</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="encryption" id="use-ssl" value="ssl">
                                        <label class="form-check-label" for="use-ssl">SSL</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="encryption" id="no-encryption" value="none">
                                        <label class="form-check-label" for="no-encryption">无</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名 *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <input type="password" class="form-control" id="password" name="password">
                                <small class="text-muted" id="password-help">编辑时留空表示不修改密码</small>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default-sender" class="form-label">发送者邮箱</label>
                                <input type="email" class="form-control" id="default-sender" name="default_sender">
                                <small class="text-muted">留空则使用用户名</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sender-name" class="form-label">发送者名称</label>
                                <input type="text" class="form-control" id="sender-name" name="sender_name" value="系统通知">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="daily-limit" class="form-label">每日发送限制</label>
                                <input type="number" class="form-control" id="daily-limit" name="daily_limit" value="1000">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="rate-limit" class="form-label">每分钟发送限制</label>
                                <input type="number" class="form-control" id="rate-limit" name="rate_limit_per_minute" value="10">
                            </div>
                        </div>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="is-active" name="is_active" checked>
                        <label class="form-check-label" for="is-active">启用此配置</label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" id="test-config-modal-btn" class="btn btn-outline-info">测试连接</button>
                    <button type="submit" class="btn btn-primary">保存配置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 测试邮件模态框 -->
<div class="modal fade" id="testEmailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-send-check"></i> 发送测试邮件
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="testEmailForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="test-email" class="form-label">收件人邮箱 *</label>
                        <input type="email" class="form-control" id="test-email" name="test_email" required>
                    </div>
                    <div class="mb-3">
                        <label for="test-subject" class="form-label">邮件主题</label>
                        <input type="text" class="form-control" id="test-subject" name="test_subject" value="邮件配置测试">
                    </div>
                    <div class="mb-3">
                        <label for="test-content" class="form-label">邮件内容</label>
                        <textarea class="form-control" id="test-content" name="test_content" rows="3">这是一封测试邮件，用于验证邮件配置是否正常工作。</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">发送测试邮件</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
$(document).ready(function() {
    console.log('邮件配置页面JavaScript已加载');

    // 邮件配置表单提交
    $('#emailConfigForm').on('submit', function(e) {
        e.preventDefault();
        console.log('提交邮件配置表单');

        const formData = new FormData(this);

        // 验证必填字段
        const name = formData.get('name');
        const smtp_server = formData.get('smtp_server');
        const username = formData.get('username');

        if (!name || !smtp_server || !username) {
            showAlert('danger', '请填写所有必需字段');
            return;
        }

        // 检查是否是新建配置（需要密码）
        const isNewConfig = !$('#modal-title').text().includes('编辑');
        const password = formData.get('password');

        if (isNewConfig && !password) {
            showAlert('danger', '新建配置时密码为必填项');
            return;
        }

        $.ajax({
            url: '/admin/api/email-config',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('配置保存响应:', response);
                if (response.success) {
                    showAlert('success', response.message || '配置保存成功');
                    $('#emailConfigModal').modal('hide');
                    setTimeout(() => location.reload(), 1000);
                } else {
                    showAlert('danger', response.message || '保存失败');
                }
            },
            error: function(xhr) {
                console.error('配置保存失败:', xhr);
                const response = xhr.responseJSON || {};
                showAlert('danger', response.message || '保存失败');
            }
        });
    });

    // 添加配置按钮
    $('#add-config-btn, #add-config-btn-center').on('click', function() {
        console.log('点击添加配置按钮');
        $('#modal-title').text('添加邮件配置');
        $('#emailConfigForm')[0].reset();
        $('#password').prop('required', true);
        $('#password-help').text('新建配置时密码为必填项');
        $('#emailConfigModal').modal('show');
    });

    // 编辑配置按钮
    $('#edit-config-btn').on('click', function() {
        console.log('点击编辑配置按钮');

        $.ajax({
            url: '/admin/api/email-config',
            method: 'GET',
            success: function(response) {
                console.log('获取配置响应:', response);
                if (response.success && response.data) {
                    const config = response.data;

                    $('#modal-title').text('编辑邮件配置');
                    $('#config-name').val(config.name);
                    $('#smtp-server').val(config.smtp_server);
                    $('#smtp-port').val(config.smtp_port);
                    $('#username').val(config.username);
                    $('#password').val(''); // 不显示密码
                    $('#password').prop('required', false);
                    $('#password-help').text('编辑时留空表示不修改密码');
                    $('#default-sender').val(config.default_sender);
                    $('#sender-name').val(config.sender_name);
                    $('#daily-limit').val(config.daily_limit);
                    $('#rate-limit').val(config.rate_limit_per_minute);
                    $('#is-active').prop('checked', config.is_active);

                    // 设置加密方式
                    if (config.use_tls) {
                        $('#use-tls').prop('checked', true);
                    } else if (config.use_ssl) {
                        $('#use-ssl').prop('checked', true);
                    } else {
                        $('#no-encryption').prop('checked', true);
                    }

                    $('#emailConfigModal').modal('show');
                } else {
                    showAlert('danger', response.message || '获取配置失败');
                }
            },
            error: function(xhr) {
                console.error('获取配置失败:', xhr);
                showAlert('danger', '获取配置失败');
            }
        });
    });

    // 测试配置连接（模态框内）
    $('#test-config-modal-btn').on('click', function() {
        console.log('点击模态框内测试连接按钮');
        const formData = new FormData($('#emailConfigForm')[0]);

        // 验证必填字段
        const smtp_server = formData.get('smtp_server');
        const username = formData.get('username');
        const password = formData.get('password');

        if (!smtp_server || !username) {
            showAlert('danger', '请填写SMTP服务器和用户名');
            return;
        }

        if (!password) {
            showAlert('warning', '未提供密码，将尝试使用已保存的密码');
        }

        $.ajax({
            url: '/admin/api/email-config/test',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('连接测试响应:', response);
                if (response.success) {
                    showAlert('success', response.message || '连接测试成功');
                } else {
                    showAlert('danger', response.message || '连接测试失败');
                }
            },
            error: function(xhr) {
                console.error('连接测试失败:', xhr);
                const response = xhr.responseJSON || {};
                let message = response.message || '连接测试失败';
                if (response.diagnostic) {
                    message += '<br><small class="text-muted">诊断建议: ' + response.diagnostic + '</small>';
                }
                showAlert('danger', message);
            }
        });
    });

    // 测试连接按钮（页面上）
    $('#test-connection-btn').on('click', function() {
        console.log('点击页面测试连接按钮');

        $.ajax({
            url: '/admin/api/email-config',
            method: 'GET',
            success: function(response) {
                if (response.success && response.data) {
                    const config = response.data;

                    // 使用现有配置进行测试
                    const testData = new FormData();
                    testData.append('smtp_server', config.smtp_server);
                    testData.append('smtp_port', config.smtp_port);
                    testData.append('username', config.username);
                    testData.append('password', 'test'); // 占位符，后端会使用存储的密码
                    testData.append('encryption', config.use_tls ? 'tls' : (config.use_ssl ? 'ssl' : 'none'));

                    $.ajax({
                        url: '/admin/api/email-config/test',
                        method: 'POST',
                        data: testData,
                        processData: false,
                        contentType: false,
                        success: function(testResponse) {
                            console.log('连接测试响应:', testResponse);
                            if (testResponse.success) {
                                showAlert('success', testResponse.message || '连接测试成功');
                            } else {
                                showAlert('warning', testResponse.message || '连接测试失败');
                            }
                        },
                        error: function(xhr) {
                            console.error('连接测试失败:', xhr);
                            const errorResponse = xhr.responseJSON || {};
                            let message = errorResponse.message || '连接测试失败';
                            if (errorResponse.diagnostic) {
                                message += '<br><small class="text-muted">诊断建议: ' + errorResponse.diagnostic + '</small>';
                            }
                            showAlert('danger', message);
                        }
                    });
                } else {
                    showAlert('danger', '无法获取配置信息');
                }
            },
            error: function(xhr) {
                console.error('获取配置失败:', xhr);
                showAlert('danger', '获取配置失败');
            }
        });
    });

    // 测试邮件发送
    $('#testEmailForm').on('submit', function(e) {
        e.preventDefault();
        console.log('提交测试邮件表单');

        const formData = new FormData(this);

        $.ajax({
            url: '/admin/api/email-config/send-test',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('测试邮件响应:', response);
                if (response.success) {
                    showAlert('success', response.message || '测试邮件发送成功');
                    $('#testEmailModal').modal('hide');
                } else {
                    showAlert('danger', response.message || '测试邮件发送失败');
                }
            },
            error: function(xhr) {
                console.error('测试邮件发送失败:', xhr);
                const response = xhr.responseJSON || {};
                let message = response.message || '测试邮件发送失败';
                if (response.diagnostic) {
                    message += '<br><small class="text-muted">诊断建议: ' + response.diagnostic + '</small>';
                }
                showAlert('danger', message);
            }
        });
    });

    // 发送测试邮件按钮
    $('#test-email-btn').on('click', function() {
        console.log('点击发送测试邮件按钮');
        $('#testEmailModal').modal('show');
    });

    // 清空模态框
    $('#emailConfigModal').on('hidden.bs.modal', function() {
        console.log('邮件配置模态框已关闭');
        $('#emailConfigForm')[0].reset();
        $('#modal-title').text('添加邮件配置');
        $('#password').prop('required', true);
        $('#password-help').text('新建配置时密码为必填项');
    });
});

function showAlert(type, message) {
    console.log(`显示提示: ${type} - ${message}`);
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('.container').first().prepend(alertHtml);

    // 自动消失
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// 页面加载完成后的调试信息
$(window).on('load', function() {
    console.log('页面完全加载完成');
    console.log('jQuery版本:', $.fn.jquery);
    console.log('Bootstrap模态框可用:', typeof $.fn.modal !== 'undefined');
});
</script>
{% endblock %}
