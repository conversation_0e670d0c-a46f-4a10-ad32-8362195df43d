import unittest
import tempfile
import os
from flask import Flask, session
from models import db, User, Product, Coupon, Order, UserRole
from app import create_app # Assuming your app factory is in app.py

class CouponTestCase(unittest.TestCase):

    def setUp(self):
        """Set up a blank temp database before each test"""
        self.app = create_app('testing') # Use a testing configuration
        self.client = self.app.test_client()

        with self.app.app_context():
            db.create_all()
            self.create_admin_user() # Helper to create an admin for tests requiring login

    def tearDown(self):
        """Destroy blank temp database after each test"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()

    def create_admin_user(self):
        admin_user = User(username='admin_test', email='<EMAIL>', role=UserRole.ADMIN)
        admin_user.set_password('admin_password')
        db.session.add(admin_user)
        db.session.commit()
        return admin_user

    def login_admin(self):
        with self.client.session_transaction() as sess:
            admin = User.query.filter_by(username='admin_test').first()
            if admin:
                sess['user_id'] = admin.id
                sess['role'] = admin.role.value
        return self.client.post(
            '/admin/login',
            data={'username': 'admin_test', 'password': 'admin_password'},
            follow_redirects=True
        )

    def login_user(self, email='<EMAIL>', password='password'):
        # Helper for regular user login if needed later
        user = User(username=email.split('@')[0], email=email, role=UserRole.USER)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        with self.client.session_transaction() as sess:
            sess['user_id'] = user.id
            sess['role'] = user.role.value

        # Simulate login if your app has a login route and session mechanism
        # For now, directly setting session for simplicity in tests
        # This might need adjustment based on actual auth flow for non-admin
        return user


    # 1. Test Coupon Model
    def test_create_coupon_model(self):
        with self.app.app_context():
            coupon = Coupon(code="SAVE10", discount_percentage=10.0, is_active=True)
            db.session.add(coupon)
            db.session.commit()

            fetched_coupon = Coupon.query.filter_by(code="SAVE10").first()
            self.assertIsNotNone(fetched_coupon)
            self.assertEqual(fetched_coupon.discount_percentage, 10.0)
            self.assertTrue(fetched_coupon.is_active)

    # 2. Test Admin Coupon Management
    def test_admin_list_coupons(self):
        self.login_admin()
        # Create a sample coupon to be listed
        with self.app.app_context():
            coupon = Coupon(code="LISTME", discount_percentage=5.0, is_active=True)
            db.session.add(coupon)
            db.session.commit()

        response = self.client.get('/admin/coupons', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'LISTME', response.data)
        self.assertIn(b'优惠券管理', response.data) # Check for page title or header

    def test_admin_create_coupon_get(self):
        self.login_admin()
        response = self.client.get('/admin/coupons/new', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'创建新优惠券', response.data)

    def test_admin_create_coupon_post_success(self):
        self.login_admin()
        response = self.client.post('/admin/coupons/new', data={
            'code': 'NEWCOUPON',
            'discount_percentage': '15.5',
            'is_active': 'on'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200) # Should redirect to list page
        self.assertIn(b'优惠券创建成功', response.data)
        with self.app.app_context():
            coupon = Coupon.query.filter_by(code='NEWCOUPON').first()
            self.assertIsNotNone(coupon)
            self.assertEqual(coupon.discount_percentage, 15.5)
            self.assertTrue(coupon.is_active)

    def test_admin_create_coupon_post_duplicate_code(self):
        self.login_admin()
        with self.app.app_context():
            existing_coupon = Coupon(code='EXISTING', discount_percentage=10)
            db.session.add(existing_coupon)
            db.session.commit()

        response = self.client.post('/admin/coupons/new', data={
            'code': 'EXISTING',
            'discount_percentage': '20'
        }, follow_redirects=True)
        self.assertEqual(response.status_code, 200) # Stays on create page or redirects to it
        self.assertIn(b'优惠券代码已存在', response.data)
        with self.app.app_context():
            coupons_count = Coupon.query.filter_by(code='EXISTING').count()
            self.assertEqual(coupons_count, 1) # No new coupon should be created

    def test_admin_create_coupon_post_invalid_discount(self):
        self.login_admin()
        response_too_high = self.client.post('/admin/coupons/new', data={
            'code': 'INVALIDDISC1',
            'discount_percentage': '101'
        }, follow_redirects=True)
        self.assertIn(b'无效的折扣百分比', response_too_high.data)

        response_too_low = self.client.post('/admin/coupons/new', data={
            'code': 'INVALIDDISC2',
            'discount_percentage': '-5'
        }, follow_redirects=True)
        self.assertIn(b'无效的折扣百分比', response_too_low.data)

        with self.app.app_context():
            self.assertIsNone(Coupon.query.filter_by(code='INVALIDDISC1').first())
            self.assertIsNone(Coupon.query.filter_by(code='INVALIDDISC2').first())

    def test_admin_edit_coupon(self):
        self.login_admin()
        with self.app.app_context():
            coupon = Coupon(code='EDITME', discount_percentage=20, is_active=True)
            db.session.add(coupon)
            db.session.commit()
            coupon_id = coupon.id

        response_get = self.client.get(f'/admin/coupons/{coupon_id}/edit', follow_redirects=True)
        self.assertEqual(response_get.status_code, 200)
        self.assertIn(b'EDITME', response_get.data)

        response_post = self.client.post(f'/admin/coupons/{coupon_id}/edit', data={
            'code': 'EDITEDCODE',
            'discount_percentage': '25.0',
            'is_active': 'on' # Keep it active
        }, follow_redirects=True)
        self.assertEqual(response_post.status_code, 200) # Redirects to list
        self.assertIn(b'优惠券更新成功', response_post.data)

        with self.app.app_context():
            edited_coupon = Coupon.query.get(coupon_id)
            self.assertEqual(edited_coupon.code, 'EDITEDCODE')
            self.assertEqual(edited_coupon.discount_percentage, 25.0)

    def test_admin_activate_deactivate_coupon(self):
        self.login_admin()
        with self.app.app_context():
            coupon = Coupon(code='TOGGLEME', discount_percentage=10, is_active=True)
            db.session.add(coupon)
            db.session.commit()
            coupon_id = coupon.id

        # Deactivate
        response_deactivate = self.client.post(f'/admin/coupons/{coupon_id}/deactivate', follow_redirects=True)
        self.assertEqual(response_deactivate.status_code, 200)
        self.assertIn(b'已停用', response_deactivate.data)
        with self.app.app_context():
            self.assertFalse(Coupon.query.get(coupon_id).is_active)

        # Activate
        response_activate = self.client.post(f'/admin/coupons/{coupon_id}/activate', follow_redirects=True)
        self.assertEqual(response_activate.status_code, 200)
        self.assertIn(b'已激活', response_activate.data)
        with self.app.app_context():
            self.assertTrue(Coupon.query.get(coupon_id).is_active)

    def test_admin_delete_coupon(self):
        self.login_admin()
        with self.app.app_context():
            coupon = Coupon(code='DELETEME', discount_percentage=5)
            db.session.add(coupon)
            db.session.commit()
            coupon_id = coupon.id

        response = self.client.post(f'/admin/coupons/{coupon_id}/delete', follow_redirects=True)
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'已删除', response.data)
        with self.app.app_context():
            self.assertIsNone(Coupon.query.get(coupon_id))

    # 3. Test Shop Purchase Flow with Coupons
    def test_purchase_with_valid_coupon(self):
        with self.app.app_context():
            product = Product(name="Test Product", price=100.0, duration_days=30, traffic_limit_gb=10)
            coupon = Coupon(code="SAVE20", discount_percentage=20.0, is_active=True)
            db.session.add_all([product, coupon])
            db.session.commit()
            product_id = product.id

        # No user login needed for purchase in current app structure from shop.py
        response = self.client.post('/purchase', data={
            'product_id': str(product_id),
            'customer_email': '<EMAIL>',
            'coupon_code': 'SAVE20'
        }, follow_redirects=True)

        self.assertEqual(response.status_code, 200) # Assuming success page or payment page
        self.assertIn(b"优惠码 'SAVE20' 已应用", response.data) # Flash message

        with self.app.app_context():
            order = Order.query.filter_by(customer_email='<EMAIL>').first()
            self.assertIsNotNone(order)
            self.assertEqual(order.applied_coupon_code, 'SAVE20')
            self.assertEqual(order.discount_amount, 20.00)
            self.assertEqual(order.price, 80.00) # Final price

    def test_purchase_with_inactive_coupon(self):
        with self.app.app_context():
            product = Product(name="Test Product Inactive", price=100.0, duration_days=30, traffic_limit_gb=10)
            coupon = Coupon(code="INACTIVE25", discount_percentage=25.0, is_active=False) # Inactive
            db.session.add_all([product, coupon])
            db.session.commit()
            product_id = product.id

        response = self.client.post('/purchase', data={
            'product_id': str(product_id),
            'customer_email': '<EMAIL>',
            'coupon_code': 'INACTIVE25'
        }, follow_redirects=True)

        self.assertEqual(response.status_code, 200)
        # Expects redirect back to buy page due to invalid coupon
        self.assertIn(b"优惠码 'INACTIVE25' 无效或已停用", response.data)

        with self.app.app_context():
            # Check no order was created, or if it was, it doesn't have coupon details
            # Based on current shop.py, it redirects before order creation if coupon is invalid
            order = Order.query.filter_by(customer_email='<EMAIL>').first()
            self.assertIsNone(order) # Expect no order created

    def test_purchase_with_invalid_coupon_code(self):
        with self.app.app_context():
            product = Product(name="Test Product Invalid", price=50.0, duration_days=30, traffic_limit_gb=5)
            db.session.add(product)
            db.session.commit()
            product_id = product.id

        response = self.client.post('/purchase', data={
            'product_id': str(product_id),
            'customer_email': '<EMAIL>',
            'coupon_code': 'NONEXISTENT'
        }, follow_redirects=True)

        self.assertEqual(response.status_code, 200)
        self.assertIn(b"优惠码 'NONEXISTENT' 无效或已停用", response.data)
        with self.app.app_context():
            order = Order.query.filter_by(customer_email='<EMAIL>').first()
            self.assertIsNone(order)

    def test_purchase_without_coupon(self):
        with self.app.app_context():
            product = Product(name="Test Product No Coupon", price=75.0, duration_days=30, traffic_limit_gb=15)
            db.session.add(product)
            db.session.commit()
            product_id = product.id

        response = self.client.post('/purchase', data={
            'product_id': str(product_id),
            'customer_email': '<EMAIL>'
        }, follow_redirects=True)

        self.assertEqual(response.status_code, 200)
        with self.app.app_context():
            order = Order.query.filter_by(customer_email='<EMAIL>').first()
            self.assertIsNotNone(order)
            self.assertIsNone(order.applied_coupon_code)
            self.assertEqual(order.discount_amount, 0.0)
            self.assertEqual(order.price, 75.0)

    def test_purchase_with_coupon_and_test_mode(self):
        with self.app.app_context():
            product = Product(name="Test Product TestMode", price=120.0, duration_days=30, traffic_limit_gb=20)
            coupon = Coupon(code="TESTSAVE", discount_percentage=50.0, is_active=True)
            db.session.add_all([product, coupon])
            db.session.commit()
            product_id = product.id

        response = self.client.post('/purchase', data={
            'product_id': str(product_id),
            'customer_email': '<EMAIL>',
            'coupon_code': 'TESTSAVE',
            'test_mode': 'on' # Enable test mode
        }, follow_redirects=True)

        self.assertEqual(response.status_code, 200)
        self.assertIn(b'测试模式：节点分配成功！', response.data) # Test mode success message

        with self.app.app_context():
            order = Order.query.filter_by(customer_email='<EMAIL>').first()
            self.assertIsNotNone(order)
            self.assertEqual(order.price, 0.0) # Price should be 0 in test mode
            self.assertEqual(order.applied_coupon_code, "TESTMODE") # Special code for test mode
            self.assertEqual(order.discount_amount, 120.0) # Discount is original price

if __name__ == '__main__':
    unittest.main()
