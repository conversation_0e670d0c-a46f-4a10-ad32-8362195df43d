"""
数据库迁移脚本：添加订阅表
"""
import sqlite3
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def upgrade_database(db_path='node_sales.db'):
    """升级数据库，添加订阅表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否已存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='subscriptions'
        """)
        
        if cursor.fetchone():
            logger.info("订阅表已存在，跳过创建")
            return True
        
        # 创建订阅表
        cursor.execute("""
            CREATE TABLE subscriptions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order_id INTEGER NOT NULL,
                subscription_token VARCHAR(100) NOT NULL UNIQUE,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME,
                FOREIGN KEY (order_id) REFERENCES orders (id),
                UNIQUE (order_id)
            )
        """)
        
        # 创建索引
        cursor.execute("""
            CREATE INDEX idx_subscriptions_token ON subscriptions (subscription_token)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_subscriptions_order_id ON subscriptions (order_id)
        """)
        
        cursor.execute("""
            CREATE INDEX idx_subscriptions_active ON subscriptions (is_active)
        """)
        
        conn.commit()
        logger.info("成功创建订阅表和索引")
        
        return True
        
    except Exception as e:
        logger.error(f"创建订阅表失败: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()

def downgrade_database(db_path='node_sales.db'):
    """降级数据库，删除订阅表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 删除索引
        cursor.execute("DROP INDEX IF EXISTS idx_subscriptions_token")
        cursor.execute("DROP INDEX IF EXISTS idx_subscriptions_order_id") 
        cursor.execute("DROP INDEX IF EXISTS idx_subscriptions_active")
        
        # 删除表
        cursor.execute("DROP TABLE IF EXISTS subscriptions")
        
        conn.commit()
        logger.info("成功删除订阅表和索引")
        
        return True
        
    except Exception as e:
        logger.error(f"删除订阅表失败: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "downgrade":
        print("执行数据库降级...")
        success = downgrade_database()
    else:
        print("执行数据库升级...")
        success = upgrade_database()
    
    if success:
        print("数据库迁移完成")
    else:
        print("数据库迁移失败")
        sys.exit(1)
