"""
数据库模型包
"""
from flask_sqlalchemy import SQLAlchemy

# 创建数据库实例
db = SQLAlchemy()

# 导入所有模型以确保它们被注册
from .enums import *
from .user import User
from .order import Order, NodeConfig
from .product import Product
from .xui_panel import XUIPanel, XUIPanelGroup, XUIPanelGroupMembership
from .subscription import Subscription
from .traffic import TrafficStats
from .subscription_traffic_baseline import SubscriptionTrafficBaseline
from .email_config import EmailConfig
from .verification_code import VerificationCode, VerificationCodeType
from .captcha_code import CaptchaCode
from .coupon import Coupon
from .renewal_pricing import RenewalPricing
from .renewal_task import RenewalTask, RenewalTaskStatus
from .protocol_template import ProtocolTemplate, ProtocolVariable

# 导出所有模型和枚举
__all__ = [
    'db',
    # 枚举
    'OrderStatus', 'NodeType', 'PanelStatus', 'UserRole', 'ProductType', 'GroupRole', 'VerificationCodeType',
    # 模型
    'User', 'Order', 'NodeConfig', 'Product',
    'XUIPanel', 'XUIPanelGroup', 'XUIPanelGroupMembership',
    'Subscription', 'TrafficStats', 'SubscriptionTrafficBaseline', 'EmailConfig', 'VerificationCode', 'CaptchaCode', 'Coupon',
    'RenewalPricing', 'RenewalTask', 'RenewalTaskStatus', 'ProtocolTemplate', 'ProtocolVariable'
]
