"""
用户认证路由
"""
from flask import Blueprint, request, session, render_template, redirect, url_for, flash
from models import db, User, UserRole
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'GET':
        return render_template('auth/register.html')

    try:
        # 验证必需字段
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        email_code = request.form.get('email_code')

        if not all([username, email, password, email_code]):
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('auth.register'))

        # 验证邮箱验证码
        from services.verification_service import verification_service
        from models import VerificationCodeType

        code_success, code_message, _ = verification_service.verify_code(
            email, email_code, VerificationCodeType.REGISTER
        )

        if not code_success:
            flash(f'邮箱验证码验证失败: {code_message}', 'error')
            return redirect(url_for('auth.register'))

        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'error')
            return redirect(url_for('auth.register'))

        if User.query.filter_by(email=email).first():
            flash('邮箱已存在', 'error')
            return redirect(url_for('auth.register'))

        # 创建用户
        user = User(
            username=username,
            email=email,
            role=UserRole.USER  # 默认为普通用户
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        # 自动登录
        session['user_id'] = user.id
        session['username'] = user.username
        session['role'] = user.role.value

        flash('注册成功', 'success')
        return redirect(url_for('shop.shop_index'))

    except Exception as e:
        logger.error(f"用户注册失败: {e}")
        db.session.rollback()
        flash('注册失败', 'error')
        return redirect(url_for('auth.register'))

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'GET':
        return render_template('auth/login.html')

    try:
        # 验证必需字段
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            flash('用户名和密码不能为空', 'error')
            return redirect(url_for('auth.login'))

        # 查找用户（支持用户名或邮箱登录）
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()

        if not user or not user.check_password(password):
            flash('用户名或密码错误', 'error')
            return redirect(url_for('auth.login'))

        if not user.is_active:
            flash('账户已被禁用', 'error')
            return redirect(url_for('auth.login'))

        # 更新最后登录时间
        user.last_login = datetime.now()
        db.session.commit()

        # 设置session
        session['user_id'] = user.id
        session['username'] = user.username
        session['role'] = user.role.value

        flash('登录成功', 'success')

        # 根据用户角色重定向
        if user.role == UserRole.ADMIN:
            return redirect(url_for('admin.dashboard'))
        else:
            return redirect(url_for('shop.shop_index'))

    except Exception as e:
        logger.error(f"用户登录失败: {e}")
        flash('登录失败', 'error')
        return redirect(url_for('auth.login'))

@auth_bp.route('/logout')
def logout():
    """用户登出"""
    session.clear()
    flash('已成功登出', 'info')
    return redirect(url_for('shop.shop_index'))
