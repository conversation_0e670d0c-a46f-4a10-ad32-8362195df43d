{% extends "base.html" %}

{% block title %}创建产品 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-plus-circle"></i> 创建新产品
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <!-- 基本信息 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">产品名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required 
                                           placeholder="例如：月付套餐、季付高级版">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="product_type" class="form-label">产品类型 <span class="text-danger">*</span></label>
                                    <select class="form-select" id="product_type" name="product_type" required>
                                        <option value="">选择产品类型...</option>
                                        <option value="monthly">月付</option>
                                        <option value="quarterly">季付</option>
                                        <option value="yearly">年付</option>
                                        <option value="custom">自定义</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">产品描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="描述产品的特点和优势"></textarea>
                        </div>

                        <!-- 配置信息 -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="duration_days" class="form-label">有效期（天） <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="duration_days" name="duration_days" 
                                           required min="1" placeholder="30">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="traffic_limit_gb" class="form-label">流量限制（GB） <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="traffic_limit_gb" name="traffic_limit_gb" 
                                           required min="1" placeholder="100">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="price" class="form-label">价格 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           required min="0" step="0.01" placeholder="9.99">
                                </div>
                            </div>
                        </div>

                        <!-- 节点配置 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="node_type" class="form-label">节点类型</label>
                                    <select class="form-select" id="node_type" name="node_type">
                                        <option value="vless">VLESS</option>
                                        <option value="vmess">VMess</option>
                                        <option value="trojan">Trojan</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="target_group_id" class="form-label">目标分组</label>
                                    <select class="form-select" id="target_group_id" name="target_group_id">
                                        <option value="">不指定分组（自动分配）</option>
                                        {% for group in groups %}
                                        <option value="{{ group.id }}">
                                            {{ group.name }} (优先级: {{ group.priority }}, 面板数: {{ group.panels|length }})
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">选择分组后，用户购买此产品将优先从该分组分配节点</div>
                                </div>
                            </div>
                        </div>

                        <!-- 分组特定配置 -->
                        <div class="mb-3">
                            <label for="group_specific_config" class="form-label">分组特定配置</label>
                            <textarea class="form-control" id="group_specific_config" name="group_specific_config" rows="3" 
                                      placeholder='JSON格式的特定配置，例如：{"bandwidth_limit": "100Mbps", "concurrent_connections": 10}'></textarea>
                            <div class="form-text">可选的JSON格式配置，用于覆盖分组的默认设置</div>
                        </div>

                        <!-- 库存和状态 -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="stock_count" class="form-label">库存数量</label>
                                    <input type="number" class="form-control" id="stock_count" name="stock_count" 
                                           value="-1" placeholder="-1">
                                    <div class="form-text">-1 表示无限库存</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            启用产品
                                        </label>
                                        <div class="form-text">只有启用的产品才会在商店中显示</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('admin.products') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 创建产品
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 表单验证
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    const productType = document.getElementById('product_type').value;
    const durationDays = parseInt(document.getElementById('duration_days').value);
    const trafficLimit = parseInt(document.getElementById('traffic_limit_gb').value);
    const price = parseFloat(document.getElementById('price').value);
    
    if (!name || !productType || !durationDays || !trafficLimit || isNaN(price)) {
        e.preventDefault();
        alert('请填写所有必需字段');
        return false;
    }
    
    if (durationDays < 1) {
        e.preventDefault();
        alert('有效期必须大于0天');
        document.getElementById('duration_days').focus();
        return false;
    }
    
    if (trafficLimit < 1) {
        e.preventDefault();
        alert('流量限制必须大于0GB');
        document.getElementById('traffic_limit_gb').focus();
        return false;
    }
    
    if (price < 0) {
        e.preventDefault();
        alert('价格不能为负数');
        document.getElementById('price').focus();
        return false;
    }
    
    // 验证JSON格式
    const groupConfig = document.getElementById('group_specific_config').value.trim();
    if (groupConfig) {
        try {
            JSON.parse(groupConfig);
        } catch (e) {
            alert('分组特定配置必须是有效的JSON格式');
            document.getElementById('group_specific_config').focus();
            return false;
        }
    }
});

// 产品类型变化时自动设置有效期
document.getElementById('product_type').addEventListener('change', function() {
    const durationInput = document.getElementById('duration_days');
    switch(this.value) {
        case 'monthly':
            durationInput.value = 30;
            break;
        case 'quarterly':
            durationInput.value = 90;
            break;
        case 'yearly':
            durationInput.value = 365;
            break;
    }
});
</script>

{% endblock %}
