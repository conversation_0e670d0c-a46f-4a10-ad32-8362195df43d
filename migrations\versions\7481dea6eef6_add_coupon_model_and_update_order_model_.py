"""Add Coupon model and update Order model for coupons

Revision ID: 7481dea6eef6
Revises:
Create Date: 2025-06-07 06:57:58.236182

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7481dea6eef6'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    try:
        op.create_table('coupons',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('code', sa.String(length=80), nullable=False),
        sa.Column('discount_percentage', sa.Float(), nullable=False),
        sa.Column('is_active', sa.<PERSON>(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('code')
        )
    except sa.exc.OperationalError as e:
        if "table coupons already exists" in str(e).lower():
            print(f"Table 'coupons' already exists, skipping creation due to: {e}")
        else:
            # Re-raise the exception if it's not the one we're trying to ignore
            raise

    # Attempt to clean up a potentially stale temporary table for captcha_codes
    try:
        op.execute("DROP TABLE IF EXISTS _alembic_tmp_captcha_codes")
        print("Attempted to drop stale table _alembic_tmp_captcha_codes if it existed.")
    except sa.exc.OperationalError as e:
        print(f"Could not drop _alembic_tmp_captcha_codes (might not exist or other issue): {e}")


    with op.batch_alter_table('captcha_codes', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.drop_index(batch_op.f('idx_captcha_session_id'))
        batch_op.create_index(batch_op.f('ix_captcha_codes_session_id'), ['session_id'], unique=False)

    with op.batch_alter_table('orders', schema=None) as batch_op:
        batch_op.add_column(sa.Column('applied_coupon_code', sa.String(length=80), nullable=True))
        batch_op.add_column(sa.Column('discount_amount', sa.Float(), nullable=True))

    with op.batch_alter_table('traffic_stats', schema=None) as batch_op:
        # Attempting to directly create the desired foreign key.
        # If an existing FK on these columns (unnamed or differently named) causes a conflict,
        # this step might fail. This simplification assumes that either no FK exists,
        # or creating this one will effectively replace/be the desired one.
        print("Attempting to create FK 'fk_traffic_stats_subscription_id_subscriptions'.")
        batch_op.create_foreign_key('fk_traffic_stats_subscription_id_subscriptions', 'subscriptions', ['subscription_id'], ['id'], ondelete='CASCADE')

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=False,
               autoincrement=True)
        batch_op.alter_column('role',
               existing_type=sa.VARCHAR(length=20),
               type_=sa.Enum('ADMIN', 'USER', name='userrole'),
               existing_nullable=False,
               existing_server_default=sa.text("'user'"))
        batch_op.drop_index(batch_op.f('idx_users_email'))
        batch_op.drop_index(batch_op.f('idx_users_username'))
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.create_index(batch_op.f('idx_users_username'), ['username'], unique=1)
        batch_op.create_index(batch_op.f('idx_users_email'), ['email'], unique=1)
        batch_op.alter_column('role',
               existing_type=sa.Enum('ADMIN', 'USER', name='userrole'),
               type_=sa.VARCHAR(length=20),
               existing_nullable=False,
               existing_server_default=sa.text("'user'"))
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    with op.batch_alter_table('traffic_stats', schema=None) as batch_op:
        # For downgrade, attempt to drop the named FK we would have created.
        try:
            batch_op.drop_constraint('fk_traffic_stats_subscription_id_subscriptions', type_='foreignkey')
            print("Downgrade: Successfully dropped FK 'fk_traffic_stats_subscription_id_subscriptions'.")
        except (KeyError, ValueError) as e:
            print(f"Downgrade: Could not drop FK by name 'fk_traffic_stats_subscription_id_subscriptions': {e}")
        # The original autogenerated downgrade also had a create_foreign_key(None, ...),
        # which we are avoiding to prevent issues with unnamed constraints.
        # If the original state had an unnamed FK, it might still be there.

    with op.batch_alter_table('orders', schema=None) as batch_op:
        batch_op.drop_column('discount_amount')
        batch_op.drop_column('applied_coupon_code')

    with op.batch_alter_table('captcha_codes', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_captcha_codes_session_id'))
        batch_op.create_index(batch_op.f('idx_captcha_session_id'), ['session_id'], unique=False)
        batch_op.alter_column('id',
               existing_type=sa.INTEGER(),
               nullable=True,
               autoincrement=True)

    op.drop_table('coupons')
    # ### end Alembic commands ###
