{% extends "base.html" %}

{% block title %}订单详情 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-receipt"></i> 订单详情
                    </h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>订单信息</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>订单号：</strong></td>
                                    <td>{{ order.order_id }}</td>
                                </tr>
                                <tr>
                                    <td><strong>状态：</strong></td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if order.status.value == 'completed' else 'warning' if order.status.value == 'pending' else 'danger' }}">
                                            {% if order.status.value == 'completed' %}
                                                已完成
                                            {% elif order.status.value == 'pending' %}
                                                待处理
                                            {% elif order.status.value == 'processing' %}
                                                处理中
                                            {% elif order.status.value == 'failed' %}
                                                失败
                                            {% elif order.status.value == 'cancelled' %}
                                                已取消
                                            {% else %}
                                                {{ order.status.value }}
                                            {% endif %}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>客户邮箱：</strong></td>
                                    <td>{{ order.customer_email }}</td>
                                </tr>
                                {% if order.customer_name %}
                                <tr>
                                    <td><strong>客户姓名：</strong></td>
                                    <td>{{ order.customer_name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>创建时间：</strong></td>
                                    <td>{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                                {% if order.expires_at %}
                                <tr>
                                    <td><strong>到期时间：</strong></td>
                                    <td>{{ order.expires_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                        
                        <div class="col-md-6">
                            <h5>套餐信息</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>协议类型：</strong></td>
                                    <td>{{ order.node_type.value.upper() }}</td>
                                </tr>
                                <tr>
                                    <td><strong>有效期：</strong></td>
                                    <td>{{ order.duration_days }} 天</td>
                                </tr>
                                <tr>
                                    <td><strong>流量限制：</strong></td>
                                    <td>{{ order.traffic_limit_gb }}GB</td>
                                </tr>
                                <tr>
                                    <td><strong>原价：</strong></td>
                                    <td>
                                        {% set original_price = order.price + (order.discount_amount if order.discount_amount else 0) %}
                                        ¥{{ "%.2f"|format(original_price) }}
                                    </td>
                                </tr>
                                {% if order.applied_coupon_code %}
                                <tr>
                                    <td><strong>优惠码：</strong></td>
                                    <td>{{ order.applied_coupon_code }}</td>
                                </tr>
                                <tr>
                                    <td><strong>优惠金额：</strong></td>
                                    <td>¥{{ "%.2f"|format(order.discount_amount if order.discount_amount else 0) }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td><strong>支付金额：</strong></td>
                                    <td>
                                        {% if order.price == 0 and not order.applied_coupon_code %}
                                            <span class="text-success">免费</span>
                                        {% elif order.price == 0 and order.applied_coupon_code %}
                                             <span class="text-success">¥0.00 (已优惠)</span>
                                        {% else %}
                                            <span class="text-primary">¥{{ "%.2f"|format(order.price) }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if order.customer_remarks %}
                                <tr>
                                    <td><strong>备注名称：</strong></td>
                                    <td>{{ order.customer_remarks }}</td>
                                </tr>
                                {% endif %}
                                {% if order.payment_method %}
                                <tr>
                                    <td><strong>支付方式：</strong></td>
                                    <td>{{ order.payment_method }}</td>
                                </tr>
                                {% endif %}
                            </table>
                        </div>
                    </div>
                    
                    <!-- 节点配置信息 -->
                    {% if order.status.value == 'completed' and order.node_configs %}
                    <hr>
                    <h5>节点配置</h5>
                    {% for config in order.node_configs %}
                    <div class="card bg-light mb-3">
                        <div class="card-body">
                            <h6 class="card-title">节点 #{{ loop.index }}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>服务器地址：</strong> {{ config.server_address }}</p>
                                    <p><strong>端口：</strong> {{ config.server_port }}</p>
                                    <p><strong>协议：</strong> {{ config.protocol.upper() }}</p>
                                    <p><strong>传输方式：</strong> {{ config.transport }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>客户端ID：</strong> <code>{{ config.client_id }}</code></p>
                                    <p><strong>状态：</strong> 
                                        <span class="badge bg-{{ 'success' if config.is_active else 'secondary' }}">
                                            {{ '活跃' if config.is_active else '停用' }}
                                        </span>
                                    </p>
                                    <p><strong>流量使用：</strong> {{ "%.2f"|format(config.total_traffic_gb) }}GB</p>
                                </div>
                            </div>
                            {% if config.vless_config %}
                            <div class="mt-3">
                                <label class="form-label"><strong>配置链接：</strong></label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ config.vless_config }}" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard(this)">
                                        <i class="bi bi-clipboard"></i> 复制
                                    </button>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    {% endif %}
                    
                    <!-- 订单状态说明 -->
                    {% if order.status.value == 'pending' %}
                    <div class="alert alert-warning">
                        <i class="bi bi-clock"></i> 
                        <strong>订单待处理</strong><br>
                        您的订单正在处理中，请耐心等待。节点配置将在处理完成后发送到您的邮箱。
                    </div>
                    {% elif order.status.value == 'completed' %}
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> 
                        <strong>订单已完成</strong><br>
                        节点已成功激活，配置信息已发送到您的邮箱 {{ order.customer_email }}。
                    </div>
                    {% elif order.status.value == 'failed' %}
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 
                        <strong>订单处理失败</strong><br>
                        很抱歉，您的订单处理失败。请联系客服获取帮助。
                    </div>
                    {% endif %}
                    
                    {% if order.notes %}
                    <div class="mt-3">
                        <h6>备注信息</h6>
                        <p class="text-muted">{{ order.notes }}</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('shop.my_orders') }}" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> 返回订单列表
                    </a>
                    <a href="{{ url_for('shop.shop_index') }}" class="btn btn-primary">
                        <i class="bi bi-shop"></i> 继续购买
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(button) {
    const input = button.parentElement.querySelector('input');
    input.select();
    document.execCommand('copy');
    
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="bi bi-check"></i> 已复制';
    button.classList.remove('btn-outline-secondary');
    button.classList.add('btn-success');
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.classList.remove('btn-success');
        button.classList.add('btn-outline-secondary');
    }, 2000);
}
</script>
{% endblock %}
