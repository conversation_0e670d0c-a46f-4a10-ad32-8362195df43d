from . import db  # Assuming a shared SQLAlchemy instance from models/__init__.py
from sqlalchemy.sql import func
import datetime

class Coupon(db.Model):
    __tablename__ = 'coupons'

    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(80), unique=True, nullable=False)
    discount_percentage = db.Column(db.Float, nullable=False)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)

    # 使用次数相关字段
    max_uses = db.Column(db.Integer, nullable=True)  # NULL表示无限制
    used_count = db.Column(db.Integer, nullable=False, default=0)  # 已使用次数

    created_at = db.Column(db.DateTime(timezone=True), server_default=func.now())
    updated_at = db.Column(db.DateTime(timezone=True), default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f'<Coupon {self.code}>'

    def is_available(self):
        """检查优惠券是否可用"""
        if not self.is_active:
            return False
        if self.max_uses is None:
            return True  # 无限制使用
        return self.used_count < self.max_uses

    def get_remaining_uses(self):
        """获取剩余使用次数"""
        if self.max_uses is None:
            return None  # 无限制
        return max(0, self.max_uses - self.used_count)

    def increment_usage(self):
        """增加使用次数"""
        self.used_count += 1
        self.updated_at = func.now()

    def should_be_deleted(self):
        """检查是否应该被删除（使用次数已达上限）"""
        if self.max_uses is None:
            return False
        return self.used_count >= self.max_uses
