#!/usr/bin/env python3
"""
调试流量统计显示问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_traffic_calculation():
    """调试流量计算"""
    try:
        from services.traffic_stats_service import TrafficStatsService
        from models import TrafficStats, Order, Subscription
        
        print("=== 调试流量统计计算 ===")
        
        # 获取一些示例数据
        traffic_service = TrafficStatsService()
        
        # 查询最新的几条流量统计记录
        latest_stats = TrafficStats.query.order_by(TrafficStats.recorded_at.desc()).limit(5).all()
        
        print(f"\n找到 {len(latest_stats)} 条最新流量统计记录:")
        
        for i, stat in enumerate(latest_stats, 1):
            print(f"\n--- 记录 {i} ---")
            print(f"订阅ID: {stat.subscription_id}")
            print(f"用户ID: {stat.user_id}")
            print(f"上传字节: {stat.upload_bytes:,}")
            print(f"下载字节: {stat.download_bytes:,}")
            print(f"总字节: {stat.total_bytes:,}")
            print(f"记录时间: {stat.recorded_at}")
            
            # 转换为MB
            upload_mb = stat.upload_bytes / (1024**2)
            download_mb = stat.download_bytes / (1024**2)
            total_mb = stat.total_bytes / (1024**2)
            
            print(f"上传MB: {upload_mb:.2f}")
            print(f"下载MB: {download_mb:.2f}")
            print(f"总MB: {total_mb:.2f}")
            
            # 查找对应的订单
            subscription = Subscription.query.get(stat.subscription_id)
            if subscription and subscription.order:
                order = subscription.order
                traffic_limit_gb = order.traffic_limit_gb
                traffic_limit_mb = traffic_limit_gb * 1024
                
                print(f"流量限制GB: {traffic_limit_gb}")
                print(f"流量限制MB: {traffic_limit_mb}")
                
                # 计算使用百分比
                usage_percentage = (total_mb / traffic_limit_mb * 100) if traffic_limit_mb > 0 else 0
                print(f"使用百分比: {usage_percentage:.2f}%")
                print(f"限制后百分比: {min(round(usage_percentage, 1), 100)}%")
                
                # 使用服务方法计算
                service_result = traffic_service.get_subscription_latest_traffic_stats(stat.subscription_id, order)
                print(f"服务计算结果: {service_result}")
            else:
                print("未找到对应的订单信息")
        
        return True
        
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_specific_user():
    """调试特定用户的流量统计"""
    try:
        from services.subscription_service import SubscriptionService
        
        print("\n=== 调试用户订阅流量显示 ===")
        
        subscription_service = SubscriptionService()
        
        # 获取用户ID为1的订阅
        user_subscriptions = subscription_service.get_user_subscriptions(1)
        
        print(f"用户1的订阅数量: {len(user_subscriptions)}")
        
        for i, sub in enumerate(user_subscriptions, 1):
            print(f"\n--- 订阅 {i} ---")
            print(f"订单ID: {sub['order_id']}")
            print(f"产品名称: {sub['product_name']}")
            print(f"是否活跃: {sub['is_active']}")
            print(f"是否过期: {sub['is_expired']}")
            
            traffic_stats = sub['traffic_stats']
            print(f"流量统计: {traffic_stats}")
            
            # 检查数据合理性
            total_traffic = traffic_stats['total_traffic_mb']
            traffic_limit = traffic_stats['traffic_limit_mb']
            usage_percentage = traffic_stats['usage_percentage']
            
            print(f"总流量MB: {total_traffic}")
            print(f"流量限制MB: {traffic_limit}")
            print(f"使用百分比: {usage_percentage}%")
            
            # 重新计算百分比
            if traffic_limit > 0:
                recalc_percentage = (total_traffic / traffic_limit) * 100
                print(f"重新计算百分比: {recalc_percentage:.2f}%")
            
        return True
        
    except Exception as e:
        print(f"调试用户订阅失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始调试流量统计显示问题...")

    # 创建Flask应用上下文
    try:
        from app import create_app
        app = create_app()
        with app.app_context():
            success1 = debug_traffic_calculation()
            success2 = debug_specific_user()

            if success1 and success2:
                print("\n🎉 调试完成")
            else:
                print("\n❌ 调试过程中出现错误")
    except Exception as e:
        print(f"创建应用上下文失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
