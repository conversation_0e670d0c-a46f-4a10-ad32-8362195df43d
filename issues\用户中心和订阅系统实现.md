# 用户中心和订阅系统实现

## 任务目标
创建用户中心页面，用于显示用户的订阅信息、流量使用情况和订阅链接。实现Base64编码的v2ray配置订阅功能。

## 执行计划

### 第一阶段：核心服务创建 ✅
- [x] 创建订阅服务 (`services/subscription_service.py`)
- [x] 创建订阅路由 (`routes/subscription.py`)
- [x] 创建用户中心路由 (`routes/user.py`)

### 第二阶段：用户界面 ✅
- [x] 用户中心主页模板 (`templates/user/dashboard.html`)
- [x] 订阅管理页面模板 (`templates/user/subscriptions.html`)
- [x] 订阅详情页面模板 (`templates/user/subscription_detail.html`)
- [x] 个人资料页面模板 (`templates/user/profile.html`)

### 第三阶段：系统集成 ✅
- [x] 更新导航栏添加用户中心入口
- [x] 更新Flask应用注册新的蓝图
- [x] 修复数据库关联问题

## 主要功能实现

### 1. 订阅服务 (SubscriptionService)
**文件：** `services/subscription_service.py`

**核心功能：**
- 获取用户的所有有效订阅
- 实时流量统计查询和更新
- 生成Base64编码的订阅链接
- 支持用户ID和邮箱两种订阅方式

**关键方法：**
- `get_user_subscriptions()` - 获取用户订阅列表
- `get_subscription_configs()` - 生成Base64编码配置
- `_get_order_traffic_stats()` - 获取流量统计
- `generate_subscription_url()` - 生成订阅链接

### 2. 订阅路由 (subscription_bp)
**文件：** `routes/subscription.py`

**API端点：**
- `GET /subscription/<token>` - 获取Base64编码的v2ray配置
- `GET /api/subscription/info/<token>` - 获取订阅信息
- `POST /api/subscription/generate` - 生成订阅链接
- `GET /api/subscription/validate/<token>` - 验证订阅令牌
- `POST /api/subscription/refresh/<token>` - 刷新流量统计

### 3. 用户中心路由 (user_bp)
**文件：** `routes/user.py`

**页面路由：**
- `GET /user/dashboard` - 用户中心主页
- `GET /user/subscriptions` - 订阅管理页面
- `GET /user/subscription/<order_id>` - 订阅详情页面
- `GET /user/profile` - 个人资料页面
- `POST /user/profile` - 更新个人资料
- `POST /user/change-password` - 修改密码

**API端点：**
- `POST /user/api/subscription/refresh` - 刷新用户订阅流量
- `GET /user/api/subscription/url` - 获取用户订阅链接

### 4. 用户界面

#### 用户中心主页
**文件：** `templates/user/dashboard.html`
- 统计卡片显示（总订阅数、活跃订阅、已用流量、剩余流量）
- 订阅链接展示和复制功能
- 快捷操作入口
- 最近订阅列表

#### 订阅管理页面
**文件：** `templates/user/subscriptions.html`
- 通用订阅链接展示
- 详细的订阅列表展示
- 流量使用进度条
- 订阅状态管理

#### 订阅详情页面
**文件：** `templates/user/subscription_detail.html`
- 订阅状态展示
- 详细的基本信息和流量统计
- 节点配置列表
- 配置复制功能

#### 个人资料页面
**文件：** `templates/user/profile.html`
- 基本信息编辑
- 密码修改功能
- 账户统计信息

## 订阅链接机制

### 令牌生成
```python
# 用户ID订阅
token = base64.b64encode(f"user_{user_id}".encode()).decode()

# 邮箱订阅
token = base64.b64encode(f"email_{email}".encode()).decode()
```

### 订阅URL格式
```
http://domain.com/subscription/{token}
```

### 配置返回格式
- 返回Base64编码的v2ray配置文本
- 每行一个节点配置
- 自动过滤过期和无效节点
- 支持v2rayN、Clash、Shadowrocket等客户端

## 导航栏更新

### 登录用户导航
- 添加"用户中心"和"我的订阅"链接
- 更新用户下拉菜单，增加用户中心、订阅管理、个人资料入口

### 权限控制
- 所有用户中心页面需要登录验证
- 使用`@login_required`装饰器保护路由

## 数据库集成

### 流量统计更新
- 从X-UI面板实时获取流量数据
- 更新NodeConfig表中的流量字段
- 计算使用百分比和剩余流量

### 订阅状态判断
- 检查订单过期时间
- 验证节点配置有效性
- 动态计算订阅活跃状态

## 技术特性

### 安全性
- 订阅令牌Base64编码
- 登录验证保护
- 密码修改验证

### 性能优化
- 流量统计缓存机制
- 按需加载订阅配置
- 数据库查询优化

### 用户体验
- 响应式设计
- 实时流量刷新
- 一键复制功能
- 友好的错误提示

## 文件清单

### 后端文件
- `services/subscription_service.py` - 订阅服务
- `routes/subscription.py` - 订阅路由
- `routes/user.py` - 用户中心路由

### 前端模板
- `templates/user/dashboard.html` - 用户中心主页
- `templates/user/subscriptions.html` - 订阅管理页面
- `templates/user/subscription_detail.html` - 订阅详情页面
- `templates/user/profile.html` - 个人资料页面

### 配置文件
- `app.py` - 更新蓝图注册
- `templates/base.html` - 更新导航栏

## 使用说明

### 用户操作流程
1. 用户登录后访问用户中心
2. 查看订阅统计和流量使用情况
3. 复制订阅链接到v2ray客户端
4. 管理个人资料和密码

### 管理员功能
- 通过现有的订单管理系统创建订阅
- 监控用户流量使用情况
- 管理节点配置和状态

### 客户端配置
1. 复制用户中心的订阅链接
2. 在v2ray客户端中添加订阅
3. 客户端自动获取和更新节点配置

## 后续优化建议

1. **缓存优化**
   - 实现流量统计缓存
   - 减少X-UI API调用频率

2. **通知系统**
   - 流量用尽提醒
   - 订阅到期通知

3. **统计分析**
   - 用户使用习惯分析
   - 流量使用趋势图表

4. **多语言支持**
   - 国际化界面
   - 多语言订阅页面
