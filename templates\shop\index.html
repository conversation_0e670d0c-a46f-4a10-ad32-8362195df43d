{% extends "base.html" %}

{% block title %}商店 - 节点商城{% endblock %}

{% block content %}
<!-- 英雄区域 -->
<section class="bg-primary text-white py-5 mb-5 hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <h1 class="display-5 fw-bold mb-4 slide-up">
                    <i class="bi bi-rocket-takeoff me-3"></i>
                    专业节点服务
                </h1>
                <p class="lead mb-4 slide-up">选择最适合您的套餐方案，享受高速稳定的网络服务。我们提供7×24小时技术支持，确保您的网络体验始终流畅。</p>
                <div class="d-flex flex-wrap gap-2 gap-md-3 slide-up">
                    <span class="badge bg-light text-primary px-3 py-2">
                        <i class="bi bi-shield-check me-1"></i> 安全可靠
                    </span>
                    <span class="badge bg-light text-primary px-3 py-2">
                        <i class="bi bi-lightning me-1"></i> 高速稳定
                    </span>
                    <span class="badge bg-light text-primary px-3 py-2">
                        <i class="bi bi-headset me-1"></i> 24/7支持
                    </span>
                </div>
            </div>
            <div class="col-lg-6 text-center">
                <i class="bi bi-globe2 text-white-50 hero-icon" style="font-size: 8rem;"></i>
            </div>
        </div>
    </div>
</section>

<div class="container py-4">
    <div class="row mb-5">
        <div class="col-12 text-center">
            <h2 class="fw-bold mb-3">选择您的套餐</h2>
            <p class="text-muted">我们提供多种套餐选择，满足不同用户的需求</p>
        </div>
    </div>

    <div class="row">
        {% for product in products %}
        <div class="col-lg-4 col-md-6 mb-4 fade-in" style="animation-delay: {{ loop.index0 * 0.1 }}s;">
            <div class="card product-card h-100 border-0 shadow-sm position-relative hover-lift">
                {% if product.price == 0 %}
                <div class="position-absolute top-0 end-0 m-3">
                    <span class="badge bg-success rounded-pill px-3 py-2">
                        <i class="bi bi-gift me-1"></i> 免费体验
                    </span>
                </div>
                {% elif product.product_type.value == 'yearly' %}
                <div class="position-absolute top-0 end-0 m-3">
                    <span class="badge bg-warning text-dark rounded-pill px-3 py-2">
                        <i class="bi bi-star me-1"></i> 最优惠
                    </span>
                </div>
                {% elif product.product_type.value == 'monthly' %}
                <div class="position-absolute top-0 end-0 m-3">
                    <span class="badge bg-danger rounded-pill px-3 py-2">
                        <i class="bi bi-heart me-1"></i> 热门推荐
                    </span>
                </div>
                {% endif %}

                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <i class="bi bi-{{ 'gift' if product.price == 0 else 'star' if product.product_type.value == 'yearly' else 'heart' if product.product_type.value == 'monthly' else 'box' }} text-primary" style="font-size: 2.5rem;"></i>
                    </div>
                    <h4 class="card-title fw-bold mb-3">{{ product.name }}</h4>
                    <p class="card-text text-muted mb-4">{{ product.description or '高速稳定的网络节点服务，为您提供优质的网络体验' }}</p>

                    <div class="price mb-4">
                        {% if product.price == 0 %}
                            <div class="h2 text-success fw-bold mb-1">免费</div>
                            <small class="text-muted">体验套餐</small>
                        {% else %}
                            <div class="h2 fw-bold mb-1" style="color: var(--secondary-color);">
                                ¥{{ "%.0f"|format(product.price) }}
                                <small class="fs-6 text-muted fw-normal">
                                    {% if product.product_type.value == 'monthly' %}
                                        /月
                                    {% elif product.product_type.value == 'quarterly' %}
                                        /季度
                                    {% elif product.product_type.value == 'yearly' %}
                                        /年
                                    {% endif %}
                                </small>
                            </div>
                            {% if product.product_type.value == 'yearly' %}
                            <small class="text-success">相比月付节省60%</small>
                            {% endif %}
                        {% endif %}
                    </div>
                    
                    <div class="features-list mb-4">
                        <div class="row g-2">
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-calendar-check text-success me-2"></i>
                                    <small><strong>{{ product.duration_days }}</strong> 天</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-hdd text-success me-2"></i>
                                    <small><strong>{{ product.traffic_limit_gb }}GB</strong></small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-shield-check text-success me-2"></i>
                                    <small><strong>{{ product.node_type.value.upper() }}</strong></small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="d-flex align-items-center">
                                    <i class="bi bi-devices text-success me-2"></i>
                                    <small>多设备</small>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3 pt-3 border-top">
                            <div class="d-flex justify-content-center gap-3">
                                <small class="text-success">
                                    <i class="bi bi-check-circle me-1"></i> 高速稳定
                                </small>
                                <small class="text-success">
                                    <i class="bi bi-check-circle me-1"></i> 24/7支持
                                </small>
                            </div>
                        </div>
                    </div>

                    {% if product.stock_count == 0 %}
                        <button class="btn btn-outline-secondary btn-lg w-100" disabled>
                            <i class="bi bi-x-circle me-2"></i> 暂时缺货
                        </button>
                    {% else %}
                        <a href="{{ url_for('shop.buy_product', product_id=product.id) }}" class="btn btn-primary btn-lg w-100 shadow-sm btn-animated">
                            <i class="bi bi-cart-plus me-2"></i> 立即购买
                        </a>
                    {% endif %}
                </div>

                <div class="card-footer bg-light border-0 text-center py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            {% if product.stock_count == -1 %}
                                <i class="bi bi-check-circle text-success me-1"></i> 库存充足
                            {% elif product.stock_count > 0 %}
                                <i class="bi bi-exclamation-triangle text-warning me-1"></i> 剩余 {{ product.stock_count }} 份
                            {% endif %}
                        </small>
                        <small class="text-muted">
                            <i class="bi bi-people me-1"></i> 已售 {{ product.sold_count }} 份
                        </small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% if not products %}
    <div class="row">
        <div class="col-12 text-center py-5">
            <i class="bi bi-box text-muted" style="font-size: 4rem;"></i>
            <h3 class="text-muted mt-3">暂无商品</h3>
            <p class="text-muted">请稍后再来查看</p>
        </div>
    </div>
    {% endif %}
</div>

<!-- 服务优势 -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="fw-bold mb-3">为什么选择我们</h2>
                <p class="text-muted">专业的技术团队，为您提供最优质的网络服务体验</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6 mb-4 slide-in-left" style="animation-delay: 0.1s;">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale feature-icon" style="width: 80px; height: 80px;">
                        <i class="bi bi-lightning-charge" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold">高速稳定</h5>
                    <p class="text-muted">采用优质线路，确保网络连接高速稳定</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 slide-in-left" style="animation-delay: 0.2s;">
                <div class="text-center">
                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale feature-icon" style="width: 80px; height: 80px;">
                        <i class="bi bi-shield-check" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold">安全可靠</h5>
                    <p class="text-muted">军用级加密技术，保护您的隐私安全</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 slide-in-right" style="animation-delay: 0.1s;">
                <div class="text-center">
                    <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale feature-icon" style="width: 80px; height: 80px;">
                        <i class="bi bi-headset" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold">24/7支持</h5>
                    <p class="text-muted">全天候技术支持，随时为您解决问题</p>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-4 slide-in-right" style="animation-delay: 0.2s;">
                <div class="text-center">
                    <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3 hover-scale feature-icon" style="width: 80px; height: 80px;">
                        <i class="bi bi-devices" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold">多平台支持</h5>
                    <p class="text-muted">支持所有主流设备和操作系统</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 购买须知 -->
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h3 class="fw-bold mb-3">购买须知</h3>
                <p class="text-muted">请仔细阅读以下条款，确保您了解我们的服务内容</p>
            </div>
        </div>
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                <i class="bi bi-info-circle"></i>
                            </div>
                            <h5 class="mb-0 fw-bold">服务说明</h5>
                        </div>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <span>购买后立即生效，配置信息将发送至您的邮箱</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <span>支持Windows、Mac、iOS、Android等多平台</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <span>流量用完或到期后服务自动停止</span>
                            </li>
                            <li class="mb-0 d-flex align-items-start">
                                <i class="bi bi-x-circle text-danger me-2 mt-1"></i>
                                <span>不支持P2P下载和违法用途</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 50px; height: 50px;">
                                <i class="bi bi-shield-check"></i>
                            </div>
                            <h5 class="mb-0 fw-bold">售后保障</h5>
                        </div>
                        <ul class="list-unstyled mb-0">
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <span>7×24小时专业技术支持</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <span>99.9%服务可用性保证</span>
                            </li>
                            <li class="mb-2 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <span>不满意7天内可申请退款</span>
                            </li>
                            <li class="mb-0 d-flex align-items-start">
                                <i class="bi bi-check-circle text-success me-2 mt-1"></i>
                                <span>提供详细使用教程和客户端下载</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
</div>
{% endblock %}
