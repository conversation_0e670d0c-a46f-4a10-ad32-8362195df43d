"""
配置服务 - 统一管理系统配置
支持从数据库动态读取X-UI面板配置，其他配置从config.py读取
"""
import logging
import time
from typing import Dict, List, Optional, Any
from threading import Lock
from flask import current_app
from models import db, XUIPanel, PanelStatus

logger = logging.getLogger(__name__)

class ConfigService:
    """配置服务类 - 统一管理系统配置"""
    
    _instance = None
    _lock = Lock()
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(ConfigService, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._panels_cache = {}
            self._cache_timestamp = 0
            self._cache_ttl = 300  # 缓存5分钟
            self._initialized = True
    
    def get_xui_panels(self, force_refresh: bool = False) -> Dict[str, Dict]:
        """
        获取X-UI面板配置

        Args:
            force_refresh: 是否强制刷新缓存

        Returns:
            面板配置字典，格式与config.py中的XUI_PANELS相同
        """
        current_time = time.time()

        # 检查缓存是否有效
        if (not force_refresh and
            self._panels_cache and
            current_time - self._cache_timestamp < self._cache_ttl):
            return self._panels_cache

        try:
            # 检查是否在Flask应用上下文中
            from flask import has_app_context
            if not has_app_context():
                logger.warning("不在Flask应用上下文中，使用回退配置")
                return self._get_fallback_config()

            # 从数据库读取面板配置
            panels = XUIPanel.query.filter_by(status=PanelStatus.ACTIVE).all()

            panels_config = {}
            for panel in panels:
                panel_id = f"panel_{panel.id}"
                panels_config[panel_id] = {
                    'base_url': panel.base_url,
                    'path_prefix': panel.path_prefix,
                    'username': panel.username,
                    'password': panel.password,
                    'name': panel.name,
                    'region': panel.region,
                    'max_clients': panel.max_clients,
                    'priority': panel.priority,
                    'panel_id': panel.id  # 添加数据库ID用于关联
                }

            # 如果数据库中没有面板配置，尝试从config.py获取默认配置
            if not panels_config:
                panels_config = self._get_fallback_config()

            # 更新缓存
            self._panels_cache = panels_config
            self._cache_timestamp = current_time

            logger.info(f"成功加载 {len(panels_config)} 个X-UI面板配置")
            return panels_config

        except Exception as e:
            logger.error(f"从数据库读取面板配置失败: {e}")
            # 返回缓存或回退配置
            if self._panels_cache:
                logger.warning("使用缓存的面板配置")
                return self._panels_cache
            else:
                logger.warning("使用回退配置")
                return self._get_fallback_config()
    
    def _get_fallback_config(self) -> Dict[str, Dict]:
        """获取回退配置（从config.py或环境变量）"""
        try:
            from config import Config
            # 如果config.py中还有XUI_PANELS配置，使用它
            if hasattr(Config, 'XUI_PANELS') and Config.XUI_PANELS:
                logger.info("使用config.py中的回退配置")
                return Config.XUI_PANELS
        except Exception as e:
            logger.warning(f"无法从config.py获取回退配置: {e}")
        
        # 最终回退：使用环境变量创建默认配置
        import os
        default_config = {
            'default': {
                'base_url': os.environ.get('XUI_BASE_URL', 'http://localhost:54321'),
                'path_prefix': os.environ.get('XUI_PATH_PREFIX', '/'),
                'username': os.environ.get('XUI_USERNAME', 'admin'),
                'password': os.environ.get('XUI_PASSWORD', 'admin'),
                'name': '默认面板',
                'region': 'default',
                'max_clients': 1000,
                'priority': 1
            }
        }
        logger.info("使用环境变量创建默认配置")
        return default_config
    
    def get_panel_by_id(self, panel_id: str) -> Optional[Dict]:
        """根据面板ID获取单个面板配置"""
        panels = self.get_xui_panels()
        return panels.get(panel_id)
    
    def refresh_cache(self):
        """刷新配置缓存"""
        logger.info("手动刷新配置缓存")
        self.get_xui_panels(force_refresh=True)
    
    def get_load_balance_strategy(self) -> str:
        """获取负载均衡策略"""
        try:
            from config import Config
            return getattr(Config, 'XUI_LOAD_BALANCE_STRATEGY', 'round_robin')
        except:
            return 'round_robin'
    
    def get_auto_failover(self) -> bool:
        """获取自动故障转移设置"""
        try:
            from config import Config
            return getattr(Config, 'XUI_AUTO_FAILOVER', True)
        except:
            return True

# 全局配置服务实例
config_service = ConfigService()
