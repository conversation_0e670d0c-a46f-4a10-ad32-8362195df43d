"""
数据库迁移脚本 - 添加流量统计表和订阅分组字段
"""
import sqlite3
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def upgrade_database(db_path='node_sales.db'):
    """升级数据库，添加流量统计表和订阅分组字段"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 检查并添加订阅表的group_id字段
        logger.info("检查订阅表是否需要添加group_id字段...")
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(subscriptions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'group_id' not in columns:
            logger.info("添加订阅表group_id字段...")
            cursor.execute("""
                ALTER TABLE subscriptions 
                ADD COLUMN group_id INTEGER REFERENCES xui_panel_groups(id)
            """)
            logger.info("订阅表group_id字段添加成功")
        else:
            logger.info("订阅表group_id字段已存在，跳过")
        
        # 2. 检查并创建流量统计表
        logger.info("检查流量统计表是否存在...")
        
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='traffic_stats'
        """)
        
        if not cursor.fetchone():
            logger.info("创建流量统计表...")
            cursor.execute("""
                CREATE TABLE traffic_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    subscription_id INTEGER NOT NULL,
                    group_id INTEGER,
                    upload_bytes BIGINT NOT NULL DEFAULT 0,
                    download_bytes BIGINT NOT NULL DEFAULT 0,
                    total_bytes BIGINT NOT NULL DEFAULT 0,
                    recorded_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id),
                    FOREIGN KEY (group_id) REFERENCES xui_panel_groups (id)
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX idx_traffic_stats_user_id ON traffic_stats(user_id)")
            cursor.execute("CREATE INDEX idx_traffic_stats_subscription_id ON traffic_stats(subscription_id)")
            cursor.execute("CREATE INDEX idx_traffic_stats_group_id ON traffic_stats(group_id)")
            cursor.execute("CREATE INDEX idx_traffic_stats_recorded_at ON traffic_stats(recorded_at)")
            
            logger.info("流量统计表创建成功")
        else:
            logger.info("流量统计表已存在，跳过创建")
        
        # 3. 更新现有订阅的group_id（从关联的产品获取）
        logger.info("更新现有订阅的group_id...")
        try:
            # 检查orders表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='orders'")
            if cursor.fetchone():
                cursor.execute("""
                    UPDATE subscriptions
                    SET group_id = (
                        SELECT p.target_group_id
                        FROM orders o
                        JOIN products p ON o.product_id = p.id
                        WHERE o.id = subscriptions.order_id
                    )
                    WHERE group_id IS NULL
                """)

                updated_rows = cursor.rowcount
                if updated_rows > 0:
                    logger.info(f"更新了 {updated_rows} 个订阅的group_id")
                else:
                    logger.info("没有需要更新的订阅记录")
            else:
                logger.info("orders表不存在，跳过group_id更新")
        except Exception as e:
            logger.warning(f"更新订阅group_id时出错: {e}")
            # 不影响整体迁移，继续执行
        
        # 提交事务
        conn.commit()
        logger.info("数据库迁移完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

def downgrade_database(db_path='node_sales.db'):
    """降级数据库，移除添加的表和字段"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("开始数据库降级...")
        
        # 删除流量统计表
        cursor.execute("DROP TABLE IF EXISTS traffic_stats")
        logger.info("流量统计表已删除")
        
        # 注意：SQLite不支持直接删除列，需要重建表
        # 这里只记录警告，不实际删除group_id字段
        logger.warning("SQLite不支持删除列，group_id字段将保留")
        
        conn.commit()
        logger.info("数据库降级完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库降级失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 执行迁移
    success = upgrade_database()
    if success:
        print("数据库迁移成功完成")
    else:
        print("数据库迁移失败")
