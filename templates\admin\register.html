{% extends "base.html" %}

{% block title %}创建管理员账户 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-danger text-white text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-shield-plus"></i> 创建管理员账户
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>首次设置</strong><br>
                        系统检测到还没有管理员账户，请创建第一个管理员账户来管理系统。
                    </div>
                    
                    <form method="POST" action="{{ url_for('admin.admin_register') }}">
                        <div class="mb-3">
                            <label for="username" class="form-label">管理员用户名 *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="form-text">用于登录管理后台</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱地址 *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">用于接收系统通知</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码 *</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="form-text">至少6位字符，建议使用强密码</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">确认密码 *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="full_name" class="form-label">真实姓名</label>
                            <input type="text" class="form-control" id="full_name" name="full_name">
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="agree_terms" name="agree_terms" required>
                                <label class="form-check-label" for="agree_terms">
                                    我确认拥有系统管理权限，并承诺合法使用管理功能
                                </label>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-shield-check"></i> 创建管理员账户
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        已有管理员账户？ 
                        <a href="{{ url_for('admin.admin_login') }}" class="text-decoration-none">立即登录</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="alert alert-warning">
                <h6><i class="bi bi-exclamation-triangle"></i> 重要说明</h6>
                <ul class="mb-0">
                    <li>管理员账户创建后，此注册页面将不再可用</li>
                    <li>请妥善保管管理员账户信息</li>
                    <li>管理员可以在后台创建其他管理员账户</li>
                    <li>如忘记密码，请联系技术支持</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('密码不匹配');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
