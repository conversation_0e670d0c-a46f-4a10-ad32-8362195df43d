{% extends "base.html" %}

{% block title %}购买成功 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow border-success">
                <div class="card-header bg-success text-white text-center">
                    <h3 class="mb-0">
                        <i class="bi bi-check-circle"></i> 购买成功
                    </h3>
                </div>
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                    </div>
                    
                    <h4 class="text-success mb-3">恭喜！您的订单已成功处理</h4>
                    
                    {% if order.subscription %}
                    <div class="alert alert-primary">
                        <h5><i class="bi bi-link-45deg"></i> 订阅链接</h5>
                        <p class="mb-3">
                            您的专属订阅链接已生成，请将此链接添加到您的v2ray客户端中：
                        </p>
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="subscriptionUrl"
                                   value="{{ request.host_url.rstrip('/') }}/order/{{ order.subscription.subscription_token }}" readonly>
                            <button class="btn btn-success" type="button" onclick="copySubscriptionUrl()">
                                <i class="bi bi-clipboard me-1"></i> 复制链接
                            </button>
                        </div>
                        <small class="text-muted">
                            <i class="bi bi-info-circle"></i>
                            此订阅链接包含您购买的所有节点配置，支持自动更新。
                        </small>
                    </div>
                    {% else %}
                    <div class="alert alert-success">
                        <h5><i class="bi bi-envelope"></i> 配置信息已发送</h5>
                        <p class="mb-0">
                            节点配置信息已发送到您的邮箱：<strong>{{ order.customer_email }}</strong><br>
                            请查收邮件并按照说明配置您的客户端。
                        </p>
                    </div>
                    {% endif %}
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>订单信息</h6>
                                    <p class="mb-1"><strong>订单号：</strong> {{ order.order_id }}</p>
                                    <p class="mb-1"><strong>协议：</strong> {{ order.node_type.value.upper() }}</p>
                                    <p class="mb-1"><strong>有效期：</strong> {{ order.duration_days }} 天</p>
                                    <p class="mb-1"><strong>流量：</strong> {{ order.traffic_limit_gb }}GB</p>
                                    {% if order.applied_coupon_code and order.applied_coupon_code != "TESTMODE" %}
                                    <p class="mb-1"><strong>优惠码:</strong> {{ order.applied_coupon_code }}</p>
                                    <p class="mb-1"><strong>优惠金额:</strong> ¥{{ "%.2f"|format(order.discount_amount) }}</p>
                                    {% endif %}
                                    <p class="mb-0"><strong>支付金额:</strong> ¥{{ "%.2f"|format(order.price) }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6>重要提醒</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><i class="bi bi-check text-success"></i> 请保存好配置信息</li>
                                        <li><i class="bi bi-check text-success"></i> 支持多设备同时使用</li>
                                        <li><i class="bi bi-check text-success"></i> 如有问题请联系客服</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center">
                    <a href="{{ url_for('shop.order_status', order_id=order.order_id) }}" class="btn btn-primary me-2">
                        <i class="bi bi-eye"></i> 查看订单详情
                    </a>
                    <a href="{{ url_for('shop.shop_index') }}" class="btn btn-outline-primary">
                        <i class="bi bi-shop"></i> 继续购买
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> 使用说明
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>客户端下载</h6>
                            <ul>
                                <li><strong>Windows:</strong> v2rayN, Clash</li>
                                <li><strong>macOS:</strong> ClashX, V2rayU</li>
                                <li><strong>iOS:</strong> Shadowrocket, Quantumult X</li>
                                <li><strong>Android:</strong> v2rayNG, Clash</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>配置方法</h6>
                            <ol>
                                <li>下载并安装对应客户端</li>
                                <li>复制邮件中的配置链接</li>
                                <li>在客户端中导入配置</li>
                                <li>选择节点并开始使用</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 复制订阅链接
    function copySubscriptionUrl() {
        const urlInput = document.getElementById('subscriptionUrl');
        if (urlInput) {
            urlInput.select();
            urlInput.setSelectionRange(0, 99999);

            try {
                document.execCommand('copy');
                showNotification('订阅链接已复制到剪贴板', 'success');
            } catch (err) {
                showNotification('复制失败，请手动复制', 'error');
            }
        }
    }

    // 显示通知
    function showNotification(message, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const notification = `
            <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', notification);

        // 自动消失
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            if (alerts.length > 0) {
                alerts[alerts.length - 1].remove();
            }
        }, 3000);
    }
</script>
{% endblock %}
