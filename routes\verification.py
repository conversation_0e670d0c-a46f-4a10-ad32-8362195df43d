"""
验证码相关路由
"""
import logging
from flask import Blueprint, request, jsonify, session
from services.captcha_service import captcha_service
from services.verification_service import verification_service
from models import VerificationCodeType

logger = logging.getLogger(__name__)

verification_bp = Blueprint('verification', __name__, url_prefix='/api/verification')


@verification_bp.route('/captcha/generate', methods=['POST'])
def generate_captcha():
    """生成图形验证码"""
    try:
        success, message, data = captcha_service.generate_captcha()
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': data
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"生成图形验证码失败: {e}")
        return jsonify({
            'success': False,
            'message': '系统错误，请稍后重试'
        }), 500


@verification_bp.route('/captcha/check', methods=['POST'])
def check_captcha():
    """检查图形验证码（不标记为已使用，用于前端预验证）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据格式错误'
            }), 400

        session_id = data.get('session_id')
        captcha_code = data.get('captcha_code')

        if not session_id or not captcha_code:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        success, message, result_data = captcha_service.check_captcha(session_id, captcha_code)

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': result_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message,
                'data': result_data
            }), 400

    except Exception as e:
        logger.error(f"检查图形验证码失败: {e}")
        return jsonify({
            'success': False,
            'message': '系统错误，请稍后重试'
        }), 500


@verification_bp.route('/captcha/verify', methods=['POST'])
def verify_captcha():
    """验证图形验证码（会标记为已使用）"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据格式错误'
            }), 400

        session_id = data.get('session_id')
        captcha_code = data.get('captcha_code')

        if not session_id or not captcha_code:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400

        success, message, result_data = captcha_service.verify_captcha(session_id, captcha_code)

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': result_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message,
                'data': result_data
            }), 400

    except Exception as e:
        logger.error(f"验证图形验证码失败: {e}")
        return jsonify({
            'success': False,
            'message': '系统错误，请稍后重试'
        }), 500


@verification_bp.route('/email/send', methods=['POST'])
def send_email_verification():
    """发送邮件验证码"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据格式错误'
            }), 400
        
        email = data.get('email')
        session_id = data.get('session_id')
        captcha_code = data.get('captcha_code')
        code_type = data.get('code_type', VerificationCodeType.REGISTER)
        
        if not email or not session_id or not captcha_code:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        # 首先验证图形验证码
        captcha_success, captcha_message, _ = captcha_service.verify_captcha(session_id, captcha_code)
        
        if not captcha_success:
            return jsonify({
                'success': False,
                'message': f'图形验证码验证失败: {captcha_message}'
            }), 400
        
        # 图形验证码验证成功，发送邮件验证码
        success, message, result_data = verification_service.send_verification_code(email, code_type)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': result_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"发送邮件验证码失败: {e}")
        return jsonify({
            'success': False,
            'message': '系统错误，请稍后重试'
        }), 500


@verification_bp.route('/email/verify', methods=['POST'])
def verify_email_code():
    """验证邮件验证码"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据格式错误'
            }), 400
        
        email = data.get('email')
        email_code = data.get('email_code')
        code_type = data.get('code_type', VerificationCodeType.REGISTER)
        
        if not email or not email_code:
            return jsonify({
                'success': False,
                'message': '缺少必要参数'
            }), 400
        
        success, message, result_data = verification_service.verify_code(email, email_code, code_type)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'data': result_data
            })
        else:
            return jsonify({
                'success': False,
                'message': message,
                'data': result_data
            }), 400
            
    except Exception as e:
        logger.error(f"验证邮件验证码失败: {e}")
        return jsonify({
            'success': False,
            'message': '系统错误，请稍后重试'
        }), 500


@verification_bp.route('/cleanup', methods=['POST'])
def cleanup_expired_codes():
    """清理过期验证码（管理员功能）"""
    try:
        # 这里可以添加管理员权限检查

        captcha_count = captcha_service.cleanup_expired_codes()
        email_count = verification_service.cleanup_expired_codes()

        return jsonify({
            'success': True,
            'message': '清理完成',
            'data': {
                'captcha_cleaned': captcha_count,
                'email_cleaned': email_count
            }
        })

    except Exception as e:
        logger.error(f"清理过期验证码失败: {e}")
        return jsonify({
            'success': False,
            'message': '系统错误，请稍后重试'
        }), 500


@verification_bp.route('/traffic/collect', methods=['POST'])
def trigger_traffic_collection():
    """手动触发流量统计收集（管理员功能）"""
    try:
        # 这里可以添加管理员权限检查
        from services.scheduler_service import scheduler_service

        success = scheduler_service.trigger_traffic_stats_collection()

        return jsonify({
            'success': success,
            'message': '流量统计收集完成' if success else '流量统计收集失败'
        })

    except Exception as e:
        logger.error(f"手动触发流量统计收集失败: {e}")
        return jsonify({
            'success': False,
            'message': '系统错误，请稍后重试'
        }), 500


# 调试端点已移除，不应在生产环境中使用
