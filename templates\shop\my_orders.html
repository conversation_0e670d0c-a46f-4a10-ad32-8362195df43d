{% extends "base.html" %}

{% block title %}我的订单 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-list-ul"></i> 我的订单
            </h1>
        </div>
    </div>
    
    <!-- 查询表单 -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">查询订单</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('shop.my_orders') }}">
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ email or '' }}" placeholder="输入购买时使用的邮箱" required>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i> 查询订单
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 订单列表 -->
    {% if orders %}
    <div class="row">
        <div class="col-12">
            <h3>订单列表</h3>
            <p class="text-muted">邮箱：{{ email }}</p>
        </div>
    </div>
    
    <div class="row">
        {% for order in orders %}
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="fw-bold">订单 #{{ order.order_id }}</span>
                    <span class="badge bg-{{ 'success' if order.status.value == 'completed' else 'warning' if order.status.value == 'pending' else 'danger' }}">
                        {% if order.status.value == 'completed' %}
                            已完成
                        {% elif order.status.value == 'pending' %}
                            待处理
                        {% elif order.status.value == 'processing' %}
                            处理中
                        {% elif order.status.value == 'failed' %}
                            失败
                        {% elif order.status.value == 'cancelled' %}
                            已取消
                        {% else %}
                            {{ order.status.value }}
                        {% endif %}
                    </span>
                </div>
                <div class="card-body">
                    <h6 class="card-title">套餐信息</h6>
                    <ul class="list-unstyled">
                        <li><strong>协议：</strong> {{ order.node_type.value.upper() }}</li>
                        <li><strong>有效期：</strong> {{ order.duration_days }} 天</li>
                        <li><strong>流量：</strong> {{ order.traffic_limit_gb }}GB</li>
                        {% if order.applied_coupon_code %}
                        <li><strong>优惠码：</strong> {{ order.applied_coupon_code }}</li>
                        <li><strong>优惠金额：</strong> ¥{{ "%.2f"|format(order.discount_amount if order.discount_amount else 0) }}</li>
                        {% endif %}
                        <li><strong>支付金额：</strong>
                            {% if order.price == 0 and not order.applied_coupon_code %}
                                <span class="text-success">免费</span>
                            {% elif order.price == 0 and order.applied_coupon_code %}
                                 <span class="text-success">¥0.00 (已优惠)</span>
                            {% else %}
                                ¥{{ "%.2f"|format(order.price) }}
                            {% endif %}
                        </li>
                    </ul>
                    
                    {% if order.customer_remarks %}
                    <p><strong>备注：</strong> {{ order.customer_remarks }}</p>
                    {% endif %}
                    
                    <p class="text-muted">
                        <small>
                            创建时间：{{ order.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% if order.expires_at %}
                            <br>到期时间：{{ order.expires_at.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% endif %}
                        </small>
                    </p>
                    
                    {% if order.status.value == 'completed' and order.node_configs %}
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i> 
                        节点已激活，配置信息已发送到您的邮箱
                    </div>
                    {% elif order.status.value == 'pending' %}
                    <div class="alert alert-warning">
                        <i class="bi bi-clock"></i> 
                        订单待处理，请耐心等待
                    </div>
                    {% elif order.status.value == 'failed' %}
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle"></i> 
                        订单处理失败，请联系客服
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('shop.order_status', order_id=order.order_id) }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-eye"></i> 查看详情
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% elif email %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info text-center">
                <i class="bi bi-info-circle"></i> 
                未找到该邮箱的订单记录
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
