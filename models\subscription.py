"""
订阅相关模型
"""
from datetime import datetime
from . import db


class Subscription(db.Model):
    """订阅模型 - 管理订单的订阅链接"""
    __tablename__ = 'subscriptions'

    id = db.Column(db.Integer, primary_key=True)
    order_id = db.<PERSON>umn(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('orders.id'), nullable=False, unique=True)

    # 订阅令牌（用于生成订阅链接）
    subscription_token = db.Column(db.String(100), nullable=False, unique=True, index=True)

    # 订阅状态
    is_active = db.Column(db.<PERSON>, nullable=False, default=True)

    # 分组关联（从产品复制，用于流量统计）
    group_id = db.Column(db.Integer, db.ForeignKey('xui_panel_groups.id'), nullable=True)

    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=True)  # 订阅过期时间（从订单复制）

    # 关联订单和分组
    order = db.relationship('Order', foreign_keys=[order_id], backref=db.backref('subscription', uselist=False), lazy=True)
    group = db.relationship('XUIPanelGroup', backref='subscriptions', lazy=True)

    def __repr__(self):
        return f'<Subscription order_id={self.order_id} token={self.subscription_token[:8]}...>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'order_id': self.order_id,
            'subscription_token': self.subscription_token,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'expires_at': self.expires_at.isoformat() if self.expires_at else None
        }

    @property
    def is_expired(self):
        """检查订阅是否已过期"""
        if not self.expires_at:
            return False
        return self.expires_at < datetime.utcnow()
