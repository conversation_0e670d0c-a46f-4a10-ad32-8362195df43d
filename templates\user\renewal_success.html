{% extends "base.html" %}

{% block title %}续费成功 - 用户中心{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- 成功图标和标题 -->
            <div class="text-center mb-4">
                <div class="text-success mb-3">
                    <i class="bi bi-check-circle-fill" style="font-size: 4rem;"></i>
                </div>
                <h2 class="text-success">续费成功！</h2>
                <p class="text-muted">您的订阅已成功延长</p>
            </div>

            <!-- 续费详情卡片 -->
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-receipt"></i> 续费详情
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-4"><strong>订单号：</strong></div>
                        <div class="col-8">{{ order.order_id }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-4"><strong>续费时长：</strong></div>
                        <div class="col-8">{{ order.duration_days // 30 }}个月</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-4"><strong>支付金额：</strong></div>
                        <div class="col-8 text-success">¥{{ "%.2f"|format(order.price) }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-4"><strong>支付方式：</strong></div>
                        <div class="col-8">{{ order.payment_method or '在线支付' }}</div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-4"><strong>续费时间：</strong></div>
                        <div class="col-8">{{ order.updated_at.strftime('%Y-%m-%d %H:%M:%S') if order.updated_at else '-' }}</div>
                    </div>
                    {% if order.notes %}
                    <div class="row mb-3">
                        <div class="col-4"><strong>备注：</strong></div>
                        <div class="col-8">{{ order.notes }}</div>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 重要提示 -->
            <div class="alert alert-info mt-4">
                <h6><i class="bi bi-info-circle"></i> 重要提示</h6>
                <ul class="mb-0">
                    <li>您的订阅到期时间已自动延长{{ order.duration_days // 30 }}个月</li>
                    <li>X-UI面板中的客户端配置正在同步更新，可能需要几分钟时间</li>
                    <li>如果遇到连接问题，请稍后重试或联系客服</li>
                    <li>续费发票将在24小时内发送到您的邮箱</li>
                </ul>
            </div>

            <!-- 操作按钮 -->
            <div class="text-center mt-4">
                <a href="{{ url_for('user.dashboard') }}" class="btn btn-primary me-3">
                    <i class="bi bi-house"></i> 返回用户中心
                </a>
                <a href="{{ url_for('user.subscriptions') }}" class="btn btn-outline-primary">
                    <i class="bi bi-list-ul"></i> 查看订阅详情
                </a>
            </div>

            <!-- 客服联系方式 -->
            <div class="card mt-4">
                <div class="card-body text-center">
                    <h6>需要帮助？</h6>
                    <p class="text-muted mb-2">如果您在使用过程中遇到任何问题，请联系我们的客服团队</p>
                    <div class="d-flex justify-content-center gap-3">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-envelope"></i> 邮件客服
                        </a>
                        <a href="#" class="btn btn-outline-secondary btn-sm">
                            <i class="bi bi-chat-dots"></i> 在线客服
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 页面加载完成后的动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 成功图标动画
    const successIcon = document.querySelector('.bi-check-circle-fill');
    if (successIcon) {
        successIcon.style.transform = 'scale(0)';
        successIcon.style.transition = 'transform 0.5s ease-out';
        
        setTimeout(() => {
            successIcon.style.transform = 'scale(1)';
        }, 100);
    }
    
    // 卡片淡入动画
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 200 + index * 100);
    });
});
</script>
{% endblock %}
