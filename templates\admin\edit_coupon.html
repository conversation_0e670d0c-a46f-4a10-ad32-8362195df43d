{% extends "base.html" %}

{% block title %}编辑优惠券 - 管理后台{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4">编辑优惠券</h1>
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-edit mr-1"></i>
            编辑优惠券 #{{ coupon.id }} - {{ coupon.code }}
        </div>
        <div class="card-body">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <form method="POST" action="{{ url_for('admin.edit_coupon', coupon_id=coupon.id) }}">
                <div class="form-group">
                    <label for="code">优惠券代码</label>
                    <input type="text" class="form-control" id="code" name="code" value="{{ coupon.code }}" required>
                    <small class="form-text text-muted">例如：SAVE10, SUMMER2024</small>
                </div>

                <div class="form-group">
                    <label for="discount_percentage">折扣百分比 (%)</label>
                    <input type="number" step="0.01" class="form-control" id="discount_percentage" name="discount_percentage" value="{{ coupon.discount_percentage }}" required min="0.01" max="100">
                    <small class="form-text text-muted">例如：10 (表示10%折扣), 25.5 (表示25.5%折扣)</small>
                </div>

                <div class="form-group">
                    <label for="max_uses">最大使用次数</label>
                    <input type="number" class="form-control" id="max_uses" name="max_uses" value="{{ coupon.max_uses if coupon.max_uses is not none else '' }}" min="1" placeholder="留空表示无限制">
                    <small class="form-text text-muted">设置优惠券最多可以使用的次数，留空表示无限制使用</small>
                </div>

                <div class="form-group">
                    <label>当前使用情况</label>
                    <div class="form-control-plaintext">
                        已使用次数：<strong>{{ coupon.used_count }}</strong>
                        {% if coupon.max_uses is not none %}
                            / {{ coupon.max_uses }} (剩余 {{ coupon.get_remaining_uses() }} 次)
                        {% else %}
                            / 无限制
                        {% endif %}
                    </div>
                </div>

                <div class="form-group form-check">
                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" {% if coupon.is_active %}checked{% endif %}>
                    <label class="form-check-label" for="is_active">激活状态</label>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> 保存更改
                </button>
                <a href="{{ url_for('admin.list_coupons') }}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </form>
            <p class="mt-3">Coupon edit form will appear here. This is a basic placeholder.</p>
        </div>
    </div>
</div>
{% endblock %}
