{% extends "base.html" %}

{% block title %}优惠券管理 - 管理后台{% endblock %}

{% block content %}
<div class="container-fluid">
    <h1 class="mt-4">优惠券管理</h1>
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-ticket-alt mr-1"></i>
            优惠券列表
            <a href="{{ url_for('admin.create_coupon') }}" class="btn btn-primary btn-sm float-right">
                <i class="fas fa-plus"></i> 创建新优惠券
            </a>
        </div>
        <div class="card-body">
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <div class="table-responsive">
                <table class="table table-bordered" id="couponsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>代码</th>
                            <th>折扣 (%)</th>
                            <th>使用次数</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if coupons %}
                            {% for coupon in coupons %}
                            <tr>
                                <td>{{ coupon.id }}</td>
                                <td>{{ coupon.code }}</td>
                                <td>{{ coupon.discount_percentage }}</td>
                                <td>
                                    {% if coupon.max_uses is none %}
                                        <span class="text-muted">{{ coupon.used_count }}/无限制</span>
                                    {% else %}
                                        <span class="{% if coupon.used_count >= coupon.max_uses %}text-danger{% else %}text-info{% endif %}">
                                            {{ coupon.used_count }}/{{ coupon.max_uses }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if coupon.is_active %}
                                        <span class="badge badge-success">有效</span>
                                    {% else %}
                                        <span class="badge badge-secondary">无效</span>
                                    {% endif %}
                                </td>
                                <td>{{ coupon.created_at.strftime('%Y-%m-%d %H:%M:%S') if coupon.created_at else '' }}</td>
                                <td>{{ coupon.updated_at.strftime('%Y-%m-%d %H:%M:%S') if coupon.updated_at else '' }}</td>
                                <td>
                                    <a href="{{ url_for('admin.edit_coupon', coupon_id=coupon.id) }}" class="btn btn-info btn-sm" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if coupon.is_active %}
                                    <form action="{{ url_for('admin.deactivate_coupon', coupon_id=coupon.id) }}" method="POST" style="display:inline;">
                                        <button type="submit" class="btn btn-warning btn-sm" title="停用" onclick="return confirm('确定要停用此优惠券吗？');">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </form>
                                    {% else %}
                                    <form action="{{ url_for('admin.activate_coupon', coupon_id=coupon.id) }}" method="POST" style="display:inline;">
                                        <button type="submit" class="btn btn-success btn-sm" title="激活" onclick="return confirm('确定要激活此优惠券吗？');">
                                            <i class="fas fa-check-circle"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                    <form action="{{ url_for('admin.delete_coupon', coupon_id=coupon.id) }}" method="POST" style="display:inline;">
                                        <button type="submit" class="btn btn-danger btn-sm" title="删除" onclick="return confirm('确定要删除此优惠券吗？删除后无法恢复！');">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="8" class="text-center">暂无优惠券</td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
            <p class="mt-3">Coupon list will appear here. Placeholder for full listing and pagination if needed.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    //$('#couponsTable').DataTable(); // Enable if DataTable is desired
});
</script>
{% endblock %}
