"""
用户中心路由
"""
import logging
from functools import wraps
from flask import Blueprint, request, session, render_template, redirect, url_for, flash, jsonify
from models import db, User, UserRole, Order
from services.subscription_service import SubscriptionService

logger = logging.getLogger(__name__)

user_bp = Blueprint('user', __name__)
subscription_service = SubscriptionService()

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function

@user_bp.route('/dashboard')
@login_required
def dashboard():
    """用户中心主页"""
    try:
        user_id = session['user_id']
        user = User.query.get(user_id)
        
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('auth.login'))
        
        # 获取用户订阅
        subscriptions = subscription_service.get_user_subscriptions(user_id)
        
        # 统计信息
        total_subscriptions = len(subscriptions)
        active_subscriptions = len([s for s in subscriptions if s['is_active']])
        expired_subscriptions = len([s for s in subscriptions if s['is_expired']])
        
        # 计算总流量统计（MB单位）
        total_traffic_mb = 0
        total_limit_mb = 0
        total_remaining_mb = 0

        for subscription in subscriptions:
            if subscription['is_active']:
                stats = subscription['traffic_stats']
                total_traffic_mb += stats['total_traffic_mb']
                total_limit_mb += stats['traffic_limit_mb']
                total_remaining_mb += stats['remaining_mb']
        
        # 生成订阅链接
        subscription_url = subscription_service.generate_subscription_url(
            user_id, 
            request.host_url.rstrip('/')
        )
        
        return render_template('user/dashboard.html',
                             user=user,
                             subscriptions=subscriptions,
                             stats={
                                 'total_subscriptions': total_subscriptions,
                                 'active_subscriptions': active_subscriptions,
                                 'expired_subscriptions': expired_subscriptions,
                                 'total_traffic_mb': round(total_traffic_mb, 2),
                                 'total_limit_mb': round(total_limit_mb, 2),
                                 'total_remaining_mb': round(total_remaining_mb, 2),
                                 'usage_percentage': round((total_traffic_mb / total_limit_mb * 100) if total_limit_mb > 0 else 0, 1)
                             },
                             subscription_url=subscription_url)
        
    except Exception as e:
        logger.error(f"用户中心加载失败 user_id={session.get('user_id')}: {e}")
        flash('加载用户中心失败', 'error')
        return redirect(url_for('shop.shop_index'))

@user_bp.route('/subscriptions')
@login_required
def subscriptions():
    """订阅管理页面"""
    try:
        user_id = session['user_id']
        user = User.query.get(user_id)
        
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('auth.login'))
        
        # 获取用户订阅
        subscriptions = subscription_service.get_user_subscriptions(user_id)
        logger.info(f"User {user_id} subscriptions: {len(subscriptions)} items")

        # 生成订阅链接
        subscription_url = subscription_service.generate_subscription_url(
            user_id,
            request.host_url.rstrip('/')
        )

        return render_template('user/subscriptions.html',
                             user=user,
                             subscriptions=subscriptions,
                             subscription_url=subscription_url)
        
    except Exception as e:
        logger.error(f"订阅管理页面加载失败 user_id={session.get('user_id')}: {e}")
        flash('加载订阅管理页面失败', 'error')
        return redirect(url_for('user.dashboard'))

@user_bp.route('/subscription/<order_id>')
@login_required
def subscription_detail(order_id):
    """订阅详情页面"""
    try:
        user_id = session['user_id']
        user = User.query.get(user_id)
        
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('auth.login'))
        
        # 获取用户订阅
        subscriptions = subscription_service.get_user_subscriptions(user_id)
        
        # 查找指定的订阅
        subscription = None
        for sub in subscriptions:
            if sub['order_id'] == order_id:
                subscription = sub
                break
        
        if not subscription:
            flash('订阅不存在', 'error')
            return redirect(url_for('user.subscriptions'))
        
        return render_template('user/subscription_detail.html',
                             user=user,
                             subscription=subscription)
        
    except Exception as e:
        logger.error(f"订阅详情页面加载失败 user_id={session.get('user_id')}, order_id={order_id}: {e}")
        flash('加载订阅详情失败', 'error')
        return redirect(url_for('user.subscriptions'))

# {{CHENGQI:
# Action: Removed
# Timestamp: 2025-06-08 17:55:00 +08:00
# Task_ID: P4-LD-004
# Principle_Applied: YAGNI - 移除不再需要的手动刷新功能
# Language: Python
# Description: 移除手动刷新流量统计的API路由，因为现在直接从数据库读取
# }}
# 流量刷新API已移除 - 现在流量数据直接从数据库读取，无需手动刷新

@user_bp.route('/api/subscription/url')
@login_required
def get_subscription_url():
    """获取用户的订阅链接"""
    try:
        user_id = session['user_id']
        
        # 生成订阅链接
        subscription_url = subscription_service.generate_subscription_url(
            user_id, 
            request.host_url.rstrip('/')
        )
        
        return jsonify({
            'subscription_url': subscription_url,
            'user_id': user_id
        })
        
    except Exception as e:
        logger.error(f"获取订阅链接失败 user_id={session.get('user_id')}: {e}")
        return jsonify({
            'error': '获取订阅链接失败'
        }), 500

@user_bp.route('/profile')
@login_required
def profile():
    """用户资料页面"""
    try:
        user_id = session['user_id']
        user = User.query.get(user_id)
        
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('auth.login'))
        
        return render_template('user/profile.html', user=user)
        
    except Exception as e:
        logger.error(f"用户资料页面加载失败 user_id={session.get('user_id')}: {e}")
        flash('加载用户资料失败', 'error')
        return redirect(url_for('user.dashboard'))

@user_bp.route('/profile', methods=['POST'])
@login_required
def update_profile():
    """更新用户资料"""
    try:
        user_id = session['user_id']
        user = User.query.get(user_id)
        
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('auth.login'))
        
        # 获取表单数据
        full_name = request.form.get('full_name', '').strip()
        phone = request.form.get('phone', '').strip()
        
        # 更新用户信息
        user.full_name = full_name if full_name else None
        user.phone = phone if phone else None
        
        db.session.commit()
        
        flash('资料更新成功', 'success')
        return redirect(url_for('user.profile'))
        
    except Exception as e:
        logger.error(f"更新用户资料失败 user_id={session.get('user_id')}: {e}")
        db.session.rollback()
        flash('更新资料失败', 'error')
        return redirect(url_for('user.profile'))

@user_bp.route('/change-password', methods=['POST'])
@login_required
def change_password():
    """修改密码"""
    try:
        user_id = session['user_id']
        user = User.query.get(user_id)
        
        if not user:
            flash('用户不存在', 'error')
            return redirect(url_for('auth.login'))
        
        # 获取表单数据
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # 验证当前密码
        if not user.check_password(current_password):
            flash('当前密码错误', 'error')
            return redirect(url_for('user.profile'))
        
        # 验证新密码
        if len(new_password) < 6:
            flash('新密码长度至少6位', 'error')
            return redirect(url_for('user.profile'))
        
        if new_password != confirm_password:
            flash('两次输入的新密码不一致', 'error')
            return redirect(url_for('user.profile'))
        
        # 更新密码
        user.set_password(new_password)
        db.session.commit()
        
        flash('密码修改成功', 'success')
        return redirect(url_for('user.profile'))
        
    except Exception as e:
        logger.error(f"修改密码失败 user_id={session.get('user_id')}: {e}")
        db.session.rollback()
        flash('修改密码失败', 'error')
        return redirect(url_for('user.profile'))
