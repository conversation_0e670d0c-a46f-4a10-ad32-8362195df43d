"""
续费服务 - 处理用户订阅续费逻辑
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from models import db, User, Order, Subscription, RenewalPricing, RenewalTask, RenewalTaskStatus
from models.enums import OrderStatus
import secrets

logger = logging.getLogger(__name__)


class RenewalService:
    """续费服务类"""
    
    def __init__(self):
        self.xui_manager = None

    def get_renewal_pricing(self) -> List[Dict]:
        """获取所有启用的续费价格配置"""
        try:
            pricing_configs = RenewalPricing.get_active_pricing()
            return [config.to_dict() for config in pricing_configs]
        except Exception as e:
            logger.error(f"获取续费价格配置失败: {e}")
            return []

    def calculate_renewal_price(self, subscription: Subscription, duration_months: int) -> Dict:
        """
        计算续费价格
        
        Args:
            subscription: 订阅对象
            duration_months: 续费月数
            
        Returns:
            Dict: 包含价格信息的字典
        """
        try:
            # 获取原订单信息
            order = subscription.order
            if not order:
                raise ValueError("订阅没有关联的订单")

            # 获取续费价格配置
            pricing_config = RenewalPricing.get_pricing_by_duration(duration_months)
            if not pricing_config:
                raise ValueError(f"没有找到{duration_months}个月的续费价格配置")

            # 计算基础价格（按月计算）
            if order.duration_days and order.duration_days > 0:
                monthly_price = order.price / order.duration_days * 30
            else:
                # 如果duration_days为0或None，假设为30天
                monthly_price = order.price
            base_price = monthly_price * duration_months

            # 计算折扣
            discount_percentage = pricing_config.discount_percentage
            discount_amount = base_price * (discount_percentage / 100)
            final_price = base_price - discount_amount

            return {
                'duration_months': duration_months,
                'base_price': round(base_price, 2),
                'discount_percentage': discount_percentage,
                'discount_amount': round(discount_amount, 2),
                'final_price': round(final_price, 2),
                'monthly_price': round(monthly_price, 2),
                'original_order_price': order.price,
                'original_duration_days': order.duration_days
            }

        except Exception as e:
            logger.error(f"计算续费价格失败: {e}")
            raise

    def get_user_active_subscription(self, user_id: int) -> Optional[Subscription]:
        """获取用户的活跃订阅"""
        try:
            # 获取用户的活跃订阅
            # 明确指定连接条件以避免多外键关系的歧义
            subscription = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Order.user_id == user_id,
                Order.status == OrderStatus.COMPLETED,
                Subscription.is_active == True,
                Subscription.expires_at > datetime.utcnow()
            ).first()

            return subscription
        except Exception as e:
            logger.error(f"获取用户活跃订阅失败: {e}")
            return None

    def create_renewal_order(self, user_id: int, subscription_id: int, duration_months: int) -> Tuple[bool, Optional[Order], Optional[str]]:
        """
        创建续费订单
        
        Args:
            user_id: 用户ID
            subscription_id: 订阅ID
            duration_months: 续费月数
            
        Returns:
            Tuple[bool, Optional[Order], Optional[str]]: (是否成功, 订单对象, 错误信息)
        """
        try:
            # 验证用户和订阅
            user = User.query.get(user_id)
            if not user:
                return False, None, "用户不存在"

            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                return False, None, "订阅不存在"

            # 验证订阅属于该用户
            if subscription.order.user_id != user_id:
                return False, None, "订阅不属于该用户"

            # 验证订阅是否活跃
            if not subscription.is_active:
                return False, None, "订阅已失效"

            # 计算续费价格
            price_info = self.calculate_renewal_price(subscription, duration_months)

            # 生成订单ID
            order_id = f"R{secrets.token_hex(8).upper()}"

            # 创建续费订单
            renewal_order = Order(
                order_id=order_id,
                user_id=user_id,
                customer_email=user.email,
                customer_name=user.username,
                order_type='renewal',
                parent_subscription_id=subscription_id,
                
                # 产品配置（复制原订单）
                node_type=subscription.order.node_type,
                duration_days=duration_months * 30,  # 转换为天数
                traffic_limit_gb=subscription.order.traffic_limit_gb,
                price=price_info['final_price'],
                
                # 订单状态
                status=OrderStatus.PENDING,
                
                # 备注
                notes=f"续费{duration_months}个月，折扣{price_info['discount_percentage']}%"
            )

            db.session.add(renewal_order)
            db.session.commit()

            logger.info(f"创建续费订单成功: {order_id}, 用户: {user_id}, 订阅: {subscription_id}")
            return True, renewal_order, None

        except Exception as e:
            logger.error(f"创建续费订单失败: {e}")
            db.session.rollback()
            return False, None, str(e)

    def process_renewal_payment(self, order_id: str, payment_method: str = "manual", payment_id: str = None) -> Tuple[bool, Optional[str]]:
        """
        处理续费支付（简化版，实际应该集成支付网关）
        
        Args:
            order_id: 订单ID
            payment_method: 支付方式
            payment_id: 支付ID
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            # 获取续费订单
            order = Order.query.filter_by(order_id=order_id, order_type='renewal').first()
            if not order:
                return False, "续费订单不存在"

            if order.status != OrderStatus.PENDING:
                return False, f"订单状态不正确: {order.status.value}"

            # 更新订单状态
            order.status = OrderStatus.COMPLETED
            order.payment_method = payment_method
            order.payment_id = payment_id or f"PAY_{secrets.token_hex(8).upper()}"

            # 处理续费逻辑
            success, error_msg = self._process_subscription_renewal(order)
            if not success:
                order.status = OrderStatus.FAILED
                order.notes = f"续费处理失败: {error_msg}"
                db.session.commit()
                return False, error_msg

            db.session.commit()
            logger.info(f"续费支付处理成功: {order_id}")
            return True, None

        except Exception as e:
            logger.error(f"处理续费支付失败: {e}")
            db.session.rollback()
            return False, str(e)

    def _process_subscription_renewal(self, renewal_order: Order) -> Tuple[bool, Optional[str]]:
        """
        处理订阅续费逻辑
        
        Args:
            renewal_order: 续费订单
            
        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            # 获取原订阅
            subscription = Subscription.query.get(renewal_order.parent_subscription_id)
            if not subscription:
                return False, "原订阅不存在"

            # 计算新的到期时间
            current_expiry = subscription.expires_at or datetime.utcnow()
            additional_days = renewal_order.duration_days
            new_expiry = current_expiry + timedelta(days=additional_days)

            # 更新订阅到期时间
            subscription.expires_at = new_expiry
            subscription.updated_at = datetime.utcnow()

            # 创建续费任务
            renewal_task = RenewalTask(
                subscription_id=subscription.id,
                order_id=renewal_order.id,
                renewal_months=renewal_order.duration_days // 30,
                original_expiry=current_expiry,
                new_expiry=new_expiry,
                status=RenewalTaskStatus.PENDING
            )

            db.session.add(renewal_task)
            db.session.flush()  # 获取任务ID

            # 异步更新X-UI客户端到期时间
            success, error_msg = self._update_xui_clients_expiry(renewal_task)
            
            if success:
                renewal_task.mark_completed()
            elif error_msg:
                renewal_task.mark_partial_success(error_msg)
            else:
                renewal_task.mark_failed("X-UI客户端更新失败")

            return True, None

        except Exception as e:
            logger.error(f"处理订阅续费失败: {e}")
            return False, str(e)

    def _update_xui_clients_expiry(self, renewal_task: RenewalTask) -> Tuple[bool, Optional[str]]:
        """
        更新X-UI客户端到期时间

        Args:
            renewal_task: 续费任务

        Returns:
            Tuple[bool, Optional[str]]: (是否成功, 错误信息)
        """
        try:
            subscription = renewal_task.subscription
            if not subscription:
                return False, "订阅不存在"

            # 获取订阅关联的订单和节点配置
            order = subscription.order
            if not order or not order.node_configs:
                return False, "订单或节点配置不存在"

            # 计算新的到期时间（毫秒时间戳）
            new_expiry_timestamp = int(renewal_task.new_expiry.timestamp() * 1000)

            # 初始化面板更新状态
            panel_status = {}
            success_count = 0
            total_count = 0
            error_messages = []

            # 获取订阅分组的面板
            if subscription.group_id:
                # 使用分组面板
                from models import XUIPanelGroup
                group = XUIPanelGroup.query.get(subscription.group_id)
                if group and group.panels:
                    panels = group.panels
                else:
                    return False, f"分组 {subscription.group_id} 不存在或没有面板"
            else:
                # 使用所有活跃面板
                from models import XUIPanel, PanelStatus
                panels = XUIPanel.query.filter_by(status=PanelStatus.ACTIVE).all()

            if not panels:
                return False, "没有可用的X-UI面板"

            # 遍历每个节点配置
            for node_config in order.node_configs:
                if not node_config.is_active or not node_config.client_email:
                    continue

                client_email = node_config.client_email
                logger.info(f"开始更新客户端 {client_email} 的到期时间")

                # 在每个面板中查找并更新客户端
                client_updated = False
                for panel in panels:
                    try:
                        from xui_client import XUIClient

                        # 创建XUI客户端
                        xui_client = XUIClient(
                            base_url=panel.base_url,
                            username=panel.username,
                            password=panel.password,
                            path_prefix=panel.path_prefix
                        )

                        # 尝试更新客户端到期时间
                        success, error_msg = xui_client.update_client_expiry_by_email(
                            client_email, new_expiry_timestamp
                        )

                        panel_id = str(panel.id)
                        if success:
                            panel_status[panel_id] = {
                                'status': 'success',
                                'client_email': client_email,
                                'message': '更新成功'
                            }
                            success_count += 1
                            client_updated = True
                            logger.info(f"成功在面板 {panel.name} 中更新客户端 {client_email}")
                            break  # 成功更新后跳出面板循环
                        else:
                            panel_status[panel_id] = {
                                'status': 'failed',
                                'client_email': client_email,
                                'message': error_msg or '更新失败'
                            }
                            logger.warning(f"在面板 {panel.name} 中更新客户端 {client_email} 失败: {error_msg}")

                    except Exception as e:
                        panel_id = str(panel.id)
                        error_msg = f"面板连接错误: {str(e)}"
                        panel_status[panel_id] = {
                            'status': 'error',
                            'client_email': client_email,
                            'message': error_msg
                        }
                        logger.error(f"连接面板 {panel.name} 时发生错误: {e}")

                total_count += 1
                if not client_updated:
                    error_messages.append(f"客户端 {client_email} 在所有面板中都更新失败")

            # 更新任务状态
            renewal_task.set_panel_update_status(panel_status)

            # 判断整体结果
            if success_count == 0:
                return False, f"所有客户端更新失败: {'; '.join(error_messages)}"
            elif success_count < total_count:
                return True, f"部分客户端更新成功 ({success_count}/{total_count}): {'; '.join(error_messages)}"
            else:
                return True, None

        except Exception as e:
            logger.error(f"更新X-UI客户端到期时间失败: {e}")
            return False, str(e)
