"""
数据库迁移脚本 - 添加协议模板相关表
"""
import sqlite3
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def migrate_database(db_path='node_sales.db'):
    """执行数据库迁移"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否已经存在协议模板表
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='protocol_templates'
        """)
        
        if cursor.fetchone():
            logger.info("协议模板表已存在，跳过创建")
            conn.close()
            return True
        
        logger.info("开始创建协议模板相关表...")
        
        # 1. 创建协议模板表
        cursor.execute("""
            CREATE TABLE protocol_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                protocol_type VARCHAR(20) NOT NULL,
                template_content TEXT NOT NULL,
                custom_variables TEXT,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                is_default BOOLEAN NOT NULL DEFAULT 0,
                usage_count INTEGER NOT NULL DEFAULT 0,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        
        # 2. 创建协议变量表
        cursor.execute("""
            CREATE TABLE protocol_variables (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                scope_type VARCHAR(20) NOT NULL,
                scope_id INTEGER NOT NULL,
                variables TEXT NOT NULL,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 3. 为NodeConfig表添加协议模板相关字段
        try:
            cursor.execute("ALTER TABLE node_configs ADD COLUMN protocol_template_id INTEGER")
            cursor.execute("ALTER TABLE node_configs ADD COLUMN custom_variables TEXT")
            cursor.execute("ALTER TABLE node_configs ADD COLUMN generated_config TEXT")
            logger.info("成功为node_configs表添加协议模板字段")
        except sqlite3.OperationalError as e:
            if "duplicate column name" in str(e).lower():
                logger.info("node_configs表的协议模板字段已存在，跳过添加")
            else:
                raise e
        
        # 4. 创建索引
        cursor.execute("CREATE INDEX idx_protocol_templates_type ON protocol_templates(protocol_type)")
        cursor.execute("CREATE INDEX idx_protocol_templates_active ON protocol_templates(is_active)")
        cursor.execute("CREATE INDEX idx_protocol_templates_default ON protocol_templates(protocol_type, is_default)")
        cursor.execute("CREATE INDEX idx_protocol_variables_scope ON protocol_variables(scope_type, scope_id)")
        cursor.execute("CREATE INDEX idx_node_configs_template ON node_configs(protocol_template_id)")
        
        # 5. 插入默认协议模板
        default_templates = [
            {
                'name': 'VLESS默认模板',
                'description': 'VLESS协议的默认配置模板',
                'protocol_type': 'vless',
                'template_content': 'vless://{uuid}@{server}:{port}?type={network}&security={security}&path={path}&host={host}#{remark}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': 1
            },
            {
                'name': 'VMess默认模板',
                'description': 'VMess协议的默认配置模板',
                'protocol_type': 'vmess',
                'template_content': 'vmess://{vmess_config_base64}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': 1
            },
            {
                'name': 'Trojan默认模板',
                'description': 'Trojan协议的默认配置模板',
                'protocol_type': 'trojan',
                'template_content': 'trojan://{password}@{server}:{port}?security=tls&sni={sni}#{remark}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': 1
            },
            {
                'name': 'Shadowsocks默认模板',
                'description': 'Shadowsocks协议的默认配置模板',
                'protocol_type': 'shadowsocks',
                'template_content': 'ss://{method_password_base64}@{server}:{port}#{remark}',
                'custom_variables': '{"remark": "{region}-{provider}-{email}"}',
                'is_default': 1
            }
        ]
        
        for template in default_templates:
            cursor.execute("""
                INSERT INTO protocol_templates 
                (name, description, protocol_type, template_content, custom_variables, is_default, is_active)
                VALUES (?, ?, ?, ?, ?, ?, 1)
            """, (
                template['name'],
                template['description'],
                template['protocol_type'],
                template['template_content'],
                template['custom_variables'],
                template['is_default']
            ))
        
        # 6. 插入一些示例变量配置
        sample_variables = [
            {
                'scope_type': 'panel',
                'scope_id': 1,  # 假设存在ID为1的面板
                'variables': '{"region": "HK", "provider": "CloudFlare"}'
            },
            {
                'scope_type': 'product',
                'scope_id': 1,  # 假设存在ID为1的产品
                'variables': '{"remark_prefix": "Premium", "speed_limit": "1Gbps"}'
            }
        ]
        
        for var_config in sample_variables:
            # 检查是否存在对应的面板或产品
            if var_config['scope_type'] == 'panel':
                cursor.execute("SELECT id FROM xui_panels WHERE id = ?", (var_config['scope_id'],))
            elif var_config['scope_type'] == 'product':
                cursor.execute("SELECT id FROM products WHERE id = ?", (var_config['scope_id'],))
            
            if cursor.fetchone():
                cursor.execute("""
                    INSERT INTO protocol_variables (scope_type, scope_id, variables)
                    VALUES (?, ?, ?)
                """, (
                    var_config['scope_type'],
                    var_config['scope_id'],
                    var_config['variables']
                ))
        
        conn.commit()
        conn.close()
        
        logger.info("协议模板数据库迁移完成")
        return True
        
    except Exception as e:
        logger.error(f"协议模板数据库迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def rollback_migration(db_path='node_sales.db'):
    """回滚迁移（仅用于开发测试）"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("开始回滚协议模板迁移...")
        
        # 删除添加的字段（SQLite不支持DROP COLUMN，需要重建表）
        # 这里只删除表，字段保留以避免数据丢失
        
        # 删除协议模板相关表
        cursor.execute("DROP TABLE IF EXISTS protocol_variables")
        cursor.execute("DROP TABLE IF EXISTS protocol_templates")
        
        conn.commit()
        conn.close()
        
        logger.info("协议模板迁移回滚完成")
        return True
        
    except Exception as e:
        logger.error(f"协议模板迁移回滚失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == '__main__':
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 执行迁移
    success = migrate_database()
    if success:
        print("✅ 协议模板数据库迁移成功")
    else:
        print("❌ 协议模板数据库迁移失败")
