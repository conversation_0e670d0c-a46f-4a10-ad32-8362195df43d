# 部署指南

## 快速部署

### 1. 环境准备

```bash
# 确保Python 3.8+已安装
python3 --version

# 克隆或下载项目
git clone <your-repo-url>
cd xui

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

**必需配置项**：
```env
# X-UI面板配置
XUI_BASE_URL=http://************:54321
XUI_USERNAME=admin
XUI_PASSWORD=your_password

# 邮件服务配置
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=true
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_DEFAULT_SENDER=<EMAIL>
```

### 3. 启动系统

```bash
# 使用启动脚本（推荐）
python start.py

# 或直接启动
python app.py
```

## 生产环境部署

### 使用Gunicorn

```bash
# 安装Gunicorn
pip install gunicorn

# 启动服务
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 使用Docker

```bash
# 构建镜像
docker build -t node-sales-system .

# 运行容器
docker run -d \
  -p 5000:5000 \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/data:/app/data \
  --name node-sales \
  node-sales-system
```

### 使用Nginx反向代理

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 系统监控

### 健康检查

```bash
# 检查系统状态
curl http://localhost:5000/health

# 检查API响应
curl -X POST http://localhost:5000/api/orders/create \
  -H "Content-Type: application/json" \
  -d '{"customer_email":"<EMAIL>"}'
```

### 日志监控

```bash
# 查看应用日志
tail -f node_sales.log

# 查看系统日志
journalctl -u your-service-name -f
```

## 数据备份

### 数据库备份

```bash
# SQLite备份
cp node_sales.db node_sales_backup_$(date +%Y%m%d_%H%M%S).db

# 定期备份脚本
#!/bin/bash
BACKUP_DIR="/path/to/backup"
DATE=$(date +%Y%m%d_%H%M%S)
cp node_sales.db "$BACKUP_DIR/node_sales_$DATE.db"
find "$BACKUP_DIR" -name "node_sales_*.db" -mtime +7 -delete
```

### 配置备份

```bash
# 备份配置文件
tar -czf config_backup_$(date +%Y%m%d).tar.gz .env config.py
```

## 故障排除

### 常见问题

1. **X-UI连接失败**
   - 检查X-UI面板是否正常运行
   - 验证URL、用户名和密码
   - 确认网络连通性

2. **邮件发送失败**
   - 检查SMTP服务器配置
   - 验证邮箱密码（使用应用专用密码）
   - 确认防火墙设置

3. **数据库错误**
   - 检查数据库文件权限
   - 运行数据库初始化脚本
   - 查看详细错误日志

### 调试模式

```bash
# 启用调试模式
export FLASK_ENV=development
export LOG_LEVEL=DEBUG
python app.py
```

### 性能优化

1. **数据库优化**
   - 定期清理过期数据
   - 添加必要的索引
   - 考虑使用PostgreSQL

2. **缓存优化**
   - 使用Redis缓存频繁查询
   - 实现API响应缓存

3. **负载均衡**
   - 使用多个应用实例
   - 配置负载均衡器

## 安全建议

1. **网络安全**
   - 使用HTTPS
   - 配置防火墙规则
   - 限制API访问频率

2. **数据安全**
   - 定期备份数据
   - 加密敏感信息
   - 实现访问日志

3. **系统安全**
   - 定期更新依赖
   - 使用强密码
   - 监控异常访问

## 维护计划

### 日常维护
- 检查系统状态
- 监控日志文件
- 验证备份完整性

### 周期维护
- 更新系统依赖
- 清理临时文件
- 性能分析报告

### 紧急响应
- 系统故障处理流程
- 数据恢复程序
- 联系方式和升级路径
