{% extends "base.html" %}

{% block title %}用户中心 - 节点商城{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">
                        <i class="bi bi-person-circle text-primary me-2"></i>
                        用户中心
                    </h2>
                    <p class="text-muted mb-0">欢迎回来，{{ user.username }}！</p>
                </div>
                <!-- 移除流量刷新按钮 - 现在流量数据直接从数据库读取 -->
            </div>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-5">
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm stats-card-primary text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="fw-bold mb-1">{{ stats.total_subscriptions }}</h3>
                            <p class="mb-0 opacity-75">总订阅数</p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-collection" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm stats-card-success text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="fw-bold mb-1">{{ stats.active_subscriptions }}</h3>
                            <p class="mb-0 opacity-75">活跃订阅</p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-check-circle" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm stats-card-info text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="fw-bold mb-1">{{ stats.total_traffic_mb }}MB</h3>
                            <p class="mb-0 opacity-75">已用流量</p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-cloud-download" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm stats-card-warning text-white h-100 hover-lift">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="fw-bold mb-1">{{ stats.total_remaining_mb }}MB</h3>
                            <p class="mb-0 opacity-75">剩余流量</p>
                        </div>
                        <div class="bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="bi bi-hdd" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速访问订阅链接 -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-link-45deg text-primary me-2"></i>
                        订阅管理
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">您可以在订阅管理页面查看和管理所有节点配置：</p>
                    <a href="{{ url_for('user.subscriptions') }}" class="btn btn-primary">
                        <i class="bi bi-collection me-1"></i> 前往订阅管理
                    </a>
                    <small class="text-muted mt-2 d-block">
                        <i class="bi bi-info-circle me-1"></i>
                        在订阅管理页面可以获取通用订阅链接和查看所有节点详情
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- 快捷操作 -->
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="fw-bold mb-3">快捷操作</h4>
        </div>
        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-collection text-primary" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">订阅管理</h5>
                    <p class="text-muted mb-4">查看和管理您的所有订阅</p>
                    <a href="{{ url_for('user.subscriptions') }}" class="btn btn-primary">
                        <i class="bi bi-arrow-right me-1"></i> 进入管理
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-shop text-success" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">购买套餐</h5>
                    <p class="text-muted mb-4">选择适合您的节点套餐</p>
                    <a href="{{ url_for('shop.shop_index') }}" class="btn btn-success">
                        <i class="bi bi-arrow-right me-1"></i> 去购买
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-list-ul text-info" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">我的订单</h5>
                    <p class="text-muted mb-4">查看订单历史和状态</p>
                    <a href="{{ url_for('shop.my_orders') }}" class="btn btn-info">
                        <i class="bi bi-arrow-right me-1"></i> 查看订单
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100 text-center hover-lift">
                <div class="card-body p-4">
                    <div class="bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-person text-warning" style="font-size: 2rem;"></i>
                    </div>
                    <h5 class="fw-bold mb-2">个人资料</h5>
                    <p class="text-muted mb-4">管理您的账户信息</p>
                    <a href="{{ url_for('user.profile') }}" class="btn btn-warning">
                        <i class="bi bi-arrow-right me-1"></i> 编辑资料
                    </a>
                </div>
            </div>
        </div>
    </div>


    </div>

    <!-- 最近订阅 -->
    {% if subscriptions %}
    <div class="row">
        <div class="col-12">
            <h4 class="fw-bold mb-3">最近订阅</h4>
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 px-4 py-3">套餐名称</th>
                                    <th class="border-0 px-4 py-3">状态</th>
                                    <th class="border-0 px-4 py-3">流量使用</th>
                                    <th class="border-0 px-4 py-3">到期时间</th>
                                    <th class="border-0 px-4 py-3">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscription in subscriptions[:5] %}
                                <tr>
                                    <td class="px-4 py-3">
                                        <div class="d-flex align-items-center">
                                            <i class="bi bi-{{ 'check-circle text-success' if subscription.is_active else 'x-circle text-danger' }} me-2"></i>
                                            <div>
                                                <div class="fw-semibold">{{ subscription.product_name }}</div>
                                                <small class="text-muted">{{ subscription.order_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        {% if subscription.is_expired %}
                                            <span class="badge bg-danger">已过期</span>
                                        {% elif subscription.is_active %}
                                            <span class="badge bg-success">活跃</span>
                                        {% else %}
                                            <span class="badge bg-secondary">停用</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="progress me-2" style="width: 100px; height: 8px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ subscription.traffic_stats.usage_percentage }}%"
                                                     aria-valuenow="{{ subscription.traffic_stats.usage_percentage }}" 
                                                     aria-valuemin="0" aria-valuemax="100"></div>
                                            </div>
                                            <small class="text-muted">
                                                {{ subscription.traffic_stats.total_traffic_mb }}MB / {{ subscription.traffic_stats.traffic_limit_mb }}MB
                                            </small>
                                        </div>
                                    </td>
                                    <td class="px-4 py-3">
                                        {% if subscription.expires_at %}
                                            <small class="text-muted">{{ subscription.expires_at.strftime('%Y-%m-%d') }}</small>
                                        {% else %}
                                            <small class="text-muted">永久</small>
                                        {% endif %}
                                    </td>
                                    <td class="px-4 py-3">
                                        <a href="{{ url_for('user.subscription_detail', order_id=subscription.order_id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i> 详情
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% if subscriptions|length > 5 %}
                <div class="card-footer bg-light border-0 text-center">
                    <a href="{{ url_for('user.subscriptions') }}" class="btn btn-link text-decoration-none">
                        查看全部订阅 <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // 复制订阅链接
    function copySubscriptionUrl() {
        const urlInput = document.getElementById('subscriptionUrl');
        urlInput.select();
        urlInput.setSelectionRange(0, 99999);
        
        try {
            document.execCommand('copy');
            showNotification('订阅链接已复制到剪贴板', 'success');
        } catch (err) {
            showNotification('复制失败，请手动复制', 'error');
        }
    }
    
    // 移除流量刷新功能 - 现在流量数据直接从数据库读取，无需手动刷新
    
    // 显示通知
    function showNotification(message, type) {
        // 这里可以使用Bootstrap的Toast或其他通知组件
        alert(message);
    }
</script>
{% endblock %}
