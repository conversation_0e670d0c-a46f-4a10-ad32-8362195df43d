#!/usr/bin/env python3
"""
数据库迁移脚本：添加分组功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from models.database import db, XUIPanelGroup, XUIPanelGroupMembership, GroupRole
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_add_groups():
    """添加分组功能的数据库迁移"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始数据库迁移：添加分组功能")
            
            # 创建新表
            logger.info("创建分组相关表...")
            db.create_all()
            
            # 检查是否已有分组数据
            existing_groups = XUIPanelGroup.query.count()
            if existing_groups > 0:
                logger.info(f"发现已存在 {existing_groups} 个分组，跳过初始化")
                return
            
            # 创建默认分组
            logger.info("创建默认分组...")
            
            default_groups = [
                {
                    'name': '默认分组',
                    'description': '系统默认分组，包含所有未分组的面板',
                    'color': '#007bff',
                    'priority': 1,
                    'is_active': True
                },
                {
                    'name': '高性能组',
                    'description': '高性能服务器分组，适用于高级套餐',
                    'color': '#28a745',
                    'priority': 2,
                    'is_active': True
                },
                {
                    'name': '备用组',
                    'description': '备用服务器分组，用于故障转移',
                    'color': '#ffc107',
                    'priority': 3,
                    'is_active': True
                }
            ]
            
            created_groups = []
            for group_data in default_groups:
                group = XUIPanelGroup(**group_data)
                db.session.add(group)
                created_groups.append(group)
                logger.info(f"创建分组: {group_data['name']}")
            
            db.session.commit()
            logger.info(f"成功创建 {len(created_groups)} 个默认分组")
            
            # 将现有面板添加到默认分组
            from models.database import XUIPanel
            panels = XUIPanel.query.all()
            
            if panels and created_groups:
                default_group = created_groups[0]  # 使用第一个分组作为默认分组
                logger.info(f"将 {len(panels)} 个现有面板添加到默认分组...")
                
                for panel in panels:
                    membership = XUIPanelGroupMembership(
                        panel_id=panel.id,
                        group_id=default_group.id,
                        weight=1,
                        role=GroupRole.PRIMARY
                    )
                    db.session.add(membership)
                    logger.info(f"面板 {panel.name} 已添加到默认分组")
                
                db.session.commit()
                logger.info("现有面板已成功添加到默认分组")
            
            logger.info("数据库迁移完成！")
            
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            db.session.rollback()
            raise

def rollback_groups():
    """回滚分组功能（仅用于开发测试）"""
    app = create_app()
    
    with app.app_context():
        try:
            logger.info("开始回滚分组功能...")
            
            # 删除分组成员关系
            XUIPanelGroupMembership.query.delete()
            logger.info("删除分组成员关系")
            
            # 删除分组
            XUIPanelGroup.query.delete()
            logger.info("删除分组")
            
            db.session.commit()
            logger.info("分组功能回滚完成")
            
        except Exception as e:
            logger.error(f"回滚失败: {e}")
            db.session.rollback()
            raise

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='分组功能数据库迁移')
    parser.add_argument('--rollback', action='store_true', help='回滚分组功能（仅用于开发测试）')
    
    args = parser.parse_args()
    
    if args.rollback:
        confirm = input("确定要回滚分组功能吗？这将删除所有分组数据！(yes/no): ")
        if confirm.lower() == 'yes':
            rollback_groups()
        else:
            print("回滚操作已取消")
    else:
        migrate_add_groups()
