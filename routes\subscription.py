"""
订阅路由 - 处理订阅链接和配置下载
"""
import base64
import logging
from flask import Blueprint, request, Response, jsonify, abort, make_response
from services.subscription_service import SubscriptionService

logger = logging.getLogger(__name__)

subscription_bp = Blueprint('subscription', __name__)
subscription_service = SubscriptionService()

@subscription_bp.route('/subscription/<token>')
def get_subscription(token):
    """
    获取订阅配置
    
    Args:
        token: 订阅令牌 (Base64编码的用户标识)
    
    Returns:
        Base64编码的v2ray配置文本
    """
    try:
        # 解码令牌
        try:
            decoded = base64.b64decode(token).decode()
        except Exception:
            logger.warning(f"无效的订阅令牌: {token}")
            abort(404)
        
        # 根据令牌类型处理
        if decoded.startswith('user_'):
            # 用户ID订阅
            encoded_config = subscription_service.get_subscription_configs(token)
        elif decoded.startswith('email_'):
            # 邮箱订阅
            email = decoded.replace('email_', '')
            encoded_config = subscription_service.get_email_subscription_configs(email)
        else:
            logger.warning(f"未知的令牌格式: {decoded}")
            abort(404)
        
        if encoded_config is None:
            logger.info(f"未找到有效的订阅配置: {token}")
            abort(404)
        
        # 返回Base64编码的配置
        response = Response(
            encoded_config,
            mimetype='text/plain',
            headers={
                'Content-Type': 'text/plain; charset=utf-8',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        )
        
        logger.info(f"成功返回订阅配置: {token}")
        return response
        
    except Exception as e:
        logger.error(f"获取订阅配置失败 token={token}: {e}")
        abort(500)

@subscription_bp.route('/api/subscription/info/<token>')
def get_subscription_info(token):
    """
    获取订阅信息（不返回配置，只返回统计信息）
    
    Args:
        token: 订阅令牌
    
    Returns:
        JSON格式的订阅信息
    """
    try:
        # 解码令牌
        try:
            decoded = base64.b64decode(token).decode()
        except Exception:
            return jsonify({'error': '无效的订阅令牌'}), 400
        
        # 根据令牌类型处理
        if decoded.startswith('user_'):
            user_id = int(decoded.replace('user_', ''))
            subscriptions = subscription_service.get_user_subscriptions(user_id)
        elif decoded.startswith('email_'):
            email = decoded.replace('email_', '')
            subscriptions = subscription_service.get_user_by_email_subscriptions(email)
        else:
            return jsonify({'error': '未知的令牌格式'}), 400
        
        # 统计信息
        total_subscriptions = len(subscriptions)
        active_subscriptions = len([s for s in subscriptions if s['is_active']])
        expired_subscriptions = len([s for s in subscriptions if s['is_expired']])
        
        # 计算总流量统计（MB单位）
        total_traffic_mb = 0
        total_limit_mb = 0
        total_remaining_mb = 0
        
        for subscription in subscriptions:
            if subscription['is_active']:
                stats = subscription['traffic_stats']
                total_traffic_mb += stats['total_traffic_mb']
                total_limit_mb += stats['traffic_limit_mb']
                total_remaining_mb += stats['remaining_mb']
        
        return jsonify({
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': expired_subscriptions,
            'traffic_stats': {
                'total_used_mb': round(total_traffic_mb, 2),
                'total_limit_mb': round(total_limit_mb, 2),
                'total_remaining_mb': round(total_remaining_mb, 2),
                'usage_percentage': round((total_traffic_mb / total_limit_mb * 100) if total_limit_mb > 0 else 0, 1)
            },
            'subscriptions': subscriptions
        })
        
    except Exception as e:
        logger.error(f"获取订阅信息失败 token={token}: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@subscription_bp.route('/api/subscription/generate', methods=['POST'])
def generate_subscription_url():
    """
    生成订阅链接
    
    Request Body:
        {
            "type": "user" | "email",
            "identifier": "user_id" | "email_address",
            "base_url": "http://example.com"
        }
    
    Returns:
        JSON格式的订阅链接
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        subscription_type = data.get('type')
        identifier = data.get('identifier')
        base_url = data.get('base_url', request.host_url.rstrip('/'))
        
        if not subscription_type or not identifier:
            return jsonify({'error': '缺少必要参数'}), 400
        
        if subscription_type == 'user':
            try:
                user_id = int(identifier)
                subscription_url = subscription_service.generate_subscription_url(user_id, base_url)
            except ValueError:
                return jsonify({'error': '无效的用户ID'}), 400
        elif subscription_type == 'email':
            subscription_url = subscription_service.generate_email_subscription_url(identifier, base_url)
        else:
            return jsonify({'error': '无效的订阅类型'}), 400

        return jsonify({
            'subscription_url': subscription_url,
            'type': subscription_type,
            'identifier': identifier
        })

    except Exception as e:
        logger.error(f"生成订阅链接失败: {e}")
        return jsonify({'error': '服务器内部错误'}), 500

@subscription_bp.route('/order/<subscription_token>')
def order_subscription(subscription_token):
    """
    基于订单的订阅端点

    Args:
        subscription_token: 订阅令牌

    Returns:
        Base64编码的v2ray配置文本
    """
    try:
        # 获取订单订阅配置
        encoded_config = subscription_service.get_order_subscription_configs(subscription_token)

        if encoded_config is None:
            logger.info(f"未找到有效的订单订阅配置: {subscription_token[:8]}...")
            abort(404)

        # 返回Base64编码的配置（在浏览器中直接显示）
        response = make_response(encoded_config)
        response.headers['Content-Type'] = 'text/plain; charset=utf-8'

        # 添加缓存控制头
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

        logger.info(f"成功返回订单订阅配置: {subscription_token[:8]}...")
        return response

    except Exception as e:
        logger.error(f"获取订单订阅配置失败 token={subscription_token[:8]}...: {e}")
        abort(500)

@subscription_bp.route('/api/order/info/<subscription_token>')
def get_order_subscription_info(subscription_token):
    """
    获取订单订阅信息API

    Args:
        subscription_token: 订阅令牌

    Returns:
        JSON格式的订阅信息
    """
    try:
        subscription_info = subscription_service.get_subscription_by_token(subscription_token)

        if not subscription_info:
            return jsonify({'error': '订阅不存在或已失效'}), 404

        return jsonify(subscription_info)

    except Exception as e:
        logger.error(f"获取订单订阅信息失败 token={subscription_token[:8]}...: {e}")
        return jsonify({'error': '获取订阅信息失败'}), 500

@subscription_bp.route('/api/subscription/validate/<token>')
def validate_subscription_token(token):
    """
    验证订阅令牌是否有效
    
    Args:
        token: 订阅令牌
    
    Returns:
        JSON格式的验证结果
    """
    try:
        # 解码令牌
        try:
            decoded = base64.b64decode(token).decode()
        except Exception:
            return jsonify({'valid': False, 'error': '无效的令牌格式'})
        
        # 检查令牌格式
        if decoded.startswith('user_'):
            try:
                user_id = int(decoded.replace('user_', ''))
                subscriptions = subscription_service.get_user_subscriptions(user_id)
                has_active = any(s['is_active'] for s in subscriptions)
                return jsonify({
                    'valid': True,
                    'type': 'user',
                    'identifier': user_id,
                    'has_active_subscriptions': has_active,
                    'subscription_count': len(subscriptions)
                })
            except ValueError:
                return jsonify({'valid': False, 'error': '无效的用户ID'})
        elif decoded.startswith('email_'):
            email = decoded.replace('email_', '')
            subscriptions = subscription_service.get_user_by_email_subscriptions(email)
            has_active = any(s['is_active'] for s in subscriptions)
            return jsonify({
                'valid': True,
                'type': 'email',
                'identifier': email,
                'has_active_subscriptions': has_active,
                'subscription_count': len(subscriptions)
            })
        else:
            return jsonify({'valid': False, 'error': '未知的令牌类型'})
        
    except Exception as e:
        logger.error(f"验证订阅令牌失败 token={token}: {e}")
        return jsonify({'valid': False, 'error': '服务器内部错误'})

@subscription_bp.route('/api/subscription/refresh/<token>', methods=['POST'])
def refresh_subscription_traffic(token):
    """
    刷新订阅的流量统计
    
    Args:
        token: 订阅令牌
    
    Returns:
        JSON格式的刷新结果
    """
    try:
        # 解码令牌
        try:
            decoded = base64.b64decode(token).decode()
        except Exception:
            return jsonify({'error': '无效的订阅令牌'}), 400
        
        # 根据令牌类型处理
        if decoded.startswith('user_'):
            user_id = int(decoded.replace('user_', ''))
            subscriptions = subscription_service.get_user_subscriptions(user_id)
        elif decoded.startswith('email_'):
            email = decoded.replace('email_', '')
            subscriptions = subscription_service.get_user_by_email_subscriptions(email)
        else:
            return jsonify({'error': '未知的令牌格式'}), 400
        
        # 统计刷新结果
        refreshed_count = 0
        for subscription in subscriptions:
            if subscription['is_active']:
                refreshed_count += 1
        
        return jsonify({
            'success': True,
            'refreshed_subscriptions': refreshed_count,
            'total_subscriptions': len(subscriptions),
            'message': f'已刷新 {refreshed_count} 个活跃订阅的流量统计'
        })
        
    except Exception as e:
        logger.error(f"刷新订阅流量统计失败 token={token}: {e}")
        return jsonify({'error': '服务器内部错误'}), 500
