"""
协议模板服务 - 管理协议模板和变量替换
"""
import json
import base64
import logging
import re
from typing import Dict, List, Optional, Any
from models import db
from models.protocol_template import ProtocolTemplate, ProtocolVariable

logger = logging.getLogger(__name__)


class ProtocolTemplateService:
    """协议模板管理服务"""
    
    def __init__(self):
        self.system_variables = [
            'server', 'port', 'uuid', 'email', 'path', 'host', 'sni', 
            'security', 'network', 'alpn', 'flow', 'password'
        ]
    
    def get_all_templates(self, protocol_type: str = None) -> List[Dict]:
        """获取所有模板"""
        try:
            templates = ProtocolTemplate.get_active_templates(protocol_type)
            return [template.to_dict() for template in templates]
        except Exception as e:
            logger.error(f"获取协议模板失败: {e}")
            return []
    
    def get_template_by_id(self, template_id: int) -> Optional[Dict]:
        """根据ID获取模板"""
        try:
            template = ProtocolTemplate.query.get(template_id)
            return template.to_dict() if template else None
        except Exception as e:
            logger.error(f"获取协议模板失败 template_id={template_id}: {e}")
            return None
    
    def create_template(self, name: str, protocol_type: str, template_content: str,
                       description: str = None, custom_variables: Dict = None,
                       created_by: int = None) -> Dict:
        """创建协议模板"""
        try:
            # 验证模板内容
            validation_result = self.validate_template(template_content)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': f"模板验证失败: {validation_result['error']}"
                }
            
            # 检查是否已存在同名模板
            existing = ProtocolTemplate.query.filter_by(
                name=name, protocol_type=protocol_type
            ).first()
            if existing:
                return {
                    'success': False,
                    'error': '同协议类型下已存在同名模板'
                }
            
            # 创建模板
            template = ProtocolTemplate(
                name=name,
                description=description,
                protocol_type=protocol_type,
                template_content=template_content,
                custom_variables=json.dumps(custom_variables or {}, ensure_ascii=False),
                created_by=created_by
            )
            
            db.session.add(template)
            db.session.commit()
            
            logger.info(f"创建协议模板成功: {name} ({protocol_type})")
            return {
                'success': True,
                'template': template.to_dict()
            }
            
        except Exception as e:
            logger.error(f"创建协议模板失败: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': f'创建失败: {str(e)}'
            }
    
    def update_template(self, template_id: int, **kwargs) -> Dict:
        """更新协议模板"""
        try:
            template = ProtocolTemplate.query.get(template_id)
            if not template:
                return {
                    'success': False,
                    'error': '模板不存在'
                }
            
            # 验证模板内容（如果有更新）
            if 'template_content' in kwargs:
                validation_result = self.validate_template(kwargs['template_content'])
                if not validation_result['valid']:
                    return {
                        'success': False,
                        'error': f"模板验证失败: {validation_result['error']}"
                    }
            
            # 更新字段
            for key, value in kwargs.items():
                if hasattr(template, key):
                    if key == 'custom_variables' and isinstance(value, dict):
                        setattr(template, key, json.dumps(value, ensure_ascii=False))
                    else:
                        setattr(template, key, value)
            
            db.session.commit()
            
            logger.info(f"更新协议模板成功: {template.name}")
            return {
                'success': True,
                'template': template.to_dict()
            }
            
        except Exception as e:
            logger.error(f"更新协议模板失败 template_id={template_id}: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': f'更新失败: {str(e)}'
            }
    
    def delete_template(self, template_id: int) -> Dict:
        """删除协议模板"""
        try:
            template = ProtocolTemplate.query.get(template_id)
            if not template:
                return {
                    'success': False,
                    'error': '模板不存在'
                }
            
            # 检查是否为默认模板
            if template.is_default:
                return {
                    'success': False,
                    'error': '不能删除默认模板'
                }
            
            # 检查是否有节点在使用
            if template.node_configs:
                return {
                    'success': False,
                    'error': f'模板正在被 {len(template.node_configs)} 个节点使用，无法删除'
                }
            
            db.session.delete(template)
            db.session.commit()
            
            logger.info(f"删除协议模板成功: {template.name}")
            return {
                'success': True
            }
            
        except Exception as e:
            logger.error(f"删除协议模板失败 template_id={template_id}: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': f'删除失败: {str(e)}'
            }
    
    def set_default_template(self, template_id: int) -> Dict:
        """设置默认模板"""
        try:
            template = ProtocolTemplate.query.get(template_id)
            if not template:
                return {
                    'success': False,
                    'error': '模板不存在'
                }
            
            # 取消同协议类型的其他默认模板
            ProtocolTemplate.query.filter_by(
                protocol_type=template.protocol_type,
                is_default=True
            ).update({'is_default': False})
            
            # 设置为默认
            template.is_default = True
            db.session.commit()
            
            logger.info(f"设置默认模板成功: {template.name}")
            return {
                'success': True
            }
            
        except Exception as e:
            logger.error(f"设置默认模板失败 template_id={template_id}: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': f'设置失败: {str(e)}'
            }
    
    def validate_template(self, template_content: str) -> Dict:
        """验证模板内容"""
        try:
            # 检查基本格式
            if not template_content or not template_content.strip():
                return {
                    'valid': False,
                    'error': '模板内容不能为空'
                }
            
            # 提取变量
            variables = re.findall(r'\{([^}]+)\}', template_content)
            
            # 检查变量格式
            for var in variables:
                if not re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*$', var):
                    return {
                        'valid': False,
                        'error': f'变量名格式错误: {var}'
                    }
            
            return {
                'valid': True,
                'variables': list(set(variables))
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': f'验证失败: {str(e)}'
            }
    
    def preview_template(self, template_content: str, variables: Dict = None) -> Dict:
        """预览模板效果"""
        try:
            # 使用示例数据
            sample_data = {
                'server': 'example.com',
                'port': '443',
                'uuid': '550e8400-e29b-41d4-a716-************',
                'email': '<EMAIL>',
                'path': '/ws',
                'host': 'example.com',
                'sni': 'example.com',
                'security': 'tls',
                'network': 'ws',
                'password': 'password123',
                'region': 'HK',
                'provider': 'CloudFlare',
                'remark': '<EMAIL>'
            }
            
            # 合并用户提供的变量
            if variables:
                sample_data.update(variables)
            
            # 替换变量
            result = self.replace_variables(template_content, sample_data)
            
            return {
                'success': True,
                'preview': result,
                'variables_used': list(re.findall(r'\{([^}]+)\}', template_content))
            }
            
        except Exception as e:
            logger.error(f"预览模板失败: {e}")
            return {
                'success': False,
                'error': f'预览失败: {str(e)}'
            }
    
    def replace_variables(self, template: str, variables: Dict) -> str:
        """替换模板中的变量"""
        try:
            result = template
            
            # 替换所有变量
            for key, value in variables.items():
                placeholder = f'{{{key}}}'
                if placeholder in result:
                    result = result.replace(placeholder, str(value))
            
            return result
            
        except Exception as e:
            logger.error(f"变量替换失败: {e}")
            return template
    
    def get_variables_for_scope(self, scope_type: str, scope_id: int) -> Dict:
        """获取指定作用域的变量"""
        return ProtocolVariable.get_variables(scope_type, scope_id)
    
    def set_variables_for_scope(self, scope_type: str, scope_id: int, variables: Dict) -> bool:
        """设置指定作用域的变量"""
        return ProtocolVariable.set_variables(scope_type, scope_id, variables)
    
    def resolve_all_variables(self, template_id: int, node_config_data: Dict,
                             panel_id: int = None, product_id: int = None) -> Dict:
        """解析所有层级的变量"""
        try:
            all_variables = {}
            
            # 1. 模板默认变量
            template = ProtocolTemplate.query.get(template_id)
            if template and template.custom_variables:
                try:
                    template_vars = json.loads(template.custom_variables)
                    all_variables.update(template_vars)
                except:
                    pass
            
            # 2. 产品级变量
            if product_id:
                product_vars = self.get_variables_for_scope('product', product_id)
                all_variables.update(product_vars)
            
            # 3. 面板级变量
            if panel_id:
                panel_vars = self.get_variables_for_scope('panel', panel_id)
                all_variables.update(panel_vars)
            
            # 4. 节点级变量
            if 'node_id' in node_config_data:
                node_vars = self.get_variables_for_scope('node', node_config_data['node_id'])
                all_variables.update(node_vars)
            
            # 5. 系统变量（最高优先级）
            system_vars = {
                'server': node_config_data.get('server_address', ''),
                'port': str(node_config_data.get('server_port', '')),
                'uuid': node_config_data.get('client_id', ''),
                'email': node_config_data.get('client_email', ''),
                'path': node_config_data.get('path', '/'),
                'host': node_config_data.get('host', ''),
                'sni': node_config_data.get('sni', ''),
                'security': node_config_data.get('security', 'none'),
                'network': node_config_data.get('network', 'tcp'),
                'password': node_config_data.get('password', ''),
                'alpn': node_config_data.get('alpn', ''),
                'flow': node_config_data.get('flow', '')
            }
            all_variables.update(system_vars)
            
            # 递归解析变量引用
            resolved_variables = self._resolve_variable_references(all_variables)
            
            return resolved_variables
            
        except Exception as e:
            logger.error(f"解析变量失败: {e}")
            return {}
    
    def _resolve_variable_references(self, variables: Dict, max_depth: int = 5) -> Dict:
        """递归解析变量引用"""
        resolved = variables.copy()
        
        for _ in range(max_depth):
            changed = False
            for key, value in resolved.items():
                if isinstance(value, str) and '{' in value:
                    new_value = self.replace_variables(value, resolved)
                    if new_value != value:
                        resolved[key] = new_value
                        changed = True
            
            if not changed:
                break
        
        return resolved


# 全局服务实例
protocol_template_service = ProtocolTemplateService()
