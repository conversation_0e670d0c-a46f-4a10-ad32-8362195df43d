"""
测试流量数据库集成功能
验证从数据库查询流量数据而非API调用的功能
"""
import unittest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# 导入需要测试的模块
from services.traffic_stats_service import TrafficStatsService
from services.subscription_service import SubscriptionService
from models import db, User, Order, Subscription, TrafficStats, OrderStatus
from models.database import NodeType


class TestTrafficDatabaseIntegration(unittest.TestCase):
    """流量数据库集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.traffic_service = TrafficStatsService()
        self.subscription_service = SubscriptionService()
        
        # 创建测试数据
        self.test_user = User(
            username='testuser',
            email='<EMAIL>'
        )
        self.test_user.set_password('password123')
        
        self.test_order = Order(
            order_id='TEST001',
            user_id=1,
            customer_email='<EMAIL>',
            customer_name='Test User',
            node_type=NodeType.VLESS,
            duration_days=30,
            traffic_limit_gb=100,
            price=29.99,
            status=OrderStatus.COMPLETED,
            expires_at=datetime.utcnow() + timedelta(days=30)
        )
        
        self.test_subscription = Subscription(
            order_id=1,
            subscription_token='test_token_123',
            is_active=True,
            group_id=1,
            expires_at=datetime.utcnow() + timedelta(days=30)
        )
        
        self.test_traffic_stats = TrafficStats(
            user_id=1,
            subscription_id=1,
            group_id=1,
            upload_bytes=1024 * 1024 * 500,  # 500MB
            download_bytes=1024 * 1024 * 1500,  # 1500MB
            total_bytes=1024 * 1024 * 2000,  # 2000MB
            recorded_at=datetime.utcnow()
        )
    
    def test_get_subscription_latest_traffic_stats(self):
        """测试从数据库获取订阅最新流量统计"""
        with patch('services.traffic_stats_service.TrafficStats') as mock_traffic_stats:
            # 模拟数据库查询结果
            mock_stats = MagicMock()
            mock_stats.upload_bytes = 1024 * 1024 * 500  # 500MB
            mock_stats.download_bytes = 1024 * 1024 * 1500  # 1500MB
            mock_stats.total_bytes = 1024 * 1024 * 2000  # 2000MB
            mock_stats.recorded_at = datetime.utcnow()
            
            mock_traffic_stats.query.filter_by.return_value.order_by.return_value.first.return_value = mock_stats
            
            # 执行测试
            result = self.traffic_service.get_subscription_latest_traffic_stats(1, self.test_order)
            
            # 验证结果
            self.assertEqual(result['total_up_mb'], 500.0)
            self.assertEqual(result['total_down_mb'], 1500.0)
            self.assertEqual(result['total_traffic_mb'], 2000.0)
            self.assertEqual(result['traffic_limit_mb'], 102400)  # 100GB = 102400MB
            self.assertAlmostEqual(result['usage_percentage'], 1.95, places=1)  # 2000/102400*100
            self.assertEqual(result['remaining_mb'], 100400.0)  # 102400-2000
    
    def test_get_subscription_latest_traffic_stats_no_data(self):
        """测试没有流量统计数据的情况"""
        with patch('services.traffic_stats_service.TrafficStats') as mock_traffic_stats:
            # 模拟没有数据的情况
            mock_traffic_stats.query.filter_by.return_value.order_by.return_value.first.return_value = None
            
            # 执行测试
            result = self.traffic_service.get_subscription_latest_traffic_stats(1, self.test_order)
            
            # 验证结果
            self.assertEqual(result['total_up_mb'], 0)
            self.assertEqual(result['total_down_mb'], 0)
            self.assertEqual(result['total_traffic_mb'], 0)
            self.assertEqual(result['traffic_limit_mb'], 102400)
            self.assertEqual(result['usage_percentage'], 0)
            self.assertEqual(result['remaining_mb'], 102400.0)
            self.assertIsNone(result['last_updated'])
    
    def test_get_order_traffic_stats_from_db(self):
        """测试从数据库获取订单流量统计"""
        with patch('services.traffic_stats_service.Subscription') as mock_subscription:
            # 模拟订阅查询
            mock_sub = MagicMock()
            mock_sub.id = 1
            mock_subscription.query.filter_by.return_value.first.return_value = mock_sub
            
            with patch.object(self.traffic_service, 'get_subscription_latest_traffic_stats') as mock_get_stats:
                # 模拟流量统计结果
                expected_stats = {
                    'total_up_mb': 500.0,
                    'total_down_mb': 1500.0,
                    'total_traffic_mb': 2000.0,
                    'traffic_limit_mb': 102400,
                    'usage_percentage': 1.95,
                    'remaining_mb': 100400.0,
                    'last_updated': datetime.utcnow()
                }
                mock_get_stats.return_value = expected_stats
                
                # 执行测试
                result = self.traffic_service.get_order_traffic_stats_from_db(self.test_order)
                
                # 验证结果
                self.assertEqual(result, expected_stats)
                mock_get_stats.assert_called_once_with(1, self.test_order)
    
    def test_subscription_service_uses_database(self):
        """测试SubscriptionService使用数据库查询而非API调用"""
        with patch('services.subscription_service.TrafficStatsService') as mock_traffic_service_class:
            # 模拟TrafficStatsService实例
            mock_traffic_service = MagicMock()
            mock_traffic_service_class.return_value = mock_traffic_service
            
            # 模拟数据库查询结果
            expected_stats = {
                'total_up_mb': 300.0,
                'total_down_mb': 700.0,
                'total_traffic_mb': 1000.0,
                'traffic_limit_mb': 51200,  # 50GB
                'usage_percentage': 1.95,
                'remaining_mb': 50200.0,
                'last_updated': datetime.utcnow()
            }
            mock_traffic_service.get_order_traffic_stats_from_db.return_value = expected_stats
            
            # 执行测试
            result = self.subscription_service._get_order_traffic_stats(self.test_order)
            
            # 验证结果
            self.assertEqual(result, expected_stats)
            mock_traffic_service.get_order_traffic_stats_from_db.assert_called_once_with(self.test_order)
    
    def test_no_api_calls_in_traffic_stats(self):
        """验证流量统计不再调用X-UI API"""
        with patch('services.subscription_service.TrafficStatsService') as mock_traffic_service_class:
            mock_traffic_service = MagicMock()
            mock_traffic_service_class.return_value = mock_traffic_service
            mock_traffic_service.get_order_traffic_stats_from_db.return_value = {
                'total_up_mb': 0,
                'total_down_mb': 0,
                'total_traffic_mb': 0,
                'traffic_limit_mb': 102400,
                'usage_percentage': 0,
                'remaining_mb': 102400.0,
                'last_updated': None
            }
            
            # 确保没有导入MultiXUIManager
            with patch.dict('sys.modules', {'multi_xui_manager': None}):
                # 执行测试
                result = self.subscription_service._get_order_traffic_stats(self.test_order)
                
                # 验证没有API调用相关的错误
                self.assertIsInstance(result, dict)
                self.assertIn('total_traffic_mb', result)


if __name__ == '__main__':
    unittest.main()
