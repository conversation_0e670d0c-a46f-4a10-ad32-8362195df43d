{% extends "base.html" %}

{% block title %}订阅管理 - 节点商城{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="fw-bold mb-1">
                        <i class="bi bi-collection text-primary me-2"></i>
                        订阅管理
                    </h2>
                    <p class="text-muted mb-0">管理您的所有节点订阅</p>
                </div>
                <div>
                    <a href="{{ url_for('user.dashboard') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-1"></i> 返回中心
                    </a>
                    {% if subscriptions and subscriptions|selectattr('is_active')|list %}
                    <a href="{{ url_for('renewal.renewal_page') }}" class="btn btn-warning me-2 fw-bold text-dark">
                        <i class="bi bi-arrow-clockwise me-1"></i> 订阅续费
                    </a>
                    {% endif %}
                    <!-- 移除全局流量刷新按钮 - 现在流量数据直接从数据库读取 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 订阅链接卡片 -->
    <div class="row mb-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 fw-bold">
                        <i class="bi bi-link-45deg me-2"></i>
                        通用订阅链接
                    </h5>
                </div>
                <div class="card-body">
                    <p class="mb-3">将此链接添加到您的v2ray客户端中，即可自动获取所有有效节点配置：</p>
                    <div class="input-group mb-3">
                        <input type="text" class="form-control" id="subscriptionUrl" value="{{ subscription_url }}" readonly>
                        <button class="btn btn-success" type="button" onclick="copySubscriptionUrl()">
                            <i class="bi bi-clipboard me-1"></i> 复制链接
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold">支持的客户端：</h6>
                            <ul class="list-unstyled mb-0">
                                <li><i class="bi bi-check-circle text-success me-1"></i> v2rayN (Windows)</li>
                                <li><i class="bi bi-check-circle text-success me-1"></i> v2rayNG (Android)</li>
                                <li><i class="bi bi-check-circle text-success me-1"></i> Shadowrocket (iOS)</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold">使用说明：</h6>
                            <ul class="list-unstyled mb-0">
                                <li><i class="bi bi-info-circle text-info me-1"></i> 配置会自动更新</li>
                                <li><i class="bi bi-info-circle text-info me-1"></i> 包含所有有效节点</li>
                                <li><i class="bi bi-info-circle text-info me-1"></i> 过期节点自动移除</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 订阅列表 -->
    {% if subscriptions %}
    <div class="row">
        <div class="col-12">
            <h4 class="fw-bold mb-3">我的订阅 ({{ subscriptions|length }})</h4>
            
            {% for subscription in subscriptions %}
            <div class="card border-0 shadow-sm mb-4 hover-lift">
                <div class="card-header bg-light border-0">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0 fw-bold">
                                <i class="bi bi-{{ 'check-circle text-success' if subscription.is_active else 'x-circle text-danger' }} me-2"></i>
                                {{ subscription.product_name }}
                            </h5>
                            <small class="text-muted">订单号：{{ subscription.order_id }}</small>
                        </div>
                        <div class="col-md-6 text-md-end">
                            {% if subscription.is_expired %}
                                <span class="badge bg-danger px-3 py-2">已过期</span>
                            {% elif subscription.is_active %}
                                <span class="badge bg-success px-3 py-2">活跃中</span>
                            {% else %}
                                <span class="badge bg-secondary px-3 py-2">已停用</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 基本信息 -->
                        <div class="col-lg-4 mb-3">
                            <h6 class="fw-bold mb-3">基本信息</h6>
                            <div class="row g-2">
                                <div class="col-6">
                                    <small class="text-muted">协议类型</small>
                                    <div class="fw-semibold">{{ subscription.node_type.upper() }}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">有效期</small>
                                    <div class="fw-semibold">{{ subscription.duration_days }} 天</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">节点数量</small>
                                    <div class="fw-semibold">{{ subscription.node_count }} 个</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">购买价格</small>
                                    <div class="fw-semibold text-primary">¥{{ subscription.price }}</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 流量统计 -->
                        <div class="col-lg-4 mb-3">
                            <h6 class="fw-bold mb-3">流量统计</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <small class="text-muted">已用流量</small>
                                    <small class="fw-semibold">{{ subscription.traffic_stats.usage_percentage }}%</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar 
                                        {% if subscription.traffic_stats.usage_percentage >= 90 %}bg-danger
                                        {% elif subscription.traffic_stats.usage_percentage >= 70 %}bg-warning
                                        {% else %}bg-success{% endif %}" 
                                         role="progressbar" 
                                         style="width: {{ subscription.traffic_stats.usage_percentage }}%"
                                         aria-valuenow="{{ subscription.traffic_stats.usage_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <div class="row g-2">
                                <div class="col-6">
                                    <small class="text-muted">已用</small>
                                    <div class="fw-semibold">{{ subscription.traffic_stats.total_traffic_mb }}MB</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">剩余</small>
                                    <div class="fw-semibold">{{ subscription.traffic_stats.remaining_mb }}MB</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">上传</small>
                                    <div class="fw-semibold">{{ subscription.traffic_stats.total_up_mb }}MB</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">下载</small>
                                    <div class="fw-semibold">{{ subscription.traffic_stats.total_down_mb }}MB</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 时间信息 -->
                        <div class="col-lg-4 mb-3">
                            <h6 class="fw-bold mb-3">时间信息</h6>
                            <div class="row g-2">
                                <div class="col-12">
                                    <small class="text-muted">购买时间</small>
                                    <div class="fw-semibold">{{ subscription.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                                </div>
                                <div class="col-12">
                                    <small class="text-muted">到期时间</small>
                                    <div class="fw-semibold {% if subscription.is_expired %}text-danger{% endif %}">
                                        {% if subscription.expires_at %}
                                            {{ subscription.expires_at.strftime('%Y-%m-%d %H:%M') }}
                                        {% else %}
                                            永久有效
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="border-top pt-3 mt-3">
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ url_for('user.subscription_detail', order_id=subscription.order_id) }}"
                               class="btn btn-primary btn-sm">
                                <i class="bi bi-eye me-1"></i> 查看详情
                            </a>
                            {% if subscription.is_active and not subscription.is_expired %}
                            <!-- 续费按钮 - 醒目样式 -->
                            <a href="{{ url_for('renewal.renewal_page') }}"
                               class="btn btn-warning btn-sm fw-bold text-dark">
                                <i class="bi bi-arrow-clockwise me-1"></i>
                                <span class="d-none d-sm-inline">订阅</span>续费
                            </a>
                            <!-- 移除单个订阅流量刷新按钮 - 现在流量数据直接从数据库读取 -->
                            {% elif subscription.is_expired %}
                            <!-- 过期订阅的续费按钮 - 更醒目 -->
                            <a href="{{ url_for('renewal.renewal_page') }}"
                               class="btn btn-danger btn-sm fw-bold">
                                <i class="bi bi-exclamation-triangle me-1"></i>
                                立即续费
                            </a>
                            {% endif %}
                            <a href="{{ url_for('shop.order_status', order_id=subscription.order_id) }}"
                               class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-receipt me-1"></i> 订单详情
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <!-- 空状态 -->
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-collection text-muted" style="font-size: 4rem;"></i>
                <h4 class="text-muted mt-3">暂无订阅</h4>
                <p class="text-muted">您还没有购买任何套餐，去商店看看吧！</p>
                <a href="{{ url_for('shop.shop_index') }}" class="btn btn-primary">
                    <i class="bi bi-shop me-1"></i> 去购买套餐
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
    // 复制订阅链接
    function copySubscriptionUrl() {
        const urlInput = document.getElementById('subscriptionUrl');
        urlInput.select();
        urlInput.setSelectionRange(0, 99999);
        
        try {
            document.execCommand('copy');
            showNotification('订阅链接已复制到剪贴板', 'success');
        } catch (err) {
            showNotification('复制失败，请手动复制', 'error');
        }
    }
    
    // 移除流量刷新功能 - 现在流量数据直接从数据库读取，无需手动刷新
    
    // 显示通知
    function showNotification(message, type) {
        // 这里可以使用Bootstrap的Toast或其他通知组件
        alert(message);
    }
</script>
{% endblock %}
