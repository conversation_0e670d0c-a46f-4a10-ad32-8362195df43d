{% extends "base.html" %}

{% block title %}登录 - 节点商城{% endblock %}

{% block content %}
<div class="container-fluid vh-100 d-flex align-items-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <div class="card border-0 shadow-lg">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                <i class="bi bi-person" style="font-size: 2rem;"></i>
                            </div>
                            <h3 class="fw-bold text-dark">欢迎回来</h3>
                            <p class="text-muted">登录您的账户继续使用服务</p>
                        </div>

                        <form method="POST" action="{{ url_for('auth.login') }}" id="loginForm">
                            <div class="mb-4">
                                <label for="username" class="form-label fw-semibold">用户名或邮箱</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-person text-muted"></i>
                                    </span>
                                    <input type="text" class="form-control border-start-0 ps-0" id="username" name="username" placeholder="请输入用户名或邮箱" required>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="password" class="form-label fw-semibold">密码</label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="bi bi-lock text-muted"></i>
                                    </span>
                                    <input type="password" class="form-control border-start-0 ps-0" id="password" name="password" placeholder="请输入密码" required>
                                    <button class="btn btn-outline-secondary border-start-0" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="rememberMe">
                                    <label class="form-check-label text-muted" for="rememberMe">
                                        记住我
                                    </label>
                                </div>
                            </div>

                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="bi bi-box-arrow-in-right me-2"></i> 立即登录
                                </button>
                            </div>
                        </form>

                        <div class="text-center">
                            <p class="text-muted mb-0">
                                还没有账户？
                                <a href="{{ url_for('auth.register') }}" class="text-primary text-decoration-none fw-semibold">立即注册</a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- 特性展示 -->
                <div class="row mt-4 text-center text-white">
                    <div class="col-4">
                        <i class="bi bi-shield-check mb-2" style="font-size: 1.5rem;"></i>
                        <p class="small mb-0">安全可靠</p>
                    </div>
                    <div class="col-4">
                        <i class="bi bi-lightning mb-2" style="font-size: 1.5rem;"></i>
                        <p class="small mb-0">高速稳定</p>
                    </div>
                    <div class="col-4">
                        <i class="bi bi-headset mb-2" style="font-size: 1.5rem;"></i>
                        <p class="small mb-0">24/7支持</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 密码显示/隐藏切换
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordInput = document.getElementById('password');
        const icon = this.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'bi bi-eye';
        }
    });

    // 表单提交动画
    document.getElementById('loginForm').addEventListener('submit', function() {
        const submitBtn = this.querySelector('button[type="submit"]');
        submitBtn.innerHTML = '<span class="loading me-2"></span> 登录中...';
        submitBtn.disabled = true;
    });
</script>
{% endblock %}
