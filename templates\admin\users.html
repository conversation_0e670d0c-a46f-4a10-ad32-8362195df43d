{% extends "base.html" %}

{% block title %}用户管理 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-people"></i> 用户管理
                </h1>
                <a href="{{ url_for('admin.create_user') }}" class="btn btn-primary">
                    <i class="bi bi-person-plus"></i> 添加用户
                </a>
            </div>
        </div>
    </div>
    
    <!-- 用户列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">用户列表</h5>
                </div>
                <div class="card-body">
                    {% if users.items %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>真实姓名</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>注册时间</th>
                                    <th>最后登录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users.items %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.full_name or '-' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if user.role.value == 'admin' else 'primary' }}">
                                            {{ '管理员' if user.role.value == 'admin' else '普通用户' }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                            {{ '活跃' if user.is_active else '停用' }}
                                        </span>
                                    </td>
                                    <td>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '-' }}</td>
                                    <td>{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '从未登录' }}</td>
                                    <td>
                                        {% if user.role.value == 'user' %}
                                            <button type="button"
                                                    class="btn btn-danger btn-sm"
                                                    onclick="confirmDeleteUser({{ user.id }}, '{{ user.username }}')"
                                                    title="删除用户">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        {% else %}
                                            <span class="text-muted small">管理员</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if users.pages > 1 %}
                    <nav aria-label="用户列表分页">
                        <ul class="pagination justify-content-center">
                            {% if users.has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num) }}">上一页</a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in users.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != users.page %}
                                        <li class="page-item">
                                            <a class="page-link" href="{{ url_for('admin.users', page=page_num) }}">{{ page_num }}</a>
                                        </li>
                                    {% else %}
                                        <li class="page-item active">
                                            <span class="page-link">{{ page_num }}</span>
                                        </li>
                                    {% endif %}
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">…</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if users.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.users', page=users.next_num) }}">下一页</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">暂无用户</h5>
                        <p class="text-muted">系统中还没有注册用户</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回仪表板
            </a>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger"></i> 确认删除用户
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>警告：</strong>此操作不可撤销！
                </div>
                <p>您确定要删除用户 <strong id="deleteUserName"></strong> 吗？</p>
                <p class="text-muted small">
                    删除用户将同时删除：
                </p>
                <ul class="text-muted small">
                    <li>用户的所有订单和订阅</li>
                    <li>相关的流量统计记录</li>
                    <li>X-UI面板中的客户端配置</li>
                    <li>续费任务记录</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form id="deleteUserForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> 确认删除
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDeleteUser(userId, username) {
    // 设置模态框内容
    document.getElementById('deleteUserName').textContent = username;
    document.getElementById('deleteUserForm').action = `/admin/users/${userId}/delete`;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('deleteUserModal'));
    modal.show();
}

// 处理删除表单提交
document.getElementById('deleteUserForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');

    // 禁用按钮并显示加载状态
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> 删除中...';

    // 表单会自动提交，不需要阻止默认行为
});
</script>

{% endblock %}
