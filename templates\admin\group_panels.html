{% extends "base.html" %}

{% block title %}分组面板管理 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <span class="badge me-2" style="background-color: {{ group.color }}; color: white;">
                        {{ group.priority }}
                    </span>
                    {{ group.name }} - 面板管理
                </h1>
                <div>
                    <a href="{{ url_for('admin.edit_group', group_id=group.id) }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-pencil"></i> 编辑分组
                    </a>
                    <a href="{{ url_for('admin.groups') }}" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left"></i> 返回分组列表
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 分组信息 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            {% if group.description %}
                            <p class="mb-2"><strong>描述：</strong> {{ group.description }}</p>
                            {% endif %}
                            <p class="mb-0">
                                <span class="badge bg-{{ 'success' if group.is_active else 'secondary' }} me-2">
                                    {{ '活跃' if group.is_active else '停用' }}
                                </span>
                                <strong>优先级：</strong> {{ group.priority }}
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="row text-center mb-3">
                                <div class="col-6">
                                    <div class="fw-bold fs-4">{{ group.panels|length }}</div>
                                    <small class="text-muted">总面板数</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold fs-4">{{ group.active_panels|length }}</div>
                                    <small class="text-muted">活跃面板</small>
                                </div>
                            </div>
                            <!-- 同步状态和按钮 -->
                            <div class="row">
                                <div class="col-12">
                                    <div id="syncStatusCard" class="card border-info mb-2" style="display: none;">
                                        <div class="card-body p-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <small class="text-muted">订阅同步状态</small>
                                                    <div id="syncStatusText" class="fw-bold small"></div>
                                                </div>
                                                <div id="syncStatusBadge"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-outline-success btn-sm w-100"
                                            id="syncSubscriptionsBtn"
                                            onclick="syncGroupSubscriptions()"
                                            title="同步现有订阅到所有面板">
                                        <i class="bi bi-arrow-repeat"></i> 同步现有订阅
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加面板表单 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-plus-circle"></i> 添加面板到分组</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('admin.add_panel_to_group', group_id=group.id) }}" id="addPanelForm">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label class="form-label">选择面板</label>
                                <select class="form-select" name="panel_id" id="panelSelect" required>
                                    <option value="">选择面板...</option>
                                    {% for panel in all_panels %}
                                        {% if panel.id not in group_panel_ids %}
                                        <option value="{{ panel.id }}">{{ panel.name }} ({{ panel.base_url }})</option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">入站协议</label>
                                <select class="form-select" name="inbound_id" id="inboundSelect" disabled>
                                    <option value="">先选择面板...</option>
                                </select>
                                <div class="form-text">选择面板后自动加载</div>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">权重</label>
                                <input type="number" class="form-control" name="weight" value="1" min="1" max="100"
                                       placeholder="权重" title="权重">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">角色</label>
                                <select class="form-select" name="role">
                                    <option value="primary">主要节点</option>
                                    <option value="backup">备用节点</option>
                                    <option value="fallback">故障转移</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary w-100 d-block" id="addButton">
                                    <i class="bi bi-plus"></i> 添加
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- 分组中的面板列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-server"></i> 分组中的面板</h5>
                </div>
                <div class="card-body">
                    {% if group.memberships %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>面板名称</th>
                                    <th>地址</th>
                                    <th>区域</th>
                                    <th>入站协议ID</th>
                                    <th>权重</th>
                                    <th>角色</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for membership in group.memberships %}
                                <tr>
                                    <td>
                                        <strong>{{ membership.panel.name }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ membership.panel.base_url }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ membership.panel.region }}</span>
                                    </td>
                                    <td>
                                        {% if membership.inbound_id %}
                                            <span class="badge bg-info">{{ membership.inbound_id }}</span>
                                        {% else %}
                                            <span class="text-muted">未指定</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <form method="POST" action="{{ url_for('admin.update_panel_in_group', group_id=group.id, panel_id=membership.panel.id) }}"
                                              style="display: inline;">
                                            <input type="number" class="form-control form-control-sm d-inline-block"
                                                   name="weight" value="{{ membership.weight }}" min="1" max="100"
                                                   style="width: 70px;" onchange="this.form.submit()">
                                            <input type="hidden" name="role" value="{{ membership.role.value }}">
                                            <input type="hidden" name="inbound_id" value="{{ membership.inbound_id or '' }}">
                                        </form>
                                    </td>
                                    <td>
                                        <form method="POST" action="{{ url_for('admin.update_panel_in_group', group_id=group.id, panel_id=membership.panel.id) }}"
                                              style="display: inline;">
                                            <select class="form-select form-select-sm" name="role" onchange="this.form.submit()">
                                                <option value="primary" {{ 'selected' if membership.role.value == 'primary' else '' }}>主要</option>
                                                <option value="backup" {{ 'selected' if membership.role.value == 'backup' else '' }}>备用</option>
                                                <option value="fallback" {{ 'selected' if membership.role.value == 'fallback' else '' }}>故障转移</option>
                                            </select>
                                            <input type="hidden" name="weight" value="{{ membership.weight }}">
                                            <input type="hidden" name="inbound_id" value="{{ membership.inbound_id or '' }}">
                                        </form>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if membership.panel.status.value == 'active' else 'warning' if membership.panel.status.value == 'maintenance' else 'secondary' }}">
                                            {% if membership.panel.status.value == 'active' %}
                                                运行中
                                            {% elif membership.panel.status.value == 'maintenance' %}
                                                维护中
                                            {% else %}
                                                停用
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <form method="POST" action="{{ url_for('admin.remove_panel_from_group', group_id=group.id, panel_id=membership.panel.id) }}" 
                                              style="display: inline;" onsubmit="return confirm('确定要从分组中移除面板 {{ membership.panel.name }} 吗？')">
                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                <i class="bi bi-trash"></i> 移除
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-server text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">分组中暂无面板</h5>
                        <p class="text-muted">使用上方的表单添加面板到此分组</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-control-sm, .form-select-sm {
    font-size: 0.875rem;
}

.table td {
    vertical-align: middle;
}

.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const panelSelect = document.getElementById('panelSelect');
    const inboundSelect = document.getElementById('inboundSelect');
    const addButton = document.getElementById('addButton');

    panelSelect.addEventListener('change', function() {
        const panelId = this.value;

        if (!panelId) {
            inboundSelect.disabled = true;
            inboundSelect.innerHTML = '<option value="">先选择面板...</option>';
            addButton.disabled = true;
            return;
        }

        // 显示加载状态
        inboundSelect.disabled = true;
        inboundSelect.innerHTML = '<option value="">加载中...</option>';
        addButton.disabled = true;

        // 获取面板的入站协议列表
        fetch(`/admin/api/panels/${panelId}/inbounds`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    inboundSelect.innerHTML = '<option value="">选择入站协议...</option>';

                    if (data.data && data.data.length > 0) {
                        data.data.forEach(inbound => {
                            const option = document.createElement('option');
                            option.value = inbound.id;
                            option.textContent = `${inbound.remark || 'ID: ' + inbound.id} (${inbound.protocol}:${inbound.port})`;
                            if (!inbound.enable) {
                                option.textContent += ' [已禁用]';
                                option.disabled = true;
                            }
                            inboundSelect.appendChild(option);
                        });

                        inboundSelect.disabled = false;

                        // 如果只有一个启用的入站协议，自动选中
                        const enabledOptions = data.data.filter(inbound => inbound.enable);
                        if (enabledOptions.length === 1) {
                            inboundSelect.value = enabledOptions[0].id;
                            addButton.disabled = false;
                        }
                    } else {
                        inboundSelect.innerHTML = '<option value="">该面板无可用入站协议</option>';
                    }
                } else {
                    inboundSelect.innerHTML = '<option value="">加载失败</option>';
                    console.error('获取入站协议失败:', data.message);
                    alert('获取入站协议失败: ' + data.message);
                }
            })
            .catch(error => {
                inboundSelect.innerHTML = '<option value="">网络错误</option>';
                console.error('网络错误:', error);
                alert('网络错误，请检查连接');
            });
    });

    inboundSelect.addEventListener('change', function() {
        addButton.disabled = !this.value;
    });

    // 加载同步状态
    loadSyncStatus();
});

// 同步分组订阅功能
function syncGroupSubscriptions() {
    const btn = document.getElementById('syncSubscriptionsBtn');
    const originalText = btn.innerHTML;

    // 确认对话框
    if (!confirm('确定要同步此分组的现有订阅到所有面板吗？\n\n这将为每个现有订阅在分组的所有面板中创建节点。')) {
        return;
    }

    // 显示加载状态
    btn.disabled = true;
    btn.innerHTML = '<span class="loading-spinner me-1"></span> 同步中...';

    // 发送同步请求
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ url_for("admin.sync_group_subscriptions", group_id=group.id) }}';

    // 添加CSRF令牌（如果需要）
    const csrfToken = document.querySelector('meta[name=csrf-token]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }

    document.body.appendChild(form);
    form.submit();
}

// 加载同步状态
function loadSyncStatus() {
    fetch('{{ url_for("admin.get_group_sync_status", group_id=group.id) }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSyncStatusDisplay(data.data);
            } else {
                console.error('获取同步状态失败:', data.error);
            }
        })
        .catch(error => {
            console.error('网络错误:', error);
        });
}

// 更新同步状态显示
function updateSyncStatusDisplay(statusData) {
    const statusCard = document.getElementById('syncStatusCard');
    const statusText = document.getElementById('syncStatusText');
    const statusBadge = document.getElementById('syncStatusBadge');
    const syncBtn = document.getElementById('syncSubscriptionsBtn');

    // 根据状态显示不同的信息
    let statusInfo = '';
    let badgeClass = '';
    let badgeText = '';
    let showCard = true;

    switch (statusData.sync_status) {
        case 'no_panels':
            statusInfo = '分组中没有面板';
            badgeClass = 'bg-secondary';
            badgeText = '无面板';
            syncBtn.disabled = true;
            break;
        case 'no_subscriptions':
            statusInfo = '没有活跃订阅';
            badgeClass = 'bg-info';
            badgeText = '无订阅';
            syncBtn.disabled = true;
            break;
        case 'synced':
            statusInfo = `${statusData.actual_nodes}/${statusData.expected_nodes} 节点已同步`;
            badgeClass = 'bg-success';
            badgeText = '已同步';
            syncBtn.disabled = false;
            break;
        case 'partial':
            statusInfo = `${statusData.actual_nodes}/${statusData.expected_nodes} 节点 (${statusData.sync_percentage}%)`;
            badgeClass = 'bg-warning';
            badgeText = '部分同步';
            syncBtn.disabled = false;
            break;
        case 'not_synced':
            statusInfo = `${statusData.active_subscriptions} 个订阅未同步`;
            badgeClass = 'bg-danger';
            badgeText = '未同步';
            syncBtn.disabled = false;
            break;
        default:
            showCard = false;
            syncBtn.disabled = false;
    }

    if (showCard) {
        statusText.textContent = statusInfo;
        statusBadge.className = `badge ${badgeClass}`;
        statusBadge.textContent = badgeText;
        statusCard.style.display = 'block';
    } else {
        statusCard.style.display = 'none';
    }
}
</script>

{% endblock %}
