{% extends "base.html" %}

{% block title %}编辑分组 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-pencil"></i> 编辑分组：{{ group.name }}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="name" class="form-label">分组名称 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="name" name="name" required 
                                   value="{{ group.name }}" placeholder="例如：亚洲节点、高性能组、备用组">
                            <div class="form-text">分组名称必须唯一</div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">分组描述</label>
                            <textarea class="form-control" id="description" name="description" rows="3" 
                                      placeholder="描述这个分组的用途和特点">{{ group.description or '' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="color" class="form-label">分组颜色</label>
                                    <input type="color" class="form-control form-control-color" id="color" name="color" 
                                           value="{{ group.color }}" title="选择分组颜色">
                                    <div class="form-text">用于在界面中区分不同分组</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="priority" class="form-label">优先级</label>
                                    <input type="number" class="form-control" id="priority" name="priority" 
                                           value="{{ group.priority }}" min="1" max="100">
                                    <div class="form-text">数字越小优先级越高</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {{ 'checked' if group.is_active else '' }}>
                                <label class="form-check-label" for="is_active">
                                    启用分组
                                </label>
                                <div class="form-text">只有启用的分组才会参与节点分配</div>
                            </div>
                        </div>

                        <!-- 分组统计信息 -->
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> 分组统计</h6>
                            <div class="row">
                                <div class="col-6">
                                    <strong>面板数量：</strong> {{ group.panels|length }}
                                </div>
                                <div class="col-6">
                                    <strong>活跃面板：</strong> {{ group.active_panels|length }}
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-6">
                                    <strong>关联产品：</strong> {{ group.products|length }}
                                </div>
                                <div class="col-6">
                                    <strong>创建时间：</strong> {{ group.created_at.strftime('%Y-%m-%d') if group.created_at else '未知' }}
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('admin.groups') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <a href="{{ url_for('admin.group_panels', group_id=group.id) }}" class="btn btn-outline-primary me-md-2">
                                <i class="bi bi-server"></i> 管理面板
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 表单验证
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.getElementById('name').value.trim();
    if (!name) {
        e.preventDefault();
        alert('请输入分组名称');
        document.getElementById('name').focus();
        return false;
    }
    
    const priority = parseInt(document.getElementById('priority').value);
    if (priority < 1 || priority > 100) {
        e.preventDefault();
        alert('优先级必须在1-100之间');
        document.getElementById('priority').focus();
        return false;
    }
});
</script>

{% endblock %}
