# 订阅到期处理功能指南

## 概述

本功能实现了用户订阅到期后的自动清理机制，当订阅彻底到期后，系统会：
1. 从X-UI所有面板删除对应的客户端
2. 从数据库层面完全删除用户订阅和关联数据

## 核心功能

### 1. 自动检测到期订阅
- 基于 `subscriptions.expires_at` 字段判断订阅是否到期
- 只处理 `is_active=True` 的活跃订阅
- 支持时区处理，确保时间比较准确

### 2. 彻底删除机制
- **X-UI客户端删除**：从所有配置的X-UI面板删除客户端
- **数据库记录删除**：级联删除订阅、订单、节点配置等关联数据
- **错误容错**：单个操作失败不影响其他订阅处理

### 3. 定时任务调度
- 每小时自动执行一次到期订阅检查
- 可手动触发执行
- 完整的日志记录和状态监控

## 文件结构

```
services/
├── expiration_service.py          # 核心到期处理服务
├── subscription_service.py        # 订阅服务（已修改，支持公共删除方法）
└── scheduler_service.py           # 调度服务（已修改，添加到期任务）

routes/
└── admin.py                       # 管理接口（已修改，添加手动触发API）

test_expiration_service.py         # 基础测试脚本
simple_expiration_demo.py          # 简化演示脚本
demo_expiration_feature.py         # 完整演示脚本（有缩进问题）
```

## API 接口

### 1. 手动清理到期订阅
```
POST /admin/api/subscriptions/cleanup-expired
```
- 权限：管理员
- 功能：手动触发到期订阅清理
- 返回：处理统计信息

### 2. 获取即将到期订阅
```
GET /admin/api/subscriptions/expiring-soon?days=7
```
- 权限：管理员
- 参数：`days` - 多少天内到期（默认7天）
- 返回：即将到期的订阅列表

### 3. 强制删除指定订阅
```
DELETE /admin/api/subscriptions/<id>/force-delete
```
- 权限：管理员
- 功能：强制删除指定订阅（包括X-UI客户端）
- 返回：删除结果详情

## 核心类和方法

### ExpirationService 类

#### 主要方法：

1. **process_expired_subscriptions()** - 处理所有到期订阅
   ```python
   stats = expiration_service.process_expired_subscriptions()
   ```

2. **get_expiring_soon_subscriptions(days=7)** - 获取即将到期订阅
   ```python
   expiring = expiration_service.get_expiring_soon_subscriptions(days=7)
   ```

3. **force_delete_subscription(subscription_id)** - 强制删除指定订阅
   ```python
   result = expiration_service.force_delete_subscription(123)
   ```

### SubscriptionService 类（已修改）

#### 新增公共方法：

1. **delete_subscription_clients_from_xui(subscription)** - 从X-UI删除客户端
2. **delete_subscription_and_order(subscription)** - 删除数据库记录

### SchedulerService 类（已修改）

#### 新增方法：

1. **trigger_expired_subscriptions_cleanup()** - 手动触发清理
   ```python
   result = scheduler_service.trigger_expired_subscriptions_cleanup()
   ```

## 配置和部署

### 1. 自动启动
功能已集成到主应用中，应用启动时会自动：
- 初始化到期处理服务
- 注册定时任务（每小时执行）
- 启动调度器

### 2. 日志配置
系统会记录详细的处理日志：
- 到期订阅检测日志
- X-UI客户端删除日志
- 数据库操作日志
- 错误和异常日志

### 3. 监控
可通过以下方式监控功能状态：
- 查看应用日志
- 调用调度器状态API
- 管理界面手动触发测试

## 测试和验证

### 1. 基础测试
```bash
python test_expiration_service.py
```

### 2. 功能演示
```bash
python simple_expiration_demo.py
```

### 3. 应用启动验证
```bash
python -c "from app import create_app; app = create_app(); print('✅ 应用启动成功')"
```

## 处理流程

```mermaid
flowchart TD
    A[定时任务触发] --> B[查找到期订阅]
    B --> C{有到期订阅?}
    C -->|否| D[记录日志结束]
    C -->|是| E[逐个处理订阅]
    E --> F[从X-UI面板删除客户端]
    F --> G[从数据库删除记录]
    G --> H[记录处理结果]
    H --> I{还有订阅?}
    I -->|是| E
    I -->|否| J[提交数据库事务]
    J --> K[生成处理报告]
    K --> D
```

## 错误处理

### 1. X-UI删除失败
- 记录警告日志
- 继续删除数据库记录
- 在报告中标记为部分成功

### 2. 数据库删除失败
- 记录错误日志
- 回滚事务
- 在报告中标记为失败

### 3. 网络连接问题
- 记录连接失败
- 重试机制（内置在XUIClient中）
- 超时处理

## 安全考虑

### 1. 权限控制
- 所有API都需要管理员权限
- 关键操作记录操作者信息

### 2. 数据完整性
- 使用数据库事务确保一致性
- 级联删除防止孤儿记录

### 3. 操作审计
- 详细的操作日志
- 处理结果统计
- 错误信息记录

## 常见问题

### Q: 如何手动触发到期订阅清理？
A: 调用 `POST /admin/api/subscriptions/cleanup-expired` 或使用 `scheduler_service.trigger_expired_subscriptions_cleanup()`

### Q: 如何查看即将到期的订阅？
A: 调用 `GET /admin/api/subscriptions/expiring-soon?days=7`

### Q: 如何修改定时任务执行频率？
A: 修改 `scheduler_service.py` 中的 `IntervalTrigger(hours=1)` 参数

### Q: 删除失败的订阅如何处理？
A: 查看日志了解失败原因，可使用强制删除API进行手动清理

### Q: 如何停用自动清理功能？
A: 在调度器中移除对应的任务或修改应用配置

## 扩展功能

可以基于现有框架扩展的功能：
1. 邮件通知（到期提醒、清理通知）
2. 更细粒度的清理策略
3. 订阅续费功能
4. 数据导出和备份
5. 批量操作界面

## 总结

订阅到期处理功能已完全集成到系统中，提供了：
- ✅ 自动检测到期订阅
- ✅ 从X-UI面板删除客户端
- ✅ 数据库彻底删除
- ✅ 定时任务自动执行
- ✅ 管理员手动控制
- ✅ 完整的日志记录
- ✅ 错误处理和容错
- ✅ API接口支持

系统现在能够自动处理订阅生命周期，确保到期订阅被及时清理，维护系统的数据整洁性和安全性。 