"""
数据库迁移脚本：为分组成员关系添加入站协议ID字段
"""
import sqlite3
import os
import logging

logger = logging.getLogger(__name__)

def migrate_database(db_path=None):
    """执行数据库迁移"""
    try:
        # 如果没有指定路径，尝试查找数据库文件
        if db_path is None:
            possible_paths = [
                'node_sales.db',
                'instance/node_sales.db',
                os.path.join(os.path.dirname(__file__), '..', 'node_sales.db'),
                os.path.join(os.path.dirname(__file__), '..', 'instance', 'node_sales.db')
            ]

            for path in possible_paths:
                if os.path.exists(path):
                    db_path = path
                    break

            if db_path is None:
                logger.error("找不到数据库文件")
                return False

        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            logger.warning(f"数据库文件不存在: {db_path}")
            return False

        logger.info(f"使用数据库文件: {db_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否已存在
        cursor.execute("PRAGMA table_info(xui_panel_group_memberships)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'inbound_id' in columns:
            logger.info("inbound_id字段已存在，跳过迁移")
            conn.close()
            return True
        
        # 添加inbound_id字段
        logger.info("开始添加inbound_id字段...")
        cursor.execute("""
            ALTER TABLE xui_panel_group_memberships 
            ADD COLUMN inbound_id INTEGER NULL
        """)
        
        conn.commit()
        logger.info("✓ 成功添加inbound_id字段")
        
        # 验证字段是否添加成功
        cursor.execute("PRAGMA table_info(xui_panel_group_memberships)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'inbound_id' in columns:
            logger.info("✓ 字段添加验证成功")
        else:
            logger.error("✗ 字段添加验证失败")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def rollback_migration(db_path='node_sales.db'):
    """回滚迁移（SQLite不支持DROP COLUMN，需要重建表）"""
    try:
        if not os.path.exists(db_path):
            logger.warning(f"数据库文件不存在: {db_path}")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查字段是否存在
        cursor.execute("PRAGMA table_info(xui_panel_group_memberships)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'inbound_id' not in columns:
            logger.info("inbound_id字段不存在，无需回滚")
            conn.close()
            return True
        
        logger.info("开始回滚迁移...")
        
        # 创建临时表（不包含inbound_id字段）
        cursor.execute("""
            CREATE TABLE xui_panel_group_memberships_temp (
                id INTEGER PRIMARY KEY,
                panel_id INTEGER NOT NULL,
                group_id INTEGER NOT NULL,
                weight INTEGER NOT NULL DEFAULT 1,
                role TEXT NOT NULL DEFAULT 'primary',
                created_at DATETIME NOT NULL,
                FOREIGN KEY (panel_id) REFERENCES xui_panels (id),
                FOREIGN KEY (group_id) REFERENCES xui_panel_groups (id),
                UNIQUE (panel_id, group_id)
            )
        """)
        
        # 复制数据（排除inbound_id字段）
        cursor.execute("""
            INSERT INTO xui_panel_group_memberships_temp 
            (id, panel_id, group_id, weight, role, created_at)
            SELECT id, panel_id, group_id, weight, role, created_at
            FROM xui_panel_group_memberships
        """)
        
        # 删除原表
        cursor.execute("DROP TABLE xui_panel_group_memberships")
        
        # 重命名临时表
        cursor.execute("""
            ALTER TABLE xui_panel_group_memberships_temp 
            RENAME TO xui_panel_group_memberships
        """)
        
        conn.commit()
        logger.info("✓ 迁移回滚成功")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"迁移回滚失败: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 执行迁移
    success = migrate_database()
    if success:
        print("✓ 数据库迁移成功")
    else:
        print("✗ 数据库迁移失败")
