"""
更新用户注册相关数据库结构
- 移除用户表中的 full_name 和 phone 字段
- 添加图形验证码表 captcha_codes
"""
import sqlite3
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def upgrade_database(db_path='instance/node_sales.db'):
    """升级数据库结构"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 创建图形验证码表
        create_captcha_table_sql = """
        CREATE TABLE IF NOT EXISTS captcha_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code VARCHAR(10) NOT NULL,
            session_id VARCHAR(255) NOT NULL,
            is_used BOOLEAN NOT NULL DEFAULT 0,
            is_expired BOOLEAN NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            used_at DATETIME,
            ip_address VARCHAR(45),
            user_agent TEXT,
            attempts INTEGER NOT NULL DEFAULT 0,
            max_attempts INTEGER NOT NULL DEFAULT 5
        );
        """
        
        cursor.execute(create_captcha_table_sql)
        logger.info("图形验证码表创建成功")
        
        # 2. 为图形验证码表创建索引
        create_captcha_index_sql = """
        CREATE INDEX IF NOT EXISTS idx_captcha_session_id ON captcha_codes(session_id);
        """
        
        cursor.execute(create_captcha_index_sql)
        logger.info("图形验证码表索引创建成功")
        
        # 3. 检查用户表是否存在 full_name 和 phone 字段
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        has_full_name = 'full_name' in columns
        has_phone = 'phone' in columns
        
        if has_full_name or has_phone:
            logger.info("检测到用户表中存在 full_name 或 phone 字段，开始移除...")
            
            # 4. 创建新的用户表结构（不包含 full_name 和 phone）
            create_new_users_table_sql = """
            CREATE TABLE users_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME
            );
            """
            
            cursor.execute(create_new_users_table_sql)
            logger.info("新用户表结构创建成功")
            
            # 5. 复制数据到新表（排除 full_name 和 phone）
            copy_data_sql = """
            INSERT INTO users_new (id, username, email, password_hash, role, is_active, created_at, last_login)
            SELECT id, username, email, password_hash, role, is_active, created_at, last_login
            FROM users;
            """
            
            cursor.execute(copy_data_sql)
            logger.info("用户数据复制成功")
            
            # 6. 删除旧表
            cursor.execute("DROP TABLE users")
            logger.info("旧用户表删除成功")
            
            # 7. 重命名新表
            cursor.execute("ALTER TABLE users_new RENAME TO users")
            logger.info("新用户表重命名成功")
            
            # 8. 重新创建用户表索引
            create_users_indexes_sql = [
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users(username);",
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users(email);"
            ]
            
            for sql in create_users_indexes_sql:
                cursor.execute(sql)
            
            logger.info("用户表索引重新创建成功")
        else:
            logger.info("用户表中未发现 full_name 或 phone 字段，跳过移除操作")
        
        # 提交事务
        conn.commit()
        logger.info("数据库升级完成")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库升级失败: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()

def downgrade_database(db_path='instance/node_sales.db'):
    """降级数据库结构（回滚操作）"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 删除图形验证码表
        cursor.execute("DROP TABLE IF EXISTS captcha_codes")
        logger.info("图形验证码表删除成功")
        
        # 2. 检查用户表是否缺少 full_name 和 phone 字段
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        has_full_name = 'full_name' in columns
        has_phone = 'phone' in columns
        
        if not has_full_name or not has_phone:
            logger.info("检测到用户表中缺少 full_name 或 phone 字段，开始添加...")
            
            # 3. 创建包含 full_name 和 phone 的用户表结构
            create_old_users_table_sql = """
            CREATE TABLE users_old (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) NOT NULL DEFAULT 'user',
                full_name VARCHAR(100),
                phone VARCHAR(20),
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME
            );
            """
            
            cursor.execute(create_old_users_table_sql)
            logger.info("旧用户表结构创建成功")
            
            # 4. 复制数据到旧表结构
            copy_data_sql = """
            INSERT INTO users_old (id, username, email, password_hash, role, is_active, created_at, last_login)
            SELECT id, username, email, password_hash, role, is_active, created_at, last_login
            FROM users;
            """
            
            cursor.execute(copy_data_sql)
            logger.info("用户数据复制成功")
            
            # 5. 删除新表
            cursor.execute("DROP TABLE users")
            logger.info("新用户表删除成功")
            
            # 6. 重命名旧表
            cursor.execute("ALTER TABLE users_old RENAME TO users")
            logger.info("旧用户表重命名成功")
            
            # 7. 重新创建用户表索引
            create_users_indexes_sql = [
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username ON users(username);",
                "CREATE UNIQUE INDEX IF NOT EXISTS idx_users_email ON users(email);"
            ]
            
            for sql in create_users_indexes_sql:
                cursor.execute(sql)
            
            logger.info("用户表索引重新创建成功")
        else:
            logger.info("用户表中已存在 full_name 和 phone 字段，跳过添加操作")
        
        # 提交事务
        conn.commit()
        logger.info("数据库降级完成")
        
        return True
        
    except Exception as e:
        logger.error(f"数据库降级失败: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if conn:
            conn.close()

if __name__ == '__main__':
    # 直接运行时执行升级
    import sys
    import os

    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

    # 检查数据库文件是否存在
    db_path = 'instance/node_sales.db'
    if not os.path.exists('instance'):
        os.makedirs('instance')
        print("创建 instance 目录")

    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在，将创建新的数据库")

    if len(sys.argv) > 1 and sys.argv[1] == 'downgrade':
        print("执行数据库降级...")
        success = downgrade_database(db_path)
    else:
        print("执行数据库升级...")
        success = upgrade_database(db_path)

    if success:
        print("操作完成")
    else:
        print("操作失败")
        sys.exit(1)
