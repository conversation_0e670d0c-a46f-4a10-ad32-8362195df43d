"""
流量基准管理路由
提供流量基准数据的查看、恢复和清理功能
"""
from flask import Blueprint, request, render_template, jsonify, flash, redirect, url_for
from models import db
from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
from models.subscription import Subscription
from services.traffic_baseline_deletion_service import traffic_baseline_deletion_service
from routes.admin import admin_required
import logging

logger = logging.getLogger(__name__)

traffic_baseline_bp = Blueprint('traffic_baseline', __name__, url_prefix='/admin/traffic-baseline')


@traffic_baseline_bp.route('/')
@admin_required
def index():
    """流量基准管理主页"""
    try:
        # 获取活跃基准统计
        active_baselines = SubscriptionTrafficBaseline.query.filter_by(is_deleted=False).all()
        active_count = len(active_baselines)
        active_total_mb = sum(b.baseline_total_bytes for b in active_baselines) / (1024**2)
        
        # 获取已删除基准统计
        deleted_baselines = SubscriptionTrafficBaseline.query.filter_by(is_deleted=True).all()
        deleted_count = len(deleted_baselines)
        deleted_total_mb = sum(b.baseline_total_bytes for b in deleted_baselines) / (1024**2)
        
        # 获取最近的基准记录
        recent_baselines = SubscriptionTrafficBaseline.query.filter_by(
            is_deleted=False
        ).order_by(SubscriptionTrafficBaseline.last_updated.desc()).limit(10).all()
        
        stats = {
            'active_count': active_count,
            'active_total_mb': round(active_total_mb, 2),
            'deleted_count': deleted_count,
            'deleted_total_mb': round(deleted_total_mb, 2),
            'total_count': active_count + deleted_count,
            'total_mb': round(active_total_mb + deleted_total_mb, 2)
        }
        
        return render_template('admin/traffic_baseline/index.html', 
                             stats=stats, 
                             recent_baselines=recent_baselines)
        
    except Exception as e:
        logger.error(f"获取流量基准统计失败: {e}")
        flash('获取流量基准数据失败', 'error')
        return redirect(url_for('admin.dashboard'))


@traffic_baseline_bp.route('/list')
@admin_required
def list_baselines():
    """流量基准列表页面"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 20
        show_deleted = request.args.get('show_deleted', 'false').lower() == 'true'
        
        # 构建查询
        query = SubscriptionTrafficBaseline.query
        if not show_deleted:
            query = query.filter_by(is_deleted=False)
        
        # 分页查询
        baselines = query.order_by(
            SubscriptionTrafficBaseline.last_updated.desc()
        ).paginate(page=page, per_page=per_page, error_out=False)
        
        # 获取关联的订阅信息
        baseline_data = []
        for baseline in baselines.items:
            subscription = Subscription.query.get(baseline.subscription_id)
            baseline_info = {
                'baseline': baseline,
                'subscription': subscription,
                'order': subscription.order if subscription else None
            }
            baseline_data.append(baseline_info)
        
        return render_template('admin/traffic_baseline/list.html',
                             baselines=baselines,
                             baseline_data=baseline_data,
                             show_deleted=show_deleted)
        
    except Exception as e:
        logger.error(f"获取流量基准列表失败: {e}")
        flash('获取流量基准列表失败', 'error')
        return redirect(url_for('traffic_baseline.index'))


@traffic_baseline_bp.route('/api/restore/<int:subscription_id>', methods=['POST'])
@admin_required
def restore_baseline(subscription_id):
    """恢复软删除的流量基准"""
    try:
        result = traffic_baseline_deletion_service.restore_baseline(subscription_id)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': result['message'],
                'data': {
                    'subscription_id': subscription_id,
                    'baseline_total_mb': result['baseline_total_mb']
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 400
            
    except Exception as e:
        logger.error(f"恢复流量基准失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@traffic_baseline_bp.route('/api/cleanup-deleted', methods=['POST'])
@admin_required
def cleanup_deleted_baselines():
    """清理过期的软删除基准数据"""
    try:
        days = request.json.get('days', 30) if request.is_json else 30
        
        result = traffic_baseline_deletion_service.cleanup_old_deleted_baselines(days)
        
        if result['success']:
            return jsonify({
                'success': True,
                'message': result['message'],
                'data': {
                    'cleaned_count': result['cleaned_count'],
                    'total_mb_cleaned': result.get('total_mb_cleaned', 0)
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 400
            
    except Exception as e:
        logger.error(f"清理已删除基准失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@traffic_baseline_bp.route('/api/stats')
@admin_required
def get_baseline_stats():
    """获取流量基准统计数据API"""
    try:
        # 按分组统计
        from sqlalchemy import func
        from models.xui_panel import XUIPanelGroup
        
        group_stats = db.session.query(
            Subscription.group_id,
            XUIPanelGroup.name.label('group_name'),
            func.count(SubscriptionTrafficBaseline.id).label('baseline_count'),
            func.sum(SubscriptionTrafficBaseline.baseline_total_bytes).label('total_bytes')
        ).join(
            Subscription, SubscriptionTrafficBaseline.subscription_id == Subscription.id
        ).outerjoin(
            XUIPanelGroup, Subscription.group_id == XUIPanelGroup.id
        ).filter(
            SubscriptionTrafficBaseline.is_deleted == False
        ).group_by(Subscription.group_id, XUIPanelGroup.name).all()
        
        # 格式化统计数据
        stats_data = []
        for stat in group_stats:
            stats_data.append({
                'group_id': stat.group_id,
                'group_name': stat.group_name or '默认分组',
                'baseline_count': stat.baseline_count,
                'total_mb': round((stat.total_bytes or 0) / (1024**2), 2)
            })
        
        # 总体统计
        total_baselines = SubscriptionTrafficBaseline.query.filter_by(is_deleted=False).count()
        total_bytes = db.session.query(
            func.sum(SubscriptionTrafficBaseline.baseline_total_bytes)
        ).filter_by(is_deleted=False).scalar() or 0
        
        return jsonify({
            'success': True,
            'data': {
                'group_stats': stats_data,
                'total_baselines': total_baselines,
                'total_mb': round(total_bytes / (1024**2), 2)
            }
        })
        
    except Exception as e:
        logger.error(f"获取基准统计失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@traffic_baseline_bp.route('/api/search')
@admin_required
def search_baselines():
    """搜索流量基准"""
    try:
        subscription_id = request.args.get('subscription_id', type=int)
        order_id = request.args.get('order_id')
        customer_email = request.args.get('customer_email')
        
        query = SubscriptionTrafficBaseline.query.join(Subscription)
        
        if subscription_id:
            query = query.filter(SubscriptionTrafficBaseline.subscription_id == subscription_id)
        
        if order_id:
            query = query.join(Subscription.order).filter(
                Order.order_id.ilike(f'%{order_id}%')
            )
        
        if customer_email:
            query = query.join(Subscription.order).filter(
                Order.customer_email.ilike(f'%{customer_email}%')
            )
        
        baselines = query.order_by(
            SubscriptionTrafficBaseline.last_updated.desc()
        ).limit(50).all()
        
        results = []
        for baseline in baselines:
            subscription = Subscription.query.get(baseline.subscription_id)
            order = subscription.order if subscription else None
            
            results.append({
                'subscription_id': baseline.subscription_id,
                'baseline_total_mb': round(baseline.baseline_total_bytes / (1024**2), 2),
                'is_deleted': baseline.is_deleted,
                'last_updated': baseline.last_updated.isoformat(),
                'order_id': order.order_id if order else None,
                'customer_email': order.customer_email if order else None
            })
        
        return jsonify({
            'success': True,
            'data': results
        })
        
    except Exception as e:
        logger.error(f"搜索流量基准失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
