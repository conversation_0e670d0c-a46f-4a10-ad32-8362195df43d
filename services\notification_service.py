"""
统一通知服务 - 管理各种类型的通知
"""
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from models import db, User, Subscription, Order
from .email_service import email_service
from .verification_service import verification_service

logger = logging.getLogger(__name__)


class NotificationService:
    """统一通知服务类"""
    
    def __init__(self):
        self.notification_types = {
            'subscription_expiry': '订阅到期通知',
            'subscription_warning': '订阅即将到期警告',
            'order_confirmation': '订单确认通知',
            'order_completed': '订单完成通知',
            'verification_code': '验证码通知',
            'system_maintenance': '系统维护通知'
        }

    def send_subscription_expiry_notification(self, subscription_id: int) -> bool:
        """
        发送订阅到期通知
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 获取订阅信息
            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                logger.error(f"订阅不存在: {subscription_id}")
                return False
            
            # 获取用户信息
            order = Order.query.get(subscription.order_id)
            if not order:
                logger.error(f"订单不存在: {subscription.order_id}")
                return False
            
            user = User.query.get(order.user_id)
            if not user or not user.email:
                logger.error(f"用户不存在或无邮箱: {order.user_id}")
                return False
            
            # 准备订阅信息
            subscription_info = {
                'id': subscription.id,
                'expires_at': subscription.expires_at.strftime('%Y-%m-%d %H:%M:%S') if subscription.expires_at else 'N/A',
                'traffic_limit_gb': order.traffic_limit_gb,
                'order_id': order.id,
                'product_name': f"{order.node_type.value.upper()} 套餐" if order.node_type else "数据套餐"
            }
            
            # 发送邮件
            success = email_service.send_subscription_expiry_email(
                user.email, user.username, subscription_info
            )
            
            if success:
                logger.info(f"订阅到期通知发送成功: 用户 {user.username}, 订阅 {subscription_id}")
            else:
                logger.error(f"订阅到期通知发送失败: 用户 {user.username}, 订阅 {subscription_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送订阅到期通知失败: {e}")
            return False

    def send_subscription_warning_notification(self, subscription_id: int, days_remaining: int) -> bool:
        """
        发送订阅即将到期警告
        
        Args:
            subscription_id: 订阅ID
            days_remaining: 剩余天数
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 获取订阅信息
            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                logger.error(f"订阅不存在: {subscription_id}")
                return False
            
            # 获取用户信息
            order = Order.query.get(subscription.order_id)
            if not order:
                logger.error(f"订单不存在: {subscription.order_id}")
                return False
            
            user = User.query.get(order.user_id)
            if not user or not user.email:
                logger.error(f"用户不存在或无邮箱: {order.user_id}")
                return False
            
            # 发送警告邮件
            subject = f"订阅即将到期提醒 - 还有 {days_remaining} 天"
            
            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>{subject}</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
                        <h1 style="margin: 0; font-size: 28px;">订阅即将到期</h1>
                    </div>
                    
                    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
                        <h2 style="color: #495057;">尊敬的 {user.username}，</h2>
                        <p>您的订阅将在 <strong style="color: #ff7043;">{days_remaining} 天</strong> 后到期，请及时续费。</p>
                        
                        <div style="background: #fff; border-left: 4px solid #ffa726; padding: 20px; margin: 20px 0;">
                            <h3 style="margin-top: 0; color: #ffa726;">订阅信息</h3>
                            <table style="width: 100%; border-collapse: collapse;">
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>订阅ID：</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{subscription.id}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>产品名称：</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{f"{order.node_type.value.upper()} 套餐" if order.node_type else "数据套餐"}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>到期时间：</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{subscription.expires_at.strftime('%Y-%m-%d %H:%M:%S') if subscription.expires_at else 'N/A'}</td>
                                </tr>
                                <tr>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;"><strong>流量限制：</strong></td>
                                    <td style="padding: 8px 0; border-bottom: 1px solid #eee;">{order.traffic_limit_gb} GB</td>
                                </tr>
                            </table>
                        </div>
                        
                        <p style="color: #6c757d;">
                            <strong>温馨提示：</strong><br>
                            • 订阅到期后服务将自动停止<br>
                            • 建议提前续费以保证服务连续性<br>
                            • 如有疑问，请联系客服
                        </p>
                        
                        <div style="text-align: center; margin: 30px 0;">
                            <a href="#" style="background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">立即续费</a>
                        </div>
                    </div>
                    
                    <div style="text-align: center; padding: 20px; color: #6c757d; font-size: 14px;">
                        <p>此邮件由系统自动发送，请勿回复</p>
                        <p>如有疑问，请联系客服</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            success = email_service.send_email(user.email, subject, html_body, is_html=True)
            
            if success:
                logger.info(f"订阅警告通知发送成功: 用户 {user.username}, 订阅 {subscription_id}, 剩余 {days_remaining} 天")
            else:
                logger.error(f"订阅警告通知发送失败: 用户 {user.username}, 订阅 {subscription_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"发送订阅警告通知失败: {e}")
            return False

    def send_verification_code(self, email: str, code_type: str) -> Dict[str, Any]:
        """
        发送验证码
        
        Args:
            email: 邮箱地址
            code_type: 验证码类型
            
        Returns:
            Dict: 发送结果
        """
        success, message, extra_info = verification_service.send_verification_code(email, code_type)
        
        return {
            'success': success,
            'message': message,
            'data': extra_info
        }

    def verify_code(self, email: str, code: str, code_type: str) -> Dict[str, Any]:
        """
        验证验证码
        
        Args:
            email: 邮箱地址
            code: 验证码
            code_type: 验证码类型
            
        Returns:
            Dict: 验证结果
        """
        success, message, extra_info = verification_service.verify_code(email, code, code_type)
        
        return {
            'success': success,
            'message': message,
            'data': extra_info
        }

    def check_expiring_subscriptions(self, warning_days: List[int] = [7, 3, 1]) -> Dict[str, Any]:
        """
        检查即将到期的订阅并发送通知
        
        Args:
            warning_days: 提前警告的天数列表
            
        Returns:
            Dict: 处理结果
        """
        try:
            results = {
                'total_checked': 0,
                'warnings_sent': 0,
                'expiry_notifications_sent': 0,
                'errors': []
            }
            
            now = datetime.utcnow()
            
            # 检查活跃的订阅
            active_subscriptions = Subscription.query.filter_by(is_active=True).all()
            results['total_checked'] = len(active_subscriptions)
            
            for subscription in active_subscriptions:
                try:
                    if not subscription.expires_at:
                        continue
                    
                    time_remaining = subscription.expires_at - now
                    days_remaining = time_remaining.days
                    
                    # 已过期的订阅
                    if days_remaining < 0:
                        if self.send_subscription_expiry_notification(subscription.id):
                            results['expiry_notifications_sent'] += 1
                    
                    # 即将到期的订阅
                    elif days_remaining in warning_days:
                        if self.send_subscription_warning_notification(subscription.id, days_remaining):
                            results['warnings_sent'] += 1
                
                except Exception as e:
                    error_msg = f"处理订阅 {subscription.id} 失败: {str(e)}"
                    logger.error(error_msg)
                    results['errors'].append(error_msg)
            
            logger.info(f"订阅到期检查完成: 检查 {results['total_checked']} 个订阅, "
                       f"发送 {results['warnings_sent']} 个警告, "
                       f"发送 {results['expiry_notifications_sent']} 个到期通知")
            
            return results
            
        except Exception as e:
            logger.error(f"检查即将到期订阅失败: {e}")
            return {
                'total_checked': 0,
                'warnings_sent': 0,
                'expiry_notifications_sent': 0,
                'errors': [str(e)]
            }

    def get_notification_stats(self) -> Dict[str, Any]:
        """获取通知统计信息"""
        try:
            # 获取验证码统计
            verification_stats = verification_service.get_verification_stats()
            
            # 获取邮件配置状态
            email_config = email_service._get_config()
            email_status = {
                'configured': email_config is not None,
                'config_name': email_config.name if email_config else None,
                'last_used': email_config.last_used_at.isoformat() if email_config and email_config.last_used_at else None
            }
            
            return {
                'email_status': email_status,
                'verification_stats': verification_stats,
                'notification_types': self.notification_types
            }
            
        except Exception as e:
            logger.error(f"获取通知统计失败: {e}")
            return {}


# 创建全局实例
notification_service = NotificationService()
