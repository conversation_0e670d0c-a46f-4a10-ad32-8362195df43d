"""
配置迁移脚本
将config.py中的X-UI面板配置迁移到数据库
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app
from services.config_service import config_service
from models.database import db, XUIPanel, PanelStatus

def migrate_config():
    """执行配置迁移"""
    app = create_app()
    
    with app.app_context():
        print("开始配置迁移...")
        print("=" * 50)
        
        # 检查当前数据库中的面板数量
        existing_panels = XUIPanel.query.count()
        print(f"数据库中现有面板数量: {existing_panels}")
        
        # 执行迁移
        success = config_service.migrate_config_to_database()
        
        if success:
            # 检查迁移后的面板数量
            new_panels = XUIPanel.query.count()
            migrated_count = new_panels - existing_panels
            
            print(f"✓ 配置迁移成功")
            print(f"新增面板数量: {migrated_count}")
            print(f"数据库中总面板数量: {new_panels}")
            
            # 显示所有面板信息
            if new_panels > 0:
                print("\n当前数据库中的面板:")
                print("-" * 50)
                panels = XUIPanel.query.all()
                for panel in panels:
                    print(f"ID: {panel.id}")
                    print(f"名称: {panel.name}")
                    print(f"地址: {panel.base_url}{panel.path_prefix}")
                    print(f"用户名: {panel.username}")
                    print(f"状态: {panel.status.value}")
                    print(f"优先级: {panel.priority}")
                    print("-" * 30)
            
            # 测试配置服务
            print("\n测试配置服务...")
            panels_config = config_service.get_xui_panels()
            print(f"配置服务返回 {len(panels_config)} 个面板配置")
            
            for panel_id, panel_config in panels_config.items():
                print(f"面板ID: {panel_id}")
                print(f"名称: {panel_config['name']}")
                print(f"地址: {panel_config['base_url']}{panel_config['path_prefix']}")
                print("-" * 20)
        else:
            print("✗ 配置迁移失败")
            return False
        
        print("=" * 50)
        print("配置迁移完成")
        return True

def rollback_migration():
    """回滚迁移（删除所有面板配置）"""
    app = create_app()
    
    with app.app_context():
        print("警告：即将删除数据库中的所有X-UI面板配置！")
        confirm = input("请输入 'YES' 确认删除: ")
        
        if confirm == 'YES':
            try:
                deleted_count = XUIPanel.query.delete()
                db.session.commit()
                print(f"✓ 已删除 {deleted_count} 个面板配置")
                
                # 清除配置服务缓存
                config_service.refresh_cache()
                print("✓ 已清除配置缓存")
                
            except Exception as e:
                db.session.rollback()
                print(f"✗ 删除失败: {e}")
                return False
        else:
            print("取消删除操作")
            return False
        
        return True

def show_current_config():
    """显示当前配置状态"""
    app = create_app()
    
    with app.app_context():
        print("当前配置状态")
        print("=" * 50)
        
        # 显示数据库配置
        panels = XUIPanel.query.all()
        print(f"数据库中的面板数量: {len(panels)}")
        
        if panels:
            print("\n数据库中的面板:")
            for panel in panels:
                print(f"- {panel.name} ({panel.base_url}{panel.path_prefix})")
        
        # 显示config.py配置
        try:
            from config import Config
            if hasattr(Config, 'XUI_PANELS') and Config.XUI_PANELS:
                print(f"\nconfig.py中的面板数量: {len(Config.XUI_PANELS)}")
                print("config.py中的面板:")
                for panel_id, panel_config in Config.XUI_PANELS.items():
                    print(f"- {panel_config.get('name', panel_id)} ({panel_config['base_url']}{panel_config['path_prefix']})")
            else:
                print("\nconfig.py中没有XUI_PANELS配置")
        except Exception as e:
            print(f"\n无法读取config.py配置: {e}")
        
        # 显示配置服务状态
        try:
            panels_config = config_service.get_xui_panels()
            print(f"\n配置服务返回的面板数量: {len(panels_config)}")
            if panels_config:
                print("配置服务中的面板:")
                for panel_id, panel_config in panels_config.items():
                    print(f"- {panel_config['name']} ({panel_config['base_url']}{panel_config['path_prefix']})")
        except Exception as e:
            print(f"\n配置服务错误: {e}")

if __name__ == '__main__':
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == 'migrate':
            migrate_config()
        elif command == 'rollback':
            rollback_migration()
        elif command == 'status':
            show_current_config()
        else:
            print("未知命令。可用命令: migrate, rollback, status")
    else:
        print("配置迁移脚本")
        print("使用方法:")
        print("  python scripts/migrate_config.py migrate   - 执行配置迁移")
        print("  python scripts/migrate_config.py rollback  - 回滚迁移")
        print("  python scripts/migrate_config.py status    - 显示当前配置状态")
