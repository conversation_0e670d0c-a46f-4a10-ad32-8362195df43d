"""
流量统计服务 - 定时收集和统计用户订阅流量
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from models import Order
from sqlalchemy import func, and_, desc
from models import db, User, Subscription, TrafficStats, XUIPanelGroup, Order, OrderStatus
from multi_xui_manager import MultiXUIManager

logger = logging.getLogger(__name__)

class TrafficStatsService:
    """流量统计服务类"""
    
    def __init__(self):
        self.xui_manager = MultiXUIManager() # This default manager might not use the run-specific cache, or needs re-evaluation.
                                           # For now, the primary cache usage will be for managers created during collect_all_traffic_stats
        self.current_run_cache: Optional[Dict] = None
    
    def collect_all_traffic_stats(self) -> bool:
        """收集所有活跃订阅的流量统计 - 按分组批量处理优化版"""
        self.current_run_cache = {} # Initialize cache for this run
        try:
            logger.info("开始收集流量统计...")

            # 获取所有活跃订阅 - 明确指定连接条件以避免多外键关系的歧义
            active_subscriptions = Subscription.query.join(
                Order, Subscription.order_id == Order.id
            ).filter(
                Subscription.is_active == True,
                Subscription.expires_at > datetime.utcnow(),
                Order.status == OrderStatus.COMPLETED
            ).all()

            total_subscriptions = len(active_subscriptions)
            logger.info(f"找到 {total_subscriptions} 个活跃订阅需要收集流量统计")

            if total_subscriptions == 0:
                logger.info("没有活跃订阅，跳过流量统计收集")
                return True

            # 按分组ID分组订阅
            from collections import defaultdict
            subscriptions_by_group = defaultdict(list)
            for subscription in active_subscriptions:
                group_id = subscription.group_id  # 可能为None
                subscriptions_by_group[group_id].append(subscription)

            logger.info(f"订阅分组情况: {len(subscriptions_by_group)} 个分组")
            for group_id, subs in subscriptions_by_group.items():
                group_name = f"分组{group_id}" if group_id else "默认分组"
                logger.debug(f"{group_name}: {len(subs)} 个订阅")

            success_count = 0
            error_count = 0

            # 按分组处理订阅
            for group_id, group_subscriptions in subscriptions_by_group.items():
                try:
                    group_name = f"分组{group_id}" if group_id else "默认分组"
                    logger.info(f"开始处理{group_name} ({len(group_subscriptions)} 个订阅)")

                    group_success, group_error = self._collect_group_traffic(group_id, group_subscriptions)
                    success_count += group_success
                    error_count += group_error

                    logger.info(f"{group_name}处理完成: 成功 {group_success}, 失败 {group_error}")

                    # 每个分组处理后提交一次数据库事务
                    try:
                        db.session.commit()
                        logger.debug(f"{group_name}数据已提交")
                    except Exception as e:
                        logger.error(f"提交{group_name}数据失败: {e}")
                        db.session.rollback()

                except Exception as e:
                    logger.error(f"处理{group_name}时发生错误: {e}")
                    error_count += len(group_subscriptions)

            success_rate = (success_count / total_subscriptions) * 100 if total_subscriptions > 0 else 0
            logger.info(f"流量统计收集完成: 成功 {success_count}/{total_subscriptions} ({success_rate:.1f}%), 失败 {error_count}")

            # 如果成功率低于50%，认为任务失败（调整阈值，因为网络问题可能导致部分失败）
            # 如果有任何成功的记录，就认为优化是有效的
            if success_count > 0:
                logger.info(f"✅ 按分组批量处理优化生效: 成功处理了 {success_count} 个订阅")
                return success_rate >= 50.0
            else:
                logger.warning("⚠️ 没有成功处理任何订阅，可能是网络或配置问题")
                return False

        except Exception as e:
            logger.error(f"收集流量统计失败: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return False
        finally:
            logger.info("清空当次运行的面板数据缓存。")
            self.current_run_cache = None # Clear cache after the run

    def _collect_group_traffic(self, group_id: Optional[int], subscriptions: List[Subscription]) -> tuple[int, int]:
        """收集单个分组的流量统计

        Args:
            group_id: 分组ID，None表示默认分组
            subscriptions: 该分组的订阅列表

        Returns:
            tuple: (成功数量, 失败数量)
        """
        success_count = 0
        error_count = 0

        try:
            group_name = f"分组{group_id}" if group_id else "默认分组"
            logger.debug(f"开始收集{group_name}的流量数据")

            # 创建对应的管理器
            if group_id is not None:
                manager = MultiXUIManager.from_group(group_id)
            else:
                manager = MultiXUIManager()

            # 一次性获取该分组所有面板的客户端流量数据
            logger.debug(f"获取{group_name}所有面板的流量数据...")
            all_traffic_data = manager.get_all_client_traffic()

            if not all_traffic_data:
                logger.warning(f"{group_name}没有获取到任何流量数据")
                return 0, len(subscriptions)

            # 构建客户端邮箱到流量数据的映射
            client_traffic_map = {}
            total_clients = 0
            for panel_id, client_stats in all_traffic_data.items():
                if client_stats:
                    for client_stat in client_stats:
                        email = client_stat.get('email')
                        if email:
                            # 如果同一客户端在多个面板中存在，累加流量
                            if email in client_traffic_map:
                                existing = client_traffic_map[email]
                                client_traffic_map[email] = {
                                    'upload_bytes': existing['upload_bytes'] + client_stat.get('up', 0),
                                    'download_bytes': existing['download_bytes'] + client_stat.get('down', 0),
                                    'total_bytes': existing['total_bytes'] + client_stat.get('total', 0)
                                }
                            else:
                                client_traffic_map[email] = {
                                    'upload_bytes': client_stat.get('up', 0),
                                    'download_bytes': client_stat.get('down', 0),
                                    'total_bytes': client_stat.get('total', 0)
                                }
                            total_clients += 1

            logger.debug(f"{group_name}获取到 {len(client_traffic_map)} 个唯一客户端的流量数据")

            # 处理该分组的每个订阅
            for subscription in subscriptions:
                try:
                    if self._match_subscription_traffic(subscription, client_traffic_map):
                        success_count += 1
                    else:
                        error_count += 1
                except Exception as e:
                    logger.error(f"处理订阅 {subscription.id} 流量匹配失败: {e}")
                    error_count += 1

            return success_count, error_count

        except Exception as e:
            logger.error(f"收集{group_name}流量数据时发生错误: {e}")
            return 0, len(subscriptions)

    def _match_subscription_traffic(self, subscription: Subscription, client_traffic_map: Dict[str, Dict]) -> bool:
        """从缓存的流量数据中匹配订阅的流量信息

        Args:
            subscription: 订阅对象
            client_traffic_map: 客户端邮箱到流量数据的映射

        Returns:
            bool: 是否成功匹配并记录流量数据
        """
        try:
            order = subscription.order
            if not order:
                logger.warning(f"订阅 {subscription.id} 缺少关联订单")
                return False

            # 获取客户端邮箱列表
            client_emails = self._get_order_client_emails(order)
            if not client_emails:
                logger.warning(f"订单 {order.order_id} 没有找到客户端邮箱")
                return False

            # 累加所有客户端的流量
            total_up = 0
            total_down = 0
            total_traffic = 0
            found_clients = 0

            for client_email in client_emails:
                if client_email in client_traffic_map:
                    traffic_data = client_traffic_map[client_email]
                    total_up += traffic_data['upload_bytes']
                    total_down += traffic_data['download_bytes']
                    total_traffic += traffic_data['total_bytes']
                    found_clients += 1
                    logger.debug(f"找到客户端 {client_email} 的流量数据: {traffic_data['total_bytes']} bytes")
                else:
                    logger.debug(f"未找到客户端 {client_email} 的流量数据")

            if found_clients == 0:
                logger.warning(f"订阅 {subscription.id} 的所有客户端都未找到流量数据")
                return False

            # 检查流量数据是否合理（避免异常大的数值）
            if total_traffic > 1024 * 1024 * 1024 * 1024:  # 1TB
                logger.warning(f"订阅 {subscription.id} 流量数据异常大: {total_traffic} bytes，跳过记录")
                return False

            # {{CHENGQI:
            # Action: Modified
            # Timestamp: 2025-01-27 14:35:00 +08:00
            # Task_ID: P4-LD-004
            # Principle_Applied: SOLID - 集成基准流量计算，保持数据一致性
            # Language: Python
            # Description: 在记录流量统计时集成历史基准，确保总流量包含已删除面板的贡献
            # }}

            # 获取历史基准流量
            from models.subscription_traffic_baseline import SubscriptionTrafficBaseline
            baseline = SubscriptionTrafficBaseline.query.filter_by(
                subscription_id=subscription.id
            ).first()

            # 计算包含基准的总流量
            baseline_upload = baseline.baseline_upload_bytes if baseline else 0
            baseline_download = baseline.baseline_download_bytes if baseline else 0
            baseline_total = baseline.baseline_total_bytes if baseline else 0

            final_upload = total_up + baseline_upload
            final_download = total_down + baseline_download
            final_total = total_traffic + baseline_total

            # 记录流量统计（包含基准）
            traffic_stat = TrafficStats(
                user_id=order.user_id,
                subscription_id=subscription.id,
                group_id=subscription.group_id,
                upload_bytes=final_upload,
                download_bytes=final_download,
                total_bytes=final_total,
                recorded_at=datetime.utcnow()
            )

            logger.debug(f"订阅 {subscription.id} 流量统计: 实时={total_traffic}, 基准={baseline_total}, 总计={final_total}")

            db.session.add(traffic_stat)
            logger.debug(f"记录订阅 {subscription.id} 流量统计: 上传 {total_up}, 下载 {total_down}, 总计 {total_traffic}")
            return True

        except Exception as e:
            logger.error(f"匹配订阅 {subscription.id} 流量数据失败: {e}")
            return False

    def _get_order_client_emails(self, order: Order) -> List[str]:
        """获取订单的所有客户端邮箱列表"""
        client_emails = []

        try:
            # 如果订单有节点配置，使用节点配置中的客户端邮箱
            if order.node_configs:
                for config in order.node_configs:
                    if config.is_active and config.client_email:
                        client_emails.append(config.client_email)
            else:
                # 如果没有节点配置，尝试使用生成的客户端邮箱
                if order.customer_email:
                    generated_client_email = order.customer_remarks or f"{order.customer_email}_{order.order_id}"
                    client_emails.append(generated_client_email)

            return client_emails

        except Exception as e:
            logger.error(f"获取订单 {order.order_id} 客户端邮箱失败: {e}")
            return []

    def _collect_subscription_traffic(self, subscription: Subscription) -> bool:
        """收集单个订阅的流量统计 - 已弃用，保留用于兼容性

        注意：此方法已被新的按分组批量处理逻辑替代，仅在特殊情况下使用
        """
        logger.warning(f"使用了已弃用的_collect_subscription_traffic方法处理订阅 {subscription.id}")
        max_retries = 2
        retry_count = 0

        while retry_count <= max_retries:
            try:
                order = subscription.order
                if not order:
                    logger.warning(f"订阅 {subscription.id} 缺少关联订单")
                    return False

                # 获取流量数据 - 使用与SubscriptionService相同的逻辑
                traffic_data = self._get_subscription_traffic_data_from_order(order)
                if not traffic_data:
                    if retry_count < max_retries:
                        retry_count += 1
                        logger.debug(f"订阅 {subscription.id} 流量数据获取失败，重试 {retry_count}/{max_retries}")
                        continue
                    else:
                        logger.warning(f"无法获取订阅 {subscription.id} 的流量数据，已重试 {max_retries} 次")
                        return False

                # 检查流量数据是否合理（避免异常大的数值）
                if traffic_data['total_bytes'] > 1024 * 1024 * 1024 * 1024:  # 1TB
                    logger.warning(f"订阅 {subscription.id} 流量数据异常大: {traffic_data['total_bytes']} bytes，跳过记录")
                    return False

                # 创建流量统计记录
                traffic_stat = TrafficStats(
                    user_id=order.user_id,
                    subscription_id=subscription.id,
                    group_id=subscription.group_id,
                    upload_bytes=traffic_data['upload_bytes'],
                    download_bytes=traffic_data['download_bytes'],
                    total_bytes=traffic_data['total_bytes'],
                    recorded_at=datetime.utcnow()
                )

                db.session.add(traffic_stat)
                # 不在这里提交，让批量处理统一提交

                logger.debug(f"订阅 {subscription.id} 流量统计记录成功: {traffic_data['total_bytes']} bytes")
                return True

            except Exception as e:
                retry_count += 1
                if retry_count <= max_retries:
                    logger.debug(f"收集订阅 {subscription.id} 流量失败，重试 {retry_count}/{max_retries}: {e}")
                else:
                    logger.error(f"收集订阅 {subscription.id} 流量失败，已重试 {max_retries} 次: {e}")
                    db.session.rollback()
                    return False

        return False
    
    def _get_subscription_traffic_data_from_order(self, order) -> Optional[Dict]:
        """根据订单获取流量数据 - 与SubscriptionService逻辑一致"""
        try:
            total_up = 0
            total_down = 0
            total_traffic = 0

            # 根据订单的产品分组选择正确的管理器
            manager = self._get_manager_for_order(order)

            # 如果有节点配置，使用节点配置中的客户端邮箱
            if order.node_configs:
                logger.debug(f"订单 {order.order_id} 有 {len(order.node_configs)} 个节点配置")
                for config in order.node_configs:
                    if config.is_active and config.client_email:
                        # 从X-UI获取实时流量数据
                        traffic_data = manager.get_client_traffic_summary(config.client_email)
                        if traffic_data:
                            total_up += traffic_data.get('total_up', 0)
                            total_down += traffic_data.get('total_down', 0)
                            total_traffic += traffic_data.get('total_traffic', 0)
                            logger.debug(f"找到客户端 {config.client_email} 的流量数据: {traffic_data.get('total_traffic', 0)} bytes")
                        else:
                            logger.warning(f"无法获取客户端 {config.client_email} 的流量数据")
            else:
                # 如果没有节点配置，尝试使用生成的客户端邮箱
                if not order.customer_email:
                    logger.warning(f"订单 {order.order_id} 缺少客户端邮箱")
                    return None
                
                # 根据订单服务的逻辑，客户端邮箱格式为: customer_email_order_id
                generated_client_email = order.customer_remarks or f"{order.customer_email}_{order.order_id}"
                logger.debug(f"订单 {order.order_id} 没有节点配置，尝试使用生成的客户端邮箱: {generated_client_email}")

                traffic_data = manager.get_client_traffic_summary(generated_client_email)
                if traffic_data:
                    total_up = traffic_data.get('total_up', 0)
                    total_down = traffic_data.get('total_down', 0)
                    total_traffic = traffic_data.get('total_traffic', 0)
                    logger.debug(f"找到客户端 {generated_client_email} 的流量数据: {total_traffic} bytes")
                else:
                    logger.warning(f"无法获取客户端 {generated_client_email} 的流量数据")
                    return None

            return {
                'upload_bytes': total_up,
                'download_bytes': total_down,
                'total_bytes': total_traffic
            }
            
        except Exception as e:
            logger.error(f"获取订单 {order.order_id} 流量数据失败: {e}")
            return None
            
    def _get_manager_for_order(self, order) -> 'MultiXUIManager': # Added return type hint for MultiXUIManager
        """根据订单获取合适的MultiXUIManager实例 - 与SubscriptionService逻辑一致"""
        try:
            # Ensure cache is initialized for the current run, though it should be by collect_all_traffic_stats
            if self.current_run_cache is None:
                logger.warning("current_run_cache is None in _get_manager_for_order. Initializing to empty dict. This might indicate an issue.")
                self.current_run_cache = {}

            if order.product and order.product.target_group_id:
                logger.debug(f"订单 {order.order_id} 使用产品分组 {order.product.target_group_id}")
                # Pass the current run's cache to the group-specific manager
                return MultiXUIManager.from_group(order.product.target_group_id, panel_cache=self.current_run_cache)
            else:
                logger.debug(f"订单 {order.order_id} 使用默认面板配置")
                # Pass the current run's cache to the default manager
                return MultiXUIManager(panel_cache=self.current_run_cache)
        except Exception as e:
            logger.error(f"为订单 {order.order_id} 创建管理器失败: {e}")
            # Fallback, still try to pass the cache
            return MultiXUIManager(panel_cache=self.current_run_cache if self.current_run_cache is not None else {})
    
    def _get_subscription_traffic_data(self, subscription: Subscription, client_email: str) -> Optional[Dict]:
        """获取订阅的流量数据 - 保留兼容性，但已不推荐使用"""
        logger.warning("使用了已弃用的_get_subscription_traffic_data方法，建议使用_get_subscription_traffic_data_from_order")
        try:
            # 如果订阅有指定分组，使用该分组的面板
            if subscription.group_id:
                manager = MultiXUIManager.from_group(subscription.group_id)
            else:
                manager = MultiXUIManager()
            
            # 获取客户端流量汇总
            traffic_summary = manager.get_client_traffic_summary(client_email)
            if not traffic_summary:
                return None
            
            return {
                'upload_bytes': traffic_summary.get('total_up', 0),
                'download_bytes': traffic_summary.get('total_down', 0),
                'total_bytes': traffic_summary.get('total_traffic', 0)
            }
            
        except Exception as e:
            logger.error(f"获取客户端 {client_email} 流量数据失败: {e}")
            return None
    
    def get_user_traffic_stats(self, user_id: int, days: int = 30) -> List[Dict]:
        """获取用户的流量统计"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            stats = TrafficStats.query.filter(
                TrafficStats.user_id == user_id,
                TrafficStats.recorded_at >= start_date
            ).order_by(desc(TrafficStats.recorded_at)).all()
            
            return [stat.to_dict() for stat in stats]
            
        except Exception as e:
            logger.error(f"获取用户 {user_id} 流量统计失败: {e}")
            return []
    
    def get_group_traffic_stats(self, group_id: int, days: int = 30) -> Dict:
        """获取分组的流量统计汇总"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # 获取分组信息
            group = XUIPanelGroup.query.get(group_id)
            if not group:
                return {}
            
            # 统计分组总流量
            total_stats = db.session.query(
                func.sum(TrafficStats.upload_bytes).label('total_upload'),
                func.sum(TrafficStats.download_bytes).label('total_download'),
                func.sum(TrafficStats.total_bytes).label('total_traffic'),
                func.count(TrafficStats.id).label('record_count')
            ).filter(
                TrafficStats.group_id == group_id,
                TrafficStats.recorded_at >= start_date
            ).first()
            
            # 获取用户流量排行
            user_stats = db.session.query(
                TrafficStats.user_id,
                User.username,
                func.sum(TrafficStats.total_bytes).label('total_traffic')
            ).join(User).filter(
                TrafficStats.group_id == group_id,
                TrafficStats.recorded_at >= start_date
            ).group_by(TrafficStats.user_id).order_by(
                desc(func.sum(TrafficStats.total_bytes))
            ).limit(10).all()
            
            return {
                'group_id': group_id,
                'group_name': group.name,
                'total_upload_gb': round((total_stats.total_upload or 0) / (1024**3), 3),
                'total_download_gb': round((total_stats.total_download or 0) / (1024**3), 3),
                'total_traffic_gb': round((total_stats.total_traffic or 0) / (1024**3), 3),
                'record_count': total_stats.record_count or 0,
                'top_users': [
                    {
                        'user_id': stat.user_id,
                        'username': stat.username,
                        'total_traffic_gb': round(stat.total_traffic / (1024**3), 3)
                    }
                    for stat in user_stats
                ]
            }
            
        except Exception as e:
            logger.error(f"获取分组 {group_id} 流量统计失败: {e}")
            return {}
    
    def get_all_groups_traffic_summary(self, days: int = 30) -> List[Dict]:
        """获取所有分组的流量统计汇总"""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # 获取所有有流量记录的分组
            group_stats = db.session.query(
                TrafficStats.group_id,
                XUIPanelGroup.name,
                func.sum(TrafficStats.total_bytes).label('total_traffic'),
                func.count(func.distinct(TrafficStats.user_id)).label('user_count')
            ).join(XUIPanelGroup, TrafficStats.group_id == XUIPanelGroup.id).filter(
                TrafficStats.recorded_at >= start_date
            ).group_by(TrafficStats.group_id).order_by(
                desc(func.sum(TrafficStats.total_bytes))
            ).all()
            
            return [
                {
                    'group_id': stat.group_id,
                    'group_name': stat.name,
                    'total_traffic_gb': round(stat.total_traffic / (1024**3), 3),
                    'user_count': stat.user_count
                }
                for stat in group_stats
            ]
            
        except Exception as e:
            logger.error(f"获取所有分组流量统计失败: {e}")
            return []
    
    def get_subscription_latest_traffic_stats(self, subscription_id: int, order: 'Order') -> Dict:
        """
        从数据库获取订阅的最新流量统计

        Args:
            subscription_id: 订阅ID
            order: 订单对象（用于获取流量限制等信息）

        Returns:
            Dict: 流量统计数据，格式与SubscriptionService._get_order_traffic_stats()兼容
        """
        try:
            # 查询该订阅的最新流量统计记录
            latest_stats = TrafficStats.query.filter_by(
                subscription_id=subscription_id
            ).order_by(desc(TrafficStats.recorded_at)).first()

            if latest_stats:
                # 转换为MB
                total_up_mb = latest_stats.upload_bytes / (1024**2)
                total_down_mb = latest_stats.download_bytes / (1024**2)

                # 修复：使用上传+下载的正确总和，而不是可能错误的total_bytes字段
                # 如果total_bytes明显错误（比如等于流量限制），则重新计算
                calculated_total_bytes = latest_stats.upload_bytes + latest_stats.download_bytes
                stored_total_bytes = latest_stats.total_bytes

                # 检查stored_total_bytes是否合理（不应该远大于上传+下载的总和）
                if stored_total_bytes > calculated_total_bytes * 10:  # 如果存储的总量比计算的大10倍以上，认为是错误的
                    logger.debug(f"订阅 {subscription_id} 的total_bytes字段可能有误: 存储值={stored_total_bytes}, 计算值={calculated_total_bytes}，使用计算值")
                    total_traffic_mb = calculated_total_bytes / (1024**2)
                else:
                    total_traffic_mb = stored_total_bytes / (1024**2)

                logger.debug(f"从数据库获取订阅 {subscription_id} 流量统计: {total_traffic_mb:.2f} MB")
            else:
                # 如果没有流量统计记录，返回0
                total_up_mb = 0
                total_down_mb = 0
                total_traffic_mb = 0
                logger.debug(f"订阅 {subscription_id} 没有流量统计记录")

            # 流量限制转换为MB
            traffic_limit_mb = order.traffic_limit_gb * 1024

            # 计算使用百分比
            usage_percentage = (total_traffic_mb / traffic_limit_mb * 100) if traffic_limit_mb > 0 else 0

            return {
                'total_up_mb': round(total_up_mb, 2),
                'total_down_mb': round(total_down_mb, 2),
                'total_traffic_mb': round(total_traffic_mb, 2),
                'traffic_limit_mb': traffic_limit_mb,
                'usage_percentage': min(round(usage_percentage, 1), 100),
                'remaining_mb': max(round(traffic_limit_mb - total_traffic_mb, 2), 0),
                'last_updated': latest_stats.recorded_at if latest_stats else None
            }

        except Exception as e:
            logger.error(f"从数据库获取订阅 {subscription_id} 流量统计失败: {e}")
            # 返回默认值
            traffic_limit_mb = order.traffic_limit_gb * 1024
            return {
                'total_up_mb': 0,
                'total_down_mb': 0,
                'total_traffic_mb': 0,
                'traffic_limit_mb': traffic_limit_mb,
                'usage_percentage': 0,
                'remaining_mb': traffic_limit_mb,
                'last_updated': None
            }

    def get_order_traffic_stats_from_db(self, order: 'Order') -> Dict:
        """
        从数据库获取订单的流量统计（通过订阅记录）

        Args:
            order: 订单对象

        Returns:
            Dict: 流量统计数据
        """
        try:
            # 查找订单关联的订阅
            from models import Subscription
            subscription = Subscription.query.filter_by(order_id=order.id).first()

            if not subscription:
                logger.warning(f"订单 {order.order_id} 没有关联的订阅记录")
                traffic_limit_mb = order.traffic_limit_gb * 1024
                return {
                    'total_up_mb': 0,
                    'total_down_mb': 0,
                    'total_traffic_mb': 0,
                    'traffic_limit_mb': traffic_limit_mb,
                    'usage_percentage': 0,
                    'remaining_mb': traffic_limit_mb,
                    'last_updated': None
                }

            # 使用订阅ID获取流量统计
            return self.get_subscription_latest_traffic_stats(subscription.id, order)

        except Exception as e:
            logger.error(f"从数据库获取订单 {order.order_id} 流量统计失败: {e}")
            traffic_limit_mb = order.traffic_limit_gb * 1024
            return {
                'total_up_mb': 0,
                'total_down_mb': 0,
                'total_traffic_mb': 0,
                'traffic_limit_mb': traffic_limit_mb,
                'usage_percentage': 0,
                'remaining_mb': traffic_limit_mb,
                'last_updated': None
            }

    def cleanup_old_stats(self, days: int = 90) -> bool:
        """清理旧的流量统计记录"""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days)

            deleted_count = TrafficStats.query.filter(
                TrafficStats.recorded_at < cutoff_date
            ).delete()

            db.session.commit()

            if deleted_count > 0:
                logger.info(f"清理了 {deleted_count} 条旧的流量统计记录")

            return True

        except Exception as e:
            logger.error(f"清理旧流量统计记录失败: {e}")
            db.session.rollback()
            return False
