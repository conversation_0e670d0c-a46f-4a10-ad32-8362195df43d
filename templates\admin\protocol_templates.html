{% extends "admin/base.html" %}

{% block title %}协议模板管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>协议模板管理</h2>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTemplateModal">
                    <i class="bi bi-plus-circle me-2"></i>创建模板
                </button>
            </div>

            <!-- 协议类型标签页 -->
            <ul class="nav nav-tabs" id="protocolTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                        全部 ({{ all_templates|length }})
                    </button>
                </li>
                {% for protocol_type, templates in templates_by_type.items() %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="{{ protocol_type }}-tab" data-bs-toggle="tab" data-bs-target="#{{ protocol_type }}" type="button" role="tab">
                        {{ protocol_type.upper() }} ({{ templates|length }})
                    </button>
                </li>
                {% endfor %}
            </ul>

            <!-- 标签页内容 -->
            <div class="tab-content" id="protocolTabsContent">
                <!-- 全部模板 -->
                <div class="tab-pane fade show active" id="all" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            {% if all_templates %}
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>模板名称</th>
                                                <th>协议类型</th>
                                                <th>状态</th>
                                                <th>使用次数</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for template in all_templates %}
                                            <tr>
                                                <td>
                                                    <strong>{{ template.name }}</strong>
                                                    {% if template.is_default %}
                                                        <span class="badge bg-primary ms-2">默认</span>
                                                    {% endif %}
                                                    {% if template.description %}
                                                        <br><small class="text-muted">{{ template.description }}</small>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary">{{ template.protocol_type.upper() }}</span>
                                                </td>
                                                <td>
                                                    {% if template.is_active %}
                                                        <span class="badge bg-success">活跃</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">禁用</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ template.usage_count }}</td>
                                                <td>{{ template.created_at[:19] if template.created_at else '-' }}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-info" onclick="previewTemplate({{ template.id }})">
                                                            <i class="bi bi-eye"></i> 预览
                                                        </button>
                                                        <button type="button" class="btn btn-outline-primary" onclick="editTemplate({{ template.id }})">
                                                            <i class="bi bi-pencil"></i> 编辑
                                                        </button>
                                                        {% if not template.is_default %}
                                                        <button type="button" class="btn btn-outline-success" onclick="setDefaultTemplate({{ template.id }})">
                                                            <i class="bi bi-star"></i> 设为默认
                                                        </button>
                                                        {% endif %}
                                                        {% if not template.is_default %}
                                                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate({{ template.id }})">
                                                            <i class="bi bi-trash"></i> 删除
                                                        </button>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="bi bi-file-text display-1 text-muted"></i>
                                    <h5 class="mt-3">暂无协议模板</h5>
                                    <p class="text-muted">点击上方按钮创建第一个协议模板</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- 各协议类型的模板 -->
                {% for protocol_type, templates in templates_by_type.items() %}
                <div class="tab-pane fade" id="{{ protocol_type }}" role="tabpanel">
                    <div class="card">
                        <div class="card-body">
                            {% if templates %}
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>模板名称</th>
                                                <th>状态</th>
                                                <th>使用次数</th>
                                                <th>创建时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for template in templates %}
                                            <tr>
                                                <td>
                                                    <strong>{{ template.name }}</strong>
                                                    {% if template.is_default %}
                                                        <span class="badge bg-primary ms-2">默认</span>
                                                    {% endif %}
                                                    {% if template.description %}
                                                        <br><small class="text-muted">{{ template.description }}</small>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    {% if template.is_active %}
                                                        <span class="badge bg-success">活跃</span>
                                                    {% else %}
                                                        <span class="badge bg-secondary">禁用</span>
                                                    {% endif %}
                                                </td>
                                                <td>{{ template.usage_count }}</td>
                                                <td>{{ template.created_at[:19] if template.created_at else '-' }}</td>
                                                <td>
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <button type="button" class="btn btn-outline-info" onclick="previewTemplate({{ template.id }})">
                                                            <i class="bi bi-eye"></i> 预览
                                                        </button>
                                                        <button type="button" class="btn btn-outline-primary" onclick="editTemplate({{ template.id }})">
                                                            <i class="bi bi-pencil"></i> 编辑
                                                        </button>
                                                        {% if not template.is_default %}
                                                        <button type="button" class="btn btn-outline-success" onclick="setDefaultTemplate({{ template.id }})">
                                                            <i class="bi bi-star"></i> 设为默认
                                                        </button>
                                                        {% endif %}
                                                        {% if not template.is_default %}
                                                        <button type="button" class="btn btn-outline-danger" onclick="deleteTemplate({{ template.id }})">
                                                            <i class="bi bi-trash"></i> 删除
                                                        </button>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            {% else %}
                                <div class="text-center py-5">
                                    <i class="bi bi-file-text display-1 text-muted"></i>
                                    <h5 class="mt-3">暂无 {{ protocol_type.upper() }} 模板</h5>
                                    <p class="text-muted">点击上方按钮创建 {{ protocol_type.upper() }} 协议模板</p>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- 创建模板模态框 -->
<div class="modal fade" id="createTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建协议模板</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createTemplateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="templateName" class="form-label">模板名称 *</label>
                                <input type="text" class="form-control" id="templateName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="protocolType" class="form-label">协议类型 *</label>
                                <select class="form-select" id="protocolType" name="protocol_type" required>
                                    <option value="">请选择协议类型</option>
                                    <option value="vless">VLESS</option>
                                    <option value="vmess">VMess</option>
                                    <option value="trojan">Trojan</option>
                                    <option value="shadowsocks">Shadowsocks</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="templateDescription" class="form-label">模板描述</label>
                        <textarea class="form-control" id="templateDescription" name="description" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="templateContent" class="form-label">模板内容 *</label>
                        <textarea class="form-control font-monospace" id="templateContent" name="template_content" rows="6" required 
                                  placeholder="例如: vless://{uuid}@{server}:{port}?type={network}&security={security}&path={path}&host={host}#{remark}"></textarea>
                        <div class="form-text">
                            支持的变量：{server}, {port}, {uuid}, {email}, {path}, {host}, {sni}, {security}, {network}, {remark} 等
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="customVariables" class="form-label">自定义变量 (JSON格式)</label>
                        <textarea class="form-control font-monospace" id="customVariables" name="custom_variables" rows="3"
                                  placeholder='{"remark": "{region}-{provider}-{email}", "region": "HK", "provider": "CloudFlare"}'></textarea>
                        <div class="form-text">
                            定义自定义变量的默认值，JSON格式
                        </div>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-outline-info" onclick="previewCurrentTemplate()">
                            <i class="bi bi-eye me-2"></i>预览效果
                        </button>
                    </div>
                    <div id="previewResult" class="mb-3" style="display: none;">
                        <label class="form-label">预览结果：</label>
                        <div class="bg-light p-3 rounded">
                            <code id="previewContent"></code>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createTemplate()">创建模板</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑模板模态框 -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑协议模板</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editTemplateForm">
                    <input type="hidden" id="editTemplateId" name="template_id">
                    <!-- 表单内容与创建模板相同，但ID不同 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editTemplateName" class="form-label">模板名称 *</label>
                                <input type="text" class="form-control" id="editTemplateName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editProtocolType" class="form-label">协议类型 *</label>
                                <select class="form-select" id="editProtocolType" name="protocol_type" required>
                                    <option value="vless">VLESS</option>
                                    <option value="vmess">VMess</option>
                                    <option value="trojan">Trojan</option>
                                    <option value="shadowsocks">Shadowsocks</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editTemplateDescription" class="form-label">模板描述</label>
                        <textarea class="form-control" id="editTemplateDescription" name="description" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editTemplateContent" class="form-label">模板内容 *</label>
                        <textarea class="form-control font-monospace" id="editTemplateContent" name="template_content" rows="6" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="editCustomVariables" class="form-label">自定义变量 (JSON格式)</label>
                        <textarea class="form-control font-monospace" id="editCustomVariables" name="custom_variables" rows="3"></textarea>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="editIsActive" name="is_active">
                        <label class="form-check-label" for="editIsActive">启用模板</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateTemplate()">更新模板</button>
            </div>
        </div>
    </div>
</div>

<!-- 预览模板模态框 -->
<div class="modal fade" id="previewTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模板预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">模板内容：</label>
                    <div class="bg-light p-3 rounded">
                        <code id="previewTemplateContent"></code>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">预览结果：</label>
                    <div class="bg-light p-3 rounded">
                        <code id="previewTemplateResult"></code>
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">使用的变量：</label>
                    <div id="previewVariables"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<script>
// 创建协议模板
function createTemplate() {
    const form = document.getElementById('createTemplateForm');
    const formData = new FormData(form);

    // 处理自定义变量JSON
    const customVariablesText = formData.get('custom_variables');
    let customVariables = {};
    if (customVariablesText && customVariablesText.trim()) {
        try {
            customVariables = JSON.parse(customVariablesText);
        } catch (e) {
            showAlert('自定义变量JSON格式错误', 'error');
            return;
        }
    }

    const data = {
        name: formData.get('name'),
        protocol_type: formData.get('protocol_type'),
        template_content: formData.get('template_content'),
        description: formData.get('description'),
        custom_variables: customVariables
    };

    fetch('/admin/api/protocol-templates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('协议模板创建成功', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(result.message || '创建失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('创建失败', 'error');
    });
}

// 编辑协议模板
function editTemplate(templateId) {
    fetch(`/admin/api/protocol-templates/${templateId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const template = result.template;

            // 填充编辑表单
            document.getElementById('editTemplateId').value = template.id;
            document.getElementById('editTemplateName').value = template.name;
            document.getElementById('editProtocolType').value = template.protocol_type;
            document.getElementById('editTemplateDescription').value = template.description || '';
            document.getElementById('editTemplateContent').value = template.template_content;
            document.getElementById('editCustomVariables').value = JSON.stringify(template.custom_variables, null, 2);
            document.getElementById('editIsActive').checked = template.is_active;

            // 显示编辑模态框
            new bootstrap.Modal(document.getElementById('editTemplateModal')).show();
        } else {
            showAlert(result.message || '获取模板信息失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('获取模板信息失败', 'error');
    });
}

// 更新协议模板
function updateTemplate() {
    const form = document.getElementById('editTemplateForm');
    const formData = new FormData(form);
    const templateId = formData.get('template_id');

    // 处理自定义变量JSON
    const customVariablesText = formData.get('custom_variables');
    let customVariables = {};
    if (customVariablesText && customVariablesText.trim()) {
        try {
            customVariables = JSON.parse(customVariablesText);
        } catch (e) {
            showAlert('自定义变量JSON格式错误', 'error');
            return;
        }
    }

    const data = {
        name: formData.get('name'),
        protocol_type: formData.get('protocol_type'),
        template_content: formData.get('template_content'),
        description: formData.get('description'),
        custom_variables: customVariables,
        is_active: document.getElementById('editIsActive').checked
    };

    fetch(`/admin/api/protocol-templates/${templateId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('协议模板更新成功', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(result.message || '更新失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('更新失败', 'error');
    });
}

// 删除协议模板
function deleteTemplate(templateId) {
    if (!confirm('确定要删除这个协议模板吗？此操作不可恢复。')) {
        return;
    }

    fetch(`/admin/api/protocol-templates/${templateId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('协议模板删除成功', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(result.message || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('删除失败', 'error');
    });
}

// 设置默认模板
function setDefaultTemplate(templateId) {
    if (!confirm('确定要将此模板设为默认模板吗？')) {
        return;
    }

    fetch(`/admin/api/protocol-templates/${templateId}/set-default`, {
        method: 'POST'
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showAlert('默认模板设置成功', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showAlert(result.message || '设置失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('设置失败', 'error');
    });
}

// 预览当前编辑的模板
function previewCurrentTemplate() {
    const templateContent = document.getElementById('templateContent').value;
    const customVariablesText = document.getElementById('customVariables').value;

    if (!templateContent.trim()) {
        showAlert('请先输入模板内容', 'warning');
        return;
    }

    let customVariables = {};
    if (customVariablesText && customVariablesText.trim()) {
        try {
            customVariables = JSON.parse(customVariablesText);
        } catch (e) {
            showAlert('自定义变量JSON格式错误', 'error');
            return;
        }
    }

    const data = {
        template_content: templateContent,
        variables: customVariables
    };

    fetch('/admin/api/protocol-templates/preview', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            document.getElementById('previewContent').textContent = result.preview;
            document.getElementById('previewResult').style.display = 'block';
        } else {
            showAlert(result.message || '预览失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('预览失败', 'error');
    });
}

// 预览指定模板
function previewTemplate(templateId) {
    fetch(`/admin/api/protocol-templates/${templateId}`)
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            const template = result.template;

            // 显示模板内容
            document.getElementById('previewTemplateContent').textContent = template.template_content;

            // 预览模板效果
            const data = {
                template_content: template.template_content,
                variables: template.custom_variables
            };

            return fetch('/admin/api/protocol-templates/preview', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            });
        } else {
            throw new Error(result.message || '获取模板信息失败');
        }
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            document.getElementById('previewTemplateResult').textContent = result.preview;

            // 显示使用的变量
            const variablesHtml = result.variables_used.map(variable =>
                `<span class="badge bg-secondary me-1">{${variable}}</span>`
            ).join('');
            document.getElementById('previewVariables').innerHTML = variablesHtml;

            // 显示预览模态框
            new bootstrap.Modal(document.getElementById('previewTemplateModal')).show();
        } else {
            showAlert(result.message || '预览失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('预览失败', 'error');
    });
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertClass = type === 'error' ? 'alert-danger' :
                      type === 'success' ? 'alert-success' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    // 在页面顶部显示提示
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);

    // 3秒后自动消失
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, 3000);
}
</script>
{% endblock %}
