# 流量耗尽自动处理功能实现文档

## 📋 功能概述

本功能为调度器服务添加了自动检测和处理用户流量耗尽的能力。当用户订阅的流量用完后，系统将自动删除订阅和X-UI分组下对应的客户端节点。

## 🎯 实现目标

- **自动检测**: 定期检查所有活跃订阅的流量使用情况
- **自动处理**: 当流量耗尽时，自动删除订阅和X-UI客户端
- **完整清理**: 删除数据库记录、X-UI面板客户端和相关配置
- **日志记录**: 详细记录处理过程和结果
- **错误处理**: 优雅处理各种异常情况

## 🔧 技术实现

### 1. 核心服务扩展

#### ExpirationService 新增方法

**`get_traffic_exhausted_subscriptions()`**
- 获取所有流量耗尽的活跃订阅
- 判断标准：`remaining_mb <= 0.01`（考虑浮点数精度）
- 只检查活跃订阅（`is_active = True`）

**`_process_single_traffic_exhausted_subscription(subscription)`**
- 处理单个流量耗尽订阅
- 执行步骤：
  1. 从X-UI面板删除客户端
  2. 发送流量耗尽通知邮件
  3. 删除数据库记录

**`process_traffic_exhausted_subscriptions()`**
- 批量处理所有流量耗尽订阅
- 返回详细的处理统计信息
- 包含错误处理和事务管理

**`_log_traffic_exhausted_processing_results(stats)`**
- 记录处理结果的详细日志
- 统计成功/失败数量和错误信息

#### SchedulerService 新增功能

**定时任务**
- 任务ID: `traffic_exhausted_subscriptions_cleanup`
- 执行频率: 每30分钟一次
- 任务名称: "流量耗尽订阅清理任务"

**`_process_traffic_exhausted_subscriptions_job()`**
- 定时任务执行方法
- 在Flask应用上下文中运行
- 记录执行时间和结果统计

**`trigger_traffic_exhausted_cleanup()`**
- 手动触发流量耗尽清理
- 返回处理结果和统计信息
- 支持管理员手动操作

### 2. 流量检测逻辑

```python
# 流量耗尽判断标准
traffic_stats = subscription_service._get_order_traffic_stats(order)
if traffic_stats['remaining_mb'] <= 0.01:
    # 认为流量耗尽
```

**检测流程：**
1. 获取所有活跃订阅
2. 对每个订阅获取关联订单
3. 计算流量使用情况
4. 判断剩余流量是否 ≤ 0.01 MB

### 3. 删除处理流程

**步骤1: X-UI客户端删除**
- 复用现有的 `delete_subscription_clients_from_xui()` 方法
- 支持多面板环境
- 记录删除成功和失败的客户端

**步骤2: 通知发送**
- 发送流量耗尽通知邮件
- 复用现有的通知服务
- 错误不影响后续处理

**步骤3: 数据库清理**
- 复用现有的 `delete_subscription_and_order()` 方法
- 删除订阅、订单和关联的节点配置
- 清理流量统计记录

## 📊 处理统计信息

每次处理返回详细统计：

```python
{
    'start_time': '2025-06-06T17:46:44.658000+00:00',
    'end_time': '2025-06-06T17:46:46.518000+00:00',
    'duration_seconds': 1.86,
    'total_exhausted': 0,           # 发现的流量耗尽订阅数
    'successfully_deleted': 0,      # 成功删除的订阅数
    'failed_deletions': 0,          # 删除失败的订阅数
    'xui_deletion_failures': 0,     # X-UI删除失败数
    'db_deletion_failures': 0,      # 数据库删除失败数
    'errors': [],                   # 错误信息列表
    'processed_subscriptions': []   # 处理的订阅详情
}
```

## 🕐 调度配置

### 定时任务设置

- **执行频率**: 每30分钟
- **任务类型**: IntervalTrigger
- **错过处理**: 合并错过的任务
- **最大实例**: 1个
- **宽限时间**: 300秒

### 与其他任务的协调

- **流量统计收集**: 每5分钟（更频繁）
- **到期订阅清理**: 每1小时
- **流量耗尽清理**: 每30分钟（中等频率）

## 🧪 测试验证

### 测试脚本

提供了完整的测试脚本 `test_traffic_exhausted.py`，包含：

1. **流量耗尽检测测试**
2. **流量耗尽处理测试**
3. **调度器手动触发测试**
4. **调度器任务状态测试**

### 测试结果

```
=== 测试总结 ===
通过: 4/4
🎉 所有测试通过！
```

## 🔍 日志记录

### 处理过程日志

```
[INFO] 开始执行流量耗尽订阅处理任务...
[INFO] 找到 X 个流量耗尽的订阅
[INFO] 开始处理流量耗尽订阅 123 (订单: ORDER_456)
[INFO] 成功从X-UI面板删除 2 个客户端
[INFO] 成功删除订阅 123 的数据库记录
[INFO] 流量耗尽订阅处理任务完成 - 处理了 X 个流量耗尽订阅，成功删除 Y 个，失败 Z 个
```

### 统计结果日志

```
=== 流量耗尽订阅处理结果统计 ===
开始时间: 2025-06-06T17:46:44.658000+00:00
结束时间: 2025-06-06T17:46:46.518000+00:00
处理耗时: 1.86 秒
发现流量耗尽订阅: 0 个
成功删除: 0 个
删除失败: 0 个
=== 流量耗尽处理结果统计结束 ===
```

## 🚀 使用方法

### 自动运行

功能会在调度器启动后自动运行，无需手动干预。

### 手动触发

```python
from services.scheduler_service import scheduler_service

# 手动触发流量耗尽清理
result = scheduler_service.trigger_traffic_exhausted_cleanup()
print(f"处理结果: {result}")
```

### 检查任务状态

```python
status = scheduler_service.get_job_status()
for job in status['jobs']:
    if job['id'] == 'traffic_exhausted_subscriptions_cleanup':
        print(f"流量耗尽清理任务: {job}")
```

## ⚠️ 注意事项

1. **流量检测精度**: 使用0.01MB作为阈值，避免浮点数精度问题
2. **错误处理**: 即使X-UI删除失败，也会继续删除数据库记录
3. **事务管理**: 使用数据库事务确保数据一致性
4. **日志记录**: 详细记录所有操作，便于问题排查
5. **通知服务**: 当前复用到期通知，后续可扩展专门的流量耗尽通知

## 🔄 与现有功能的集成

- **复用删除逻辑**: 与到期订阅处理使用相同的删除方法
- **复用流量统计**: 使用现有的流量统计服务获取数据
- **复用通知服务**: 使用现有的邮件通知功能
- **复用调度框架**: 集成到现有的定时任务系统

## 📈 性能考虑

- **查询优化**: 只查询活跃订阅，减少数据库负载
- **批量处理**: 一次性处理所有流量耗尽订阅
- **错误隔离**: 单个订阅处理失败不影响其他订阅
- **资源管理**: 合理的执行频率，避免系统负载过高
