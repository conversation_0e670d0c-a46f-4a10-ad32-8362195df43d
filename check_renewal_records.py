#!/usr/bin/env python3
"""
检查续费记录
"""
import sqlite3

def check_renewal_records():
    try:
        conn = sqlite3.connect('instance/node_sales.db')
        cursor = conn.cursor()
        
        # 查询续费订单
        cursor.execute('SELECT order_id, order_type, price, duration_days, parent_subscription_id, status FROM orders WHERE order_type="renewal"')
        renewal_orders = cursor.fetchall()
        
        print("续费订单:")
        for order in renewal_orders:
            print(f"  订单ID: {order[0]}, 类型: {order[1]}, 价格: ¥{order[2]}, 时长: {order[3]}天, 原订阅: {order[4]}, 状态: {order[5]}")
        
        # 查询续费任务
        cursor.execute('SELECT id, subscription_id, renewal_months, status, error_message FROM renewal_tasks')
        renewal_tasks = cursor.fetchall()
        
        print("\n续费任务:")
        for task in renewal_tasks:
            print(f"  任务ID: {task[0]}, 订阅ID: {task[1]}, 续费月数: {task[2]}, 状态: {task[3]}, 错误: {task[4] or '无'}")
        
        # 查询订阅到期时间更新
        cursor.execute('SELECT id, expires_at FROM subscriptions WHERE id=6')
        subscription = cursor.fetchone()
        
        print(f"\n订阅更新:")
        if subscription:
            print(f"  订阅ID: {subscription[0]}, 新到期时间: {subscription[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == "__main__":
    check_renewal_records()
