"""
数据库初始化脚本
"""
from app import create_app
from models import db
from services.config_service import config_service

def init_database():
    """初始化数据库"""
    app = create_app()

    with app.app_context():
        # 删除所有表（谨慎使用）
        # db.drop_all()

        # 创建所有表
        db.create_all()

        print("数据库初始化完成！")
        print("创建的表:")
        print("- orders (订单表)")
        print("- node_configs (节点配置表)")
        print("- users (用户表)")
        print("- xui_panels (X-UI面板表)")
        print("- xui_panel_groups (X-UI面板分组表)")
        print("- xui_panel_group_memberships (面板分组成员关系表)")
        print("- products (产品表)")
        print("- subscriptions (订阅表)")
        print("- traffic_stats (流量统计表)")
        print("- email_configs (邮件配置表)")
        print("- verification_codes (验证码表)")

        # 尝试从config.py迁移面板配置
        print("\n正在检查配置迁移...")
        try:
            if hasattr(config_service, 'migrate_config_to_database'):
                if config_service.migrate_config_to_database():
                    print("✓ 配置迁移完成")
                else:
                    print("✗ 配置迁移失败")
            else:
                print("! 配置迁移方法不存在，跳过")
        except Exception as e:
            print(f"✗ 配置迁移失败: {e}")

        # 创建默认邮件配置（如果不存在）
        print("\n正在检查邮件配置...")
        try:
            from models import EmailConfig
            import os

            # 检查是否已有邮件配置
            existing_config = EmailConfig.query.first()
            if not existing_config:
                # 从环境变量创建默认配置
                smtp_server = os.environ.get('MAIL_SERVER')
                smtp_username = os.environ.get('MAIL_USERNAME')
                smtp_password = os.environ.get('MAIL_PASSWORD')

                if smtp_server and smtp_username and smtp_password:
                    default_config = EmailConfig(
                        name='默认邮件配置',
                        smtp_server=smtp_server,
                        smtp_port=int(os.environ.get('MAIL_PORT', '587')),
                        use_tls=os.environ.get('MAIL_USE_TLS', 'true').lower() == 'true',
                        use_ssl=os.environ.get('MAIL_USE_SSL', 'false').lower() == 'true',
                        username=smtp_username,
                        password=smtp_password,
                        default_sender=os.environ.get('MAIL_DEFAULT_SENDER', smtp_username),
                        sender_name=os.environ.get('MAIL_SENDER_NAME', '系统通知'),
                        is_active=True,
                        is_default=True
                    )

                    db.session.add(default_config)
                    db.session.commit()
                    print("✓ 默认邮件配置创建成功")
                else:
                    print("! 未找到邮件配置环境变量，请手动配置")
            else:
                print("✓ 邮件配置已存在")

        except Exception as e:
            print(f"✗ 邮件配置检查失败: {e}")

if __name__ == '__main__':
    init_database()
