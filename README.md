# 节点销售和管理系统

基于Flask的节点销售和管理系统，支持与X-UI面板集成，实现自动化的节点分配和管理。

## 功能特性

- 🛒 **订单管理**: 创建、查询、处理和取消订单
- 🔗 **X-UI集成**: 自动在X-UI面板中创建和管理客户端
- 📧 **邮件通知**: 自动发送订单确认和节点配置邮件
- 📊 **流量监控**: 实时监控和更新客户端流量使用情况
- 🔍 **配置查询**: 根据备注名快速获取VLESS配置
- 💾 **数据持久化**: 使用SQLite数据库存储订单和配置信息
- ⚙️ **动态配置**: 支持通过管理界面动态管理X-UI面板配置
- 🔄 **负载均衡**: 支持多面板负载均衡和故障转移

## 项目结构

```
xui/
├── app.py                 # Flask主应用
├── config.py             # 系统配置文件
├── xui_client.py        # X-UI API客户端
├── init_db.py           # 数据库初始化脚本
├── multi_xui_manager.py # 多面板管理器
├── test_api.py          # API测试脚本
├── requirements.txt     # 依赖包
├── .env.example         # 环境变量示例
├── models/
│   └── database.py      # 数据库模型
├── routes/
│   ├── auth.py          # 认证路由
│   ├── admin.py         # 管理员路由
│   └── shop.py          # 商店路由
├── services/
│   └── config_service.py # 配置服务
├── scripts/
│   └── migrate_config.py # 配置迁移脚本
└── utils/
    ├── email_service.py # 邮件发送服务
    └── order_service.py # 订单管理服务
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并修改配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下信息：
- X-UI面板地址和登录凭据
- 邮件服务器配置
- 数据库连接（可选，默认使用SQLite）

### 3. 初始化数据库

```bash
python init_db.py
```

数据库初始化会自动执行以下操作：
- 创建所有必要的数据表
- 自动从config.py迁移X-UI面板配置到数据库（如果存在）

### 4. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:5000` 启动。

### 5. 测试API

```bash
python test_api.py
```

## 配置管理

### X-UI面板配置

从v2.0开始，X-UI面板配置已从config.py迁移到数据库，支持动态管理：

#### 1. 通过管理界面管理面板

访问 `http://localhost:5000/admin/panels` 可以：
- 添加新的X-UI面板
- 编辑现有面板配置
- 启用/禁用面板
- 设置面板优先级和负载均衡

#### 2. 通过配置迁移脚本

```bash
# 查看当前配置状态
python scripts/migrate_config.py status

# 执行配置迁移（从config.py到数据库）
python scripts/migrate_config.py migrate

# 回滚迁移（删除数据库中的面板配置）
python scripts/migrate_config.py rollback
```

#### 3. 配置优先级

系统按以下优先级读取配置：
1. **数据库配置**（优先）- 通过管理界面或API管理
2. **环境变量回退** - 当数据库为空时使用
3. **默认配置** - 最后的回退选项

#### 4. 负载均衡配置

支持多种负载均衡策略：
- `round_robin`: 轮询（默认）
- `least_clients`: 最少客户端
- `priority`: 按优先级
- `random`: 随机选择

在config.py中配置：
```python
XUI_LOAD_BALANCE_STRATEGY = 'round_robin'
XUI_AUTO_FAILOVER = True
```

## API接口

### 1. 创建订单
```
POST /api/orders/create
```

请求体：
```json
{
  "customer_email": "<EMAIL>",
  "customer_name": "张三",
  "node_type": "vless",
  "duration_days": 30,
  "traffic_limit_gb": 100,
  "price": 29.9,
  "payment_method": "alipay",
  "payment_id": "2024010112345678",
  "customer_remarks": "user001"
}
```

### 2. 处理订单
```
POST /api/orders/process
```

请求体：
```json
{
  "order_id": "ORD20240101120000ABC123",
  "inbound_id": 1
}
```

### 3. 查询订单
```
GET /api/orders/{order_id}
```

### 4. 根据邮箱查询订单
```
GET /api/orders/by-email/{email}
```

### 5. 根据备注名获取配置
```
GET /api/orders/config/{remarks}
```

响应示例：
```json
{
  "success": true,
  "data": {
    "vless_config": "vless://uuid@server:port?type=tcp&security=none#remarks",
    "client_email": "user001",
    "server_address": "************",
    "server_port": 443,
    "protocol": "vless",
    "is_active": true,
    "traffic_stats": {
      "total_gb": 5.2,
      "upload_gb": 1.1,
      "download_gb": 4.1,
      "last_update": "2024-01-01T12:00:00"
    }
  }
}
```

### 6. 更新流量统计
```
POST /api/orders/traffic/{client_email}
```

### 7. 取消订单
```
POST /api/orders/{order_id}/cancel
```

## 配置说明

### X-UI面板配置
- `XUI_BASE_URL`: X-UI面板地址（如：http://************:54321）
- `XUI_USERNAME`: 登录用户名
- `XUI_PASSWORD`: 登录密码

**支持的X-UI版本**：
- 本系统专为 [MHSanaei/3x-ui](https://github.com/MHSanaei/3x-ui) v2.6.0+ 设计
- 兼容3x-ui的addClient API（`/panel/inbound/addClient`）
- 自动处理v2.6.0已知的空响应问题

### 邮件服务配置
- `MAIL_SERVER`: SMTP服务器地址
- `MAIL_PORT`: SMTP端口
- `MAIL_USERNAME`: 邮箱用户名
- `MAIL_PASSWORD`: 邮箱密码（建议使用应用专用密码）

## 使用流程

1. **创建订单**: 客户购买后，调用创建订单API
2. **处理订单**: 系统自动或手动调用处理订单API，分配节点
3. **发送配置**: 系统自动发送节点配置邮件给客户
4. **查询配置**: 客户可通过备注名查询配置
5. **监控流量**: 定期更新和监控流量使用情况

## 注意事项

- 确保X-UI面板正常运行且可访问
- 配置正确的邮件服务器信息
- 定期备份数据库文件
- 生产环境建议使用PostgreSQL或MySQL数据库
- 建议配置HTTPS和防火墙规则

## 许可证

MIT License
