"""
添加续费相关表的数据库迁移脚本
"""
import sqlite3
import logging

logger = logging.getLogger(__name__)


def upgrade_database(db_path='node_sales.db'):
    """升级数据库，添加续费相关表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 创建续费价格配置表
        logger.info("创建续费价格配置表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS renewal_pricing (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                duration_months INTEGER NOT NULL UNIQUE,
                discount_percentage REAL NOT NULL DEFAULT 0.0,
                is_active BOOLEAN NOT NULL DEFAULT 1,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 2. 创建续费任务跟踪表
        logger.info("创建续费任务跟踪表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS renewal_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                subscription_id INTEGER NOT NULL,
                order_id INTEGER,
                renewal_months INTEGER NOT NULL,
                original_expiry DATETIME NOT NULL,
                new_expiry DATETIME NOT NULL,
                status VARCHAR(20) NOT NULL DEFAULT 'pending',
                panel_update_status TEXT,
                error_message TEXT,
                retry_count INTEGER NOT NULL DEFAULT 0,
                max_retries INTEGER NOT NULL DEFAULT 3,
                created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                FOREIGN KEY (subscription_id) REFERENCES subscriptions (id),
                FOREIGN KEY (order_id) REFERENCES orders (id)
            )
        """)
        
        # 3. 为orders表添加续费相关字段
        logger.info("为orders表添加续费相关字段...")
        
        # 检查order_type字段是否存在
        cursor.execute("PRAGMA table_info(orders)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'order_type' not in columns:
            cursor.execute("ALTER TABLE orders ADD COLUMN order_type VARCHAR(20) NOT NULL DEFAULT 'purchase'")
            logger.info("添加order_type字段")
        
        if 'parent_subscription_id' not in columns:
            cursor.execute("ALTER TABLE orders ADD COLUMN parent_subscription_id INTEGER")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_parent_subscription ON orders(parent_subscription_id)")
            logger.info("添加parent_subscription_id字段")
        
        # 4. 插入默认的续费价格配置
        logger.info("插入默认续费价格配置...")
        default_pricing = [
            (3, 5.0, 1),   # 3个月，5%折扣
            (6, 10.0, 1),  # 6个月，10%折扣
            (12, 20.0, 1), # 12个月，20%折扣
        ]
        
        for duration, discount, is_active in default_pricing:
            cursor.execute("""
                INSERT OR IGNORE INTO renewal_pricing 
                (duration_months, discount_percentage, is_active)
                VALUES (?, ?, ?)
            """, (duration, discount, is_active))
        
        # 5. 创建索引
        logger.info("创建索引...")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_renewal_pricing_duration ON renewal_pricing(duration_months)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_renewal_pricing_active ON renewal_pricing(is_active)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_renewal_tasks_subscription ON renewal_tasks(subscription_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_renewal_tasks_status ON renewal_tasks(status)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_orders_type ON orders(order_type)")
        
        conn.commit()
        logger.info("续费相关表创建完成")
        return True
        
    except Exception as e:
        logger.error(f"创建续费相关表失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


def downgrade_database(db_path='node_sales.db'):
    """降级数据库，删除续费相关表"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        logger.info("删除续费相关表...")
        
        # 删除表
        cursor.execute("DROP TABLE IF EXISTS renewal_tasks")
        cursor.execute("DROP TABLE IF EXISTS renewal_pricing")
        
        # 删除orders表的续费字段（SQLite不支持DROP COLUMN，需要重建表）
        logger.warning("SQLite不支持删除列，续费相关字段将保留在orders表中")
        
        conn.commit()
        logger.info("续费相关表删除完成")
        return True
        
    except Exception as e:
        logger.error(f"删除续费相关表失败: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)

    # 执行升级 - 更新instance目录中的数据库
    success = upgrade_database('instance/node_sales.db')
    if success:
        print("数据库升级成功")
    else:
        print("数据库升级失败")
