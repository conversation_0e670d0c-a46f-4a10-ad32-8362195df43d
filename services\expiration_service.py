"""
订阅到期处理服务 - 处理到期订阅的清理工作
"""
import logging
from typing import Dict, List, Tuple
from datetime import datetime, timezone
from models import db, Subscription, Order
from services.subscription_service import SubscriptionService
from services.notification_service import notification_service

logger = logging.getLogger(__name__)

class ExpirationService:
    """订阅到期处理服务类"""
    
    def __init__(self):
        self.subscription_service = SubscriptionService()
    
    def process_expired_subscriptions(self) -> Dict:
        """
        处理所有到期的订阅
        
        Returns:
            Dict: 处理结果统计
        """
        start_time = datetime.now(timezone.utc)
        logger.info("开始处理到期订阅...")
        
        # 初始化统计信息
        stats = {
            'start_time': start_time.isoformat(),
            'total_expired': 0,
            'successfully_deleted': 0,
            'failed_deletions': 0,
            'xui_deletion_failures': 0,
            'db_deletion_failures': 0,
            'processed_subscriptions': [],
            'errors': []
        }
        
        try:
            # 查找所有到期的活跃订阅
            expired_subscriptions = self._find_expired_subscriptions()
            stats['total_expired'] = len(expired_subscriptions)
            
            if not expired_subscriptions:
                logger.info("没有找到到期的订阅")
                stats['end_time'] = datetime.now(timezone.utc).isoformat()
                return stats
            
            logger.info(f"找到 {len(expired_subscriptions)} 个到期订阅")
            
            # 处理每个到期订阅
            for subscription in expired_subscriptions:
                result = self._process_single_expired_subscription(subscription)
                stats['processed_subscriptions'].append(result)
                
                if result['success']:
                    stats['successfully_deleted'] += 1
                else:
                    stats['failed_deletions'] += 1
                    if result.get('xui_deletion_failed'):
                        stats['xui_deletion_failures'] += 1
                    if result.get('db_deletion_failed'):
                        stats['db_deletion_failures'] += 1
                    
                    if result.get('error'):
                        stats['errors'].append(result['error'])
            
            # 提交所有更改
            db.session.commit()
            
        except Exception as e:
            logger.error(f"处理到期订阅时发生错误: {e}")
            db.session.rollback()
            stats['errors'].append(f"整体处理失败: {str(e)}")
        
        end_time = datetime.now(timezone.utc)
        stats['end_time'] = end_time.isoformat()
        stats['duration_seconds'] = (end_time - start_time).total_seconds()
        
        # 记录处理结果
        self._log_processing_results(stats)
        
        return stats
    
    def _find_expired_subscriptions(self) -> List[Subscription]:
        """
        查找所有到期的活跃订阅
        
        Returns:
            List[Subscription]: 到期的订阅列表
        """
        current_time = datetime.now(timezone.utc)
        
        expired_subscriptions = Subscription.query.filter(
            Subscription.is_active == True,
            Subscription.expires_at.isnot(None),
            Subscription.expires_at < current_time
        ).all()
        
        return expired_subscriptions
    
    def _process_single_expired_subscription(self, subscription: Subscription) -> Dict:
        """
        处理单个到期订阅
        
        Args:
            subscription: 到期的订阅对象
            
        Returns:
            Dict: 处理结果
        """
        result = {
            'subscription_id': subscription.id,
            'order_id': subscription.order_id,
            'order_number': subscription.order.order_id if subscription.order else None,
            'customer_email': subscription.order.customer_email if subscription.order else None,
            'expires_at': subscription.expires_at.isoformat() if subscription.expires_at else None,
            'success': False,
            'xui_deletion_success': False,
            'db_deletion_success': False,
            'xui_deletion_failed': False,
            'db_deletion_failed': False,
            'deleted_clients': [],
            'failed_client_deletions': [],
            'error': None
        }
        
        try:
            logger.info(f"开始处理到期订阅 {subscription.id} (订单: {result['order_number']})")
            
            # Step 1: 从X-UI面板删除客户端
            try:
                deleted_clients, failed_deletions = self.subscription_service.delete_subscription_clients_from_xui(subscription)
                result['deleted_clients'] = deleted_clients
                result['failed_client_deletions'] = failed_deletions
                result['xui_deletion_success'] = True
                
                if deleted_clients:
                    logger.info(f"成功从X-UI面板删除 {len(deleted_clients)} 个客户端")
                if failed_deletions:
                    logger.warning(f"删除失败的客户端: {failed_deletions}")
                    result['xui_deletion_failed'] = True
                    
            except Exception as e:
                logger.error(f"从X-UI面板删除客户端失败: {e}")
                result['xui_deletion_failed'] = True
                result['error'] = f"X-UI删除失败: {str(e)}"
            
            # Step 2: 发送到期通知邮件
            try:
                notification_sent = notification_service.send_subscription_expiry_notification(subscription.id)
                if notification_sent:
                    logger.info(f"成功发送订阅到期通知邮件: 订阅 {subscription.id}")
                else:
                    logger.warning(f"发送订阅到期通知邮件失败: 订阅 {subscription.id}")
            except Exception as e:
                logger.error(f"发送订阅到期通知邮件时发生错误: {e}")

            # Step 3: 从数据库删除订阅和订单
            try:
                self.subscription_service.delete_subscription_and_order(subscription)
                result['db_deletion_success'] = True
                logger.info(f"成功删除订阅 {subscription.id} 的数据库记录")

            except Exception as e:
                logger.error(f"删除数据库记录失败: {e}")
                result['db_deletion_failed'] = True
                if result['error']:
                    result['error'] += f"; 数据库删除失败: {str(e)}"
                else:
                    result['error'] = f"数据库删除失败: {str(e)}"
            
            # 判断整体是否成功（至少数据库删除成功）
            result['success'] = result['db_deletion_success']
            
            if result['success']:
                logger.info(f"成功处理到期订阅 {subscription.id}")
            else:
                logger.error(f"处理到期订阅 {subscription.id} 失败")
                
        except Exception as e:
            logger.error(f"处理到期订阅 {subscription.id} 时发生未预期错误: {e}")
            result['error'] = f"未预期错误: {str(e)}"
            
        return result
    
    def _log_processing_results(self, stats: Dict):
        """记录处理结果日志"""
        logger.info("=== 到期订阅处理结果统计 ===")
        logger.info(f"开始时间: {stats['start_time']}")
        logger.info(f"结束时间: {stats['end_time']}")
        logger.info(f"处理耗时: {stats.get('duration_seconds', 0):.2f} 秒")
        logger.info(f"发现到期订阅: {stats['total_expired']} 个")
        logger.info(f"成功删除: {stats['successfully_deleted']} 个")
        logger.info(f"删除失败: {stats['failed_deletions']} 个")
        
        if stats['xui_deletion_failures'] > 0:
            logger.warning(f"X-UI面板删除失败: {stats['xui_deletion_failures']} 个")
        
        if stats['db_deletion_failures'] > 0:
            logger.error(f"数据库删除失败: {stats['db_deletion_failures']} 个")
        
        if stats['errors']:
            logger.error("处理过程中的错误:")
            for error in stats['errors']:
                logger.error(f"  - {error}")
        
        logger.info("=== 处理结果统计结束 ===")
    
    def get_expiring_soon_subscriptions(self, days: int = 7) -> List[Dict]:
        """
        获取即将到期的订阅列表（用于提醒功能）
        
        Args:
            days: 多少天内到期
            
        Returns:
            List[Dict]: 即将到期的订阅信息
        """
        from datetime import timedelta
        
        current_time = datetime.now(timezone.utc)
        expiry_threshold = current_time + timedelta(days=days)
        
        expiring_subscriptions = Subscription.query.filter(
            Subscription.is_active == True,
            Subscription.expires_at.isnot(None),
            Subscription.expires_at > current_time,
            Subscription.expires_at <= expiry_threshold
        ).all()
        
        result = []
        for subscription in expiring_subscriptions:
            # 处理时区问题
            if subscription.expires_at:
                if subscription.expires_at.tzinfo is None:
                    expires_at = subscription.expires_at.replace(tzinfo=timezone.utc)
                else:
                    expires_at = subscription.expires_at
                days_until_expiry = (expires_at - current_time).days
            else:
                expires_at = None
                days_until_expiry = None
                
            result.append({
                'subscription_id': subscription.id,
                'order_id': subscription.order_id,
                'order_number': subscription.order.order_id if subscription.order else None,
                'customer_email': subscription.order.customer_email if subscription.order else None,
                'expires_at': expires_at.isoformat() if expires_at else None,
                'days_until_expiry': days_until_expiry
            })
        
        return result

    def check_and_notify_expiring_subscriptions(self, warning_days: List[int] = [7, 3, 1]) -> Dict:
        """
        检查即将到期的订阅并发送警告通知

        Args:
            warning_days: 提前警告的天数列表

        Returns:
            Dict: 处理结果统计
        """
        try:
            logger.info("开始检查即将到期的订阅...")

            results = notification_service.check_expiring_subscriptions(warning_days)

            logger.info(f"即将到期订阅检查完成: 检查 {results['total_checked']} 个订阅, "
                       f"发送 {results['warnings_sent']} 个警告, "
                       f"发送 {results['expiry_notifications_sent']} 个到期通知")

            return results

        except Exception as e:
            logger.error(f"检查即将到期订阅失败: {e}")
            return {
                'total_checked': 0,
                'warnings_sent': 0,
                'expiry_notifications_sent': 0,
                'errors': [str(e)]
            }

    def get_traffic_exhausted_subscriptions(self) -> List[Subscription]:
        """
        获取流量已耗尽的活跃订阅列表

        Returns:
            List[Subscription]: 流量耗尽的订阅列表
        """
        try:
            # 获取所有活跃订阅
            active_subscriptions = Subscription.query.filter(
                Subscription.is_active == True
            ).all()

            traffic_exhausted_subscriptions = []

            for subscription in active_subscriptions:
                try:
                    # 获取订阅关联的订单
                    order = subscription.order
                    if not order:
                        continue

                    # 获取流量统计
                    traffic_stats = self.subscription_service._get_order_traffic_stats(order)

                    # 检查流量是否耗尽（剩余流量 <= 0.01 MB，考虑浮点数精度）
                    if traffic_stats['remaining_mb'] <= 0.01:
                        traffic_exhausted_subscriptions.append(subscription)
                        logger.info(f"发现流量耗尽订阅: {subscription.id} (订单: {order.order_id}), "
                                   f"已用流量: {traffic_stats['total_traffic_mb']} MB, "
                                   f"流量限制: {traffic_stats['traffic_limit_mb']} MB")

                except Exception as e:
                    logger.error(f"检查订阅 {subscription.id} 流量状态失败: {e}")
                    continue

            logger.info(f"找到 {len(traffic_exhausted_subscriptions)} 个流量耗尽的订阅")
            return traffic_exhausted_subscriptions

        except Exception as e:
            logger.error(f"获取流量耗尽订阅列表失败: {e}")
            return []

    def _process_single_traffic_exhausted_subscription(self, subscription: Subscription) -> Dict:
        """
        处理单个流量耗尽的订阅

        Args:
            subscription: 要处理的订阅对象

        Returns:
            Dict: 处理结果
        """
        result = {
            'subscription_id': subscription.id,
            'order_number': subscription.order.order_id if subscription.order else 'Unknown',
            'success': False,
            'deleted_clients': [],
            'failed_client_deletions': [],
            'xui_deletion_success': False,
            'xui_deletion_failed': False,
            'db_deletion_success': False,
            'db_deletion_failed': False,
            'error': None
        }

        try:
            logger.info(f"开始处理流量耗尽订阅 {subscription.id} (订单: {result['order_number']})")

            # Step 1: 从X-UI面板删除客户端
            try:
                deleted_clients, failed_deletions = self.subscription_service.delete_subscription_clients_from_xui(subscription)
                result['deleted_clients'] = deleted_clients
                result['failed_client_deletions'] = failed_deletions
                result['xui_deletion_success'] = True

                if deleted_clients:
                    logger.info(f"成功从X-UI面板删除 {len(deleted_clients)} 个客户端")
                if failed_deletions:
                    logger.warning(f"删除失败的客户端: {failed_deletions}")
                    result['xui_deletion_failed'] = True

            except Exception as e:
                logger.error(f"从X-UI面板删除客户端失败: {e}")
                result['xui_deletion_failed'] = True
                result['error'] = f"X-UI删除失败: {str(e)}"

            # Step 2: 发送流量耗尽通知邮件
            try:
                # 注意：这里需要确认NotificationService是否有流量耗尽通知方法
                # 如果没有，可以复用到期通知或者后续添加专门的流量耗尽通知
                notification_sent = notification_service.send_subscription_expiry_notification(subscription.id)
                if notification_sent:
                    logger.info(f"成功发送流量耗尽通知邮件: 订阅 {subscription.id}")
                else:
                    logger.warning(f"发送流量耗尽通知邮件失败: 订阅 {subscription.id}")
            except Exception as e:
                logger.error(f"发送流量耗尽通知邮件时发生错误: {e}")

            # Step 3: 从数据库删除订阅和订单
            try:
                self.subscription_service.delete_subscription_and_order(subscription)
                result['db_deletion_success'] = True
                logger.info(f"成功删除订阅 {subscription.id} 的数据库记录")

            except Exception as e:
                logger.error(f"删除数据库记录失败: {e}")
                result['db_deletion_failed'] = True
                if result['error']:
                    result['error'] += f"; 数据库删除失败: {str(e)}"
                else:
                    result['error'] = f"数据库删除失败: {str(e)}"

            # 判断整体是否成功（至少数据库删除成功）
            result['success'] = result['db_deletion_success']

            if result['success']:
                logger.info(f"成功处理流量耗尽订阅 {subscription.id}")
            else:
                logger.error(f"处理流量耗尽订阅 {subscription.id} 失败")

        except Exception as e:
            logger.error(f"处理流量耗尽订阅 {subscription.id} 时发生未预期错误: {e}")
            result['error'] = f"未预期错误: {str(e)}"

        return result

    def process_traffic_exhausted_subscriptions(self) -> Dict:
        """
        处理所有流量耗尽的订阅

        Returns:
            Dict: 处理结果统计
        """
        start_time = datetime.now(timezone.utc)

        stats = {
            'start_time': start_time.isoformat(),
            'end_time': None,
            'duration_seconds': 0,
            'total_exhausted': 0,
            'successfully_deleted': 0,
            'failed_deletions': 0,
            'xui_deletion_failures': 0,
            'db_deletion_failures': 0,
            'errors': [],
            'processed_subscriptions': []
        }

        try:
            logger.info("开始处理流量耗尽订阅...")

            # 获取所有流量耗尽的订阅
            exhausted_subscriptions = self.get_traffic_exhausted_subscriptions()
            stats['total_exhausted'] = len(exhausted_subscriptions)

            if stats['total_exhausted'] == 0:
                logger.info("没有发现流量耗尽的订阅")
                return stats

            logger.info(f"发现 {stats['total_exhausted']} 个流量耗尽的订阅，开始处理...")

            # 处理每个流量耗尽的订阅
            for subscription in exhausted_subscriptions:
                try:
                    result = self._process_single_traffic_exhausted_subscription(subscription)
                    stats['processed_subscriptions'].append(result)

                    if result['success']:
                        stats['successfully_deleted'] += 1
                    else:
                        stats['failed_deletions'] += 1
                        if result['error']:
                            stats['errors'].append(f"订阅 {subscription.id}: {result['error']}")

                    if result['xui_deletion_failed']:
                        stats['xui_deletion_failures'] += 1

                    if result['db_deletion_failed']:
                        stats['db_deletion_failures'] += 1

                except Exception as e:
                    logger.error(f"处理流量耗尽订阅 {subscription.id} 时发生错误: {e}")
                    stats['failed_deletions'] += 1
                    stats['errors'].append(f"订阅 {subscription.id}: 处理异常 - {str(e)}")

            # 提交数据库事务
            try:
                db.session.commit()
                logger.info("流量耗尽订阅处理事务提交成功")
            except Exception as e:
                logger.error(f"提交流量耗尽订阅处理事务失败: {e}")
                db.session.rollback()
                stats['errors'].append(f"事务提交失败: {str(e)}")

        except Exception as e:
            logger.error(f"处理流量耗尽订阅时发生未预期错误: {e}")
            stats['errors'].append(f"未预期错误: {str(e)}")
            try:
                db.session.rollback()
            except:
                pass

        finally:
            end_time = datetime.now(timezone.utc)
            stats['end_time'] = end_time.isoformat()
            stats['duration_seconds'] = (end_time - start_time).total_seconds()

            # 记录处理结果
            self._log_traffic_exhausted_processing_results(stats)

        return stats

    def _log_traffic_exhausted_processing_results(self, stats: Dict):
        """记录流量耗尽处理结果日志"""
        logger.info("=== 流量耗尽订阅处理结果统计 ===")
        logger.info(f"开始时间: {stats['start_time']}")
        logger.info(f"结束时间: {stats['end_time']}")
        logger.info(f"处理耗时: {stats.get('duration_seconds', 0):.2f} 秒")
        logger.info(f"发现流量耗尽订阅: {stats['total_exhausted']} 个")
        logger.info(f"成功删除: {stats['successfully_deleted']} 个")
        logger.info(f"删除失败: {stats['failed_deletions']} 个")

        if stats['xui_deletion_failures'] > 0:
            logger.warning(f"X-UI面板删除失败: {stats['xui_deletion_failures']} 个")

        if stats['db_deletion_failures'] > 0:
            logger.error(f"数据库删除失败: {stats['db_deletion_failures']} 个")

        if stats['errors']:
            logger.error("处理过程中的错误:")
            for error in stats['errors']:
                logger.error(f"  - {error}")

        logger.info("=== 流量耗尽处理结果统计结束 ===")

    def force_delete_subscription(self, subscription_id: int) -> Dict:
        """
        强制删除指定订阅（管理员操作）

        Args:
            subscription_id: 订阅ID

        Returns:
            Dict: 删除结果
        """
        try:
            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                return {
                    'success': False,
                    'error': f"订阅 {subscription_id} 不存在"
                }

            logger.info(f"管理员强制删除订阅 {subscription_id}")
            result = self._process_single_expired_subscription(subscription)

            if result['success']:
                db.session.commit()
                logger.info(f"成功强制删除订阅 {subscription_id}")
            else:
                db.session.rollback()
                logger.error(f"强制删除订阅 {subscription_id} 失败")

            return result

        except Exception as e:
            logger.error(f"强制删除订阅 {subscription_id} 时发生错误: {e}")
            db.session.rollback()
            return {
                'success': False,
                'error': f"强制删除失败: {str(e)}"
            }

# 全局过期处理服务实例
expiration_service = ExpirationService() 