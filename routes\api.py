"""
API路由 - 提供RESTful API接口
"""
from flask import Blueprint, request, jsonify
from models import db, Product, Order, OrderStatus
from utils.order_service import OrderService
import logging

logger = logging.getLogger(__name__)

api_bp = Blueprint('api', __name__)

# ==================== 产品API ====================

@api_bp.route('/products')
def get_products():
    """获取产品列表"""
    try:
        products = Product.query.filter_by(is_active=True).all()
        return jsonify([product.to_dict() for product in products])
    except Exception as e:
        logger.error(f"获取产品列表失败: {e}")
        return jsonify({'error': '获取产品列表失败'}), 500

@api_bp.route('/products/<int:product_id>')
def get_product(product_id):
    """获取单个产品详情"""
    try:
        product = Product.query.get_or_404(product_id)
        if not product.is_active:
            return jsonify({'error': '产品已下架'}), 404
        return jsonify(product.to_dict())
    except Exception as e:
        logger.error(f"获取产品详情失败: {e}")
        return jsonify({'error': '获取产品详情失败'}), 500

# ==================== 订单API ====================

@api_bp.route('/orders', methods=['POST'])
def create_order():
    """创建订单"""
    try:
        data = request.get_json()
        
        # 验证必需字段
        required_fields = ['customer_email', 'customer_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'缺少必需字段: {field}'}), 400
        
        # 获取产品信息（如果指定了产品ID）
        product = None
        if data.get('product_id'):
            product = Product.query.get(data['product_id'])
            if not product or not product.is_active:
                return jsonify({'error': '产品不存在或已下架'}), 400
        
        # 创建订单服务
        order_service = OrderService()
        
        # 从产品或请求数据中获取订单参数
        if product:
            node_type = product.node_type.value
            duration_days = product.duration_days
            traffic_limit_gb = product.traffic_limit_gb
            price = product.price
        else:
            node_type = data.get('node_type', 'vless')
            duration_days = data.get('duration_days', 30)
            traffic_limit_gb = data.get('traffic_limit_gb', 100)
            price = data.get('price', 0)
        
        # 创建订单
        success, order = order_service.create_order(
            customer_email=data['customer_email'],
            customer_name=data['customer_name'],
            node_type=node_type,
            duration_days=duration_days,
            traffic_limit_gb=traffic_limit_gb,
            price=price,
            payment_method=data.get('payment_method', 'test'),
            customer_remarks=data.get('customer_remarks', ''),
            test_mode=data.get('payment_method') == 'test'
        )
        
        if not success:
            return jsonify({'error': '订单创建失败'}), 500
        
        # 如果是产品订单，关联产品
        if product:
            order.product_id = product.id
            if product.stock_count > 0:
                product.stock_count -= 1
            product.sold_count += 1
            db.session.commit()
        
        return jsonify({
            'success': True,
            'order_id': order.order_id,
            'message': '订单创建成功'
        }), 201
        
    except Exception as e:
        logger.error(f"创建订单失败: {e}")
        return jsonify({'error': '创建订单失败'}), 500

@api_bp.route('/orders/<order_id>')
def get_order(order_id):
    """获取订单详情"""
    try:
        order = Order.query.filter_by(order_id=order_id).first()
        if not order:
            return jsonify({'error': '订单不存在'}), 404
        
        order_dict = order.to_dict()
        
        # 添加订阅信息
        if hasattr(order, 'subscription') and order.subscription:
            order_dict['subscription'] = order.subscription.to_dict()
        
        return jsonify(order_dict)
        
    except Exception as e:
        logger.error(f"获取订单详情失败: {e}")
        return jsonify({'error': '获取订单详情失败'}), 500

@api_bp.route('/orders/<order_id>/process', methods=['POST'])
def process_order(order_id):
    """处理订单"""
    try:
        order_service = OrderService()
        success, message = order_service.process_order(order_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        logger.error(f"处理订单失败: {e}")
        return jsonify({'error': '处理订单失败'}), 500

@api_bp.route('/orders/by-email/<email>')
def get_orders_by_email(email):
    """根据邮箱获取订单列表"""
    try:
        orders = Order.query.filter_by(customer_email=email).order_by(Order.created_at.desc()).all()
        return jsonify([order.to_dict() for order in orders])
    except Exception as e:
        logger.error(f"根据邮箱获取订单失败: {e}")
        return jsonify({'error': '获取订单失败'}), 500
