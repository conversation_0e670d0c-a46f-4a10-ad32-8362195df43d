"""
产品相关模型
"""
from datetime import datetime
from . import db
from .enums import ProductType, NodeType


class Product(db.Model):
    """产品模型"""
    __tablename__ = 'products'

    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    product_type = db.Column(db.Enum(ProductType), nullable=False)

    # 产品配置
    duration_days = db.Column(db.Integer, nullable=False)
    traffic_limit_gb = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)
    node_type = db.Column(db.Enum(NodeType), nullable=False, default=NodeType.VLESS)

    # 分组相关配置
    target_group_id = db.Column(db.Integer, db.<PERSON><PERSON>('xui_panel_groups.id'), nullable=True)  # 目标分组
    group_specific_config = db.Column(db.Text, nullable=True)  # JSON格式的分组特定配置

    # 产品状态
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    stock_count = db.Column(db.Integer, nullable=False, default=-1)  # -1表示无限库存
    sold_count = db.Column(db.Integer, nullable=False, default=0)

    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联订单和分组
    orders = db.relationship('Order', backref='product', lazy=True)
    target_group = db.relationship('XUIPanelGroup', backref='products', lazy=True)

    def __repr__(self):
        return f'<Product {self.name}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'product_type': self.product_type.value if self.product_type else None,
            'duration_days': self.duration_days,
            'traffic_limit_gb': self.traffic_limit_gb,
            'price': self.price,
            'node_type': self.node_type.value if self.node_type else None,
            'is_active': self.is_active,
            'stock_count': self.stock_count,
            'sold_count': self.sold_count,
            'target_group_id': self.target_group_id,
            'target_group': self.target_group.to_dict() if self.target_group else None,
            'group_specific_config': self.group_specific_config,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
