"""
邮件发送服务
"""
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional
from config import Config

logger = logging.getLogger(__name__)

class EmailService:
    """邮件发送服务类"""
    
    def __init__(self):
        self.smtp_server = Config.MAIL_SERVER
        self.smtp_port = Config.MAIL_PORT
        self.use_tls = Config.MAIL_USE_TLS
        self.username = Config.MAIL_USERNAME
        self.password = Config.MAIL_PASSWORD
        self.default_sender = Config.MAIL_DEFAULT_SENDER or Config.MAIL_USERNAME
    
    def send_email(self, to_email: str, subject: str, body: str,
                   is_html: bool = False, attachments: List[str] = None) -> bool:
        """发送邮件"""
        try:
            # 确定发送方地址 - 统一使用认证用户名确保一致性
            sender_email = self.username  # 使用认证用户名作为发送方

            # 创建邮件对象
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = to_email
            msg['Subject'] = subject

            # 添加邮件正文
            if is_html:
                msg.attach(MIMEText(body, 'html', 'utf-8'))
            else:
                msg.attach(MIMEText(body, 'plain', 'utf-8'))

            # 添加附件
            if attachments:
                for file_path in attachments:
                    try:
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())

                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {file_path.split("/")[-1]}'
                        )
                        msg.attach(part)
                    except Exception as e:
                        logger.warning(f"添加附件失败 {file_path}: {str(e)}")

            # 连接SMTP服务器并发送邮件
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)

            if self.use_tls:
                server.starttls()

            if self.username and self.password:
                server.login(self.username, self.password)

            text = msg.as_string()
            # 使用认证用户名作为发送方，确保与登录凭据一致
            server.sendmail(sender_email, to_email, text)
            server.quit()

            logger.info(f"邮件发送成功: {to_email}, 发送方: {sender_email}")
            return True

        except Exception as e:
            logger.error(f"邮件发送失败 {to_email}: {str(e)}")
            return False
    
    def send_node_config_email(self, to_email: str, customer_name: str, 
                              order_id: str, vless_config: str, 
                              traffic_limit_gb: int, expiry_days: int) -> bool:
        """发送节点配置邮件"""
        subject = f"您的节点配置 - 订单 {order_id}"
        
        # 创建HTML邮件模板
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #4CAF50; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .config-box {{ background-color: #fff; border: 1px solid #ddd; padding: 15px; margin: 15px 0; border-radius: 5px; }}
                .config-text {{ font-family: monospace; word-break: break-all; background-color: #f5f5f5; padding: 10px; border-radius: 3px; }}
                .info-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .info-table th {{ background-color: #f2f2f2; }}
                .footer {{ text-align: center; padding: 20px; color: #666; font-size: 12px; }}
                .warning {{ color: #ff6b6b; font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>节点配置信息</h1>
                </div>
                
                <div class="content">
                    <h2>尊敬的 {customer_name or '客户'}，</h2>
                    <p>感谢您的购买！您的节点已成功配置，以下是详细信息：</p>
                    
                    <table class="info-table">
                        <tr>
                            <th>订单号</th>
                            <td>{order_id}</td>
                        </tr>
                        <tr>
                            <th>流量限制</th>
                            <td>{traffic_limit_gb} GB</td>
                        </tr>
                        <tr>
                            <th>有效期</th>
                            <td>{expiry_days} 天</td>
                        </tr>
                        <tr>
                            <th>协议类型</th>
                            <td>VLESS</td>
                        </tr>
                    </table>
                    
                    <div class="config-box">
                        <h3>节点配置链接：</h3>
                        <div class="config-text">{vless_config}</div>
                        <p><strong>使用说明：</strong></p>
                        <ul>
                            <li>复制上面的配置链接</li>
                            <li>在您的客户端中导入此配置</li>
                            <li>推荐客户端：V2rayN (Windows)、V2rayNG (Android)、Shadowrocket (iOS)</li>
                        </ul>
                    </div>
                    
                    <div class="warning">
                        <h3>重要提醒：</h3>
                        <ul>
                            <li>请妥善保管您的配置信息，不要分享给他人</li>
                            <li>请合理使用流量，超出限制后服务将暂停</li>
                            <li>如有问题，请及时联系客服</li>
                        </ul>
                    </div>
                </div>
                
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复</p>
                    <p>如有疑问，请联系客服</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return self.send_email(to_email, subject, html_body, is_html=True)
    
    def send_order_confirmation_email(self, to_email: str, customer_name: str, 
                                    order_id: str, order_details: dict) -> bool:
        """发送订单确认邮件"""
        subject = f"订单确认 - {order_id}"
        
        html_body = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #2196F3; color: white; padding: 20px; text-align: center; }}
                .content {{ padding: 20px; background-color: #f9f9f9; }}
                .info-table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                .info-table th, .info-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .info-table th {{ background-color: #f2f2f2; }}
                .footer {{ text-align: center; padding: 20px; color: #666; font-size: 12px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>订单确认</h1>
                </div>
                
                <div class="content">
                    <h2>尊敬的 {customer_name or '客户'}，</h2>
                    <p>我们已收到您的订单，正在为您处理中...</p>
                    
                    <table class="info-table">
                        <tr>
                            <th>订单号</th>
                            <td>{order_id}</td>
                        </tr>
                        <tr>
                            <th>节点类型</th>
                            <td>{order_details.get('node_type', 'VLESS')}</td>
                        </tr>
                        <tr>
                            <th>使用期限</th>
                            <td>{order_details.get('duration_days', 30)} 天</td>
                        </tr>
                        <tr>
                            <th>流量限制</th>
                            <td>{order_details.get('traffic_limit_gb', 100)} GB</td>
                        </tr>
                        <tr>
                            <th>订单金额</th>
                            <td>¥{order_details.get('price', 0)}</td>
                        </tr>
                        <tr>
                            <th>订单状态</th>
                            <td>处理中</td>
                        </tr>
                    </table>
                    
                    <p>我们将在处理完成后向您发送节点配置信息，请耐心等待。</p>
                </div>
                
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复</p>
                    <p>如有疑问，请联系客服</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return self.send_email(to_email, subject, html_body, is_html=True)
