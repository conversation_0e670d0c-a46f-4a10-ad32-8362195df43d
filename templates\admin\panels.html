{% extends "base.html" %}

{% block title %}面板管理 - 管理后台{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-server"></i> X-UI面板管理
                </h1>
                <div>
                    <button id="refresh-all-status" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-clockwise"></i> 刷新状态
                    </button>
                    <a href="{{ url_for('admin.create_panel') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 添加面板
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 面板列表 -->
    <div class="row">
        {% for panel in panels %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">{{ panel.name }}</h6>
                    <span class="badge bg-{{ 'success' if panel.status.value == 'active' else 'warning' if panel.status.value == 'maintenance' else 'secondary' }}">
                        {% if panel.status.value == 'active' %}
                            运行中
                        {% elif panel.status.value == 'maintenance' %}
                            维护中
                        {% else %}
                            停用
                        {% endif %}
                    </span>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        <strong>地址：</strong> {{ panel.base_url }}<br>
                        <strong>路径：</strong> {{ panel.path_prefix }}<br>
                        <strong>区域：</strong> {{ panel.region }}<br>
                        <strong>优先级：</strong> {{ panel.priority }}
                    </p>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">当前客户端</small>
                            <div class="fw-bold">{{ panel.current_clients }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">最大客户端</small>
                            <div class="fw-bold">{{ panel.max_clients }}</div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <small class="text-muted">在线状态</small>
                            <div>
                                <span class="badge panel-status-badge" id="status-badge-{{ panel.id }}"
                                      data-panel-id="{{ panel.id }}">
                                    <span class="spinner-border spinner-border-sm me-1" role="status" style="display: none;"></span>
                                    <span class="status-text">检查中...</span>
                                </span>
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">响应时间</small>
                            <div class="fw-bold">
                                <span id="response-time-{{ panel.id }}">--</span>ms
                            </div>
                        </div>
                    </div>
                    
                    <!-- 错误信息显示区域 -->
                    <div class="alert alert-warning mt-3 mb-0" id="error-alert-{{ panel.id }}" style="display: none;">
                        <small>
                            <i class="bi bi-exclamation-triangle"></i>
                            <span id="error-message-{{ panel.id }}"></span>
                        </small>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <a href="{{ panel.base_url }}{{ panel.path_prefix }}" target="_blank" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-box-arrow-up-right"></i> 访问
                        </a>
                        <form method="POST" action="{{ url_for('admin.delete_panel', panel_id=panel.id) }}" 
                              style="display: inline;" onsubmit="return confirm('确定要删除这个面板吗？')">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        
        {% if not panels %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-server text-muted" style="font-size: 4rem;"></i>
                    <h4 class="text-muted mt-3">暂无面板</h4>
                    <p class="text-muted">还没有添加任何X-UI面板</p>
                    <a href="{{ url_for('admin.create_panel') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> 添加第一个面板
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回仪表板
            </a>
        </div>
    </div>
</div>

<script>
// 面板状态检查JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 页面加载时自动检查所有面板状态
    checkAllPanelsStatus();

    // 刷新按钮点击事件
    document.getElementById('refresh-all-status').addEventListener('click', function() {
        checkAllPanelsStatus();
    });
});

function checkAllPanelsStatus() {
    const refreshBtn = document.getElementById('refresh-all-status');
    const refreshIcon = refreshBtn.querySelector('i');

    // 显示加载状态
    refreshBtn.disabled = true;
    refreshIcon.className = 'bi bi-arrow-clockwise spin';

    // 为所有面板显示检查中状态
    document.querySelectorAll('.panel-status-badge').forEach(badge => {
        const spinner = badge.querySelector('.spinner-border');
        const statusText = badge.querySelector('.status-text');

        badge.className = 'badge panel-status-badge bg-secondary';
        spinner.style.display = 'inline-block';
        statusText.textContent = '检查中...';

        // 隐藏错误信息
        const panelId = badge.getAttribute('data-panel-id');
        const errorAlert = document.getElementById(`error-alert-${panelId}`);
        if (errorAlert) {
            errorAlert.style.display = 'none';
        }
    });

    // 发送Ajax请求
    fetch('/admin/api/panels/check-status')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.panels.forEach(panel => {
                    updatePanelStatus(panel);
                });
            } else {
                console.error('检查面板状态失败:', data.error);
                showGlobalError('检查面板状态失败: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Ajax请求失败:', error);
            showGlobalError('网络请求失败，请检查网络连接');
        })
        .finally(() => {
            // 恢复刷新按钮状态
            refreshBtn.disabled = false;
            refreshIcon.className = 'bi bi-arrow-clockwise';
        });
}

function updatePanelStatus(panel) {
    const statusBadge = document.getElementById(`status-badge-${panel.id}`);
    const responseTime = document.getElementById(`response-time-${panel.id}`);
    const errorAlert = document.getElementById(`error-alert-${panel.id}`);
    const errorMessage = document.getElementById(`error-message-${panel.id}`);

    if (!statusBadge) return;

    const spinner = statusBadge.querySelector('.spinner-border');
    const statusText = statusBadge.querySelector('.status-text');

    // 隐藏加载动画
    spinner.style.display = 'none';

    if (panel.is_online) {
        // 在线状态
        statusBadge.className = 'badge panel-status-badge bg-success';
        statusText.textContent = '在线';

        // 更新响应时间
        if (responseTime) {
            responseTime.textContent = panel.response_time;
        }

        // 隐藏错误信息
        if (errorAlert) {
            errorAlert.style.display = 'none';
        }
    } else {
        // 离线状态
        statusBadge.className = 'badge panel-status-badge bg-danger';
        statusText.textContent = '离线';

        // 更新响应时间
        if (responseTime) {
            responseTime.textContent = panel.response_time || '--';
        }

        // 显示错误信息
        if (errorAlert && errorMessage && panel.error_message) {
            errorMessage.textContent = panel.error_message;
            errorAlert.style.display = 'block';
        }
    }
}

function showGlobalError(message) {
    // 创建全局错误提示
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
    alertDiv.innerHTML = `
        <i class="bi bi-exclamation-triangle"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // 5秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// CSS动画
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .panel-status-badge {
        min-width: 60px;
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);
</script>

{% endblock %}
