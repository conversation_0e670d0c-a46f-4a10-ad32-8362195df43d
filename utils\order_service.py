"""
订单管理服务
"""
import uuid
import logging
import base64
import secrets
from datetime import datetime, timedelta
from typing import Dict, Optional, Tuple
from models import db, Order, NodeConfig, OrderStatus, NodeType, Subscription
from multi_xui_manager import MultiXUIManager
from utils.email_service import EmailService

logger = logging.getLogger(__name__)

class OrderService:
    """订单管理服务类"""
    
    def __init__(self):
        logger.info("初始化OrderService...")
        self.xui_manager = MultiXUIManager()
        logger.info(f"OrderService中的MultiXUIManager初始化完成，面板数量: {len(self.xui_manager.panels)}")
        logger.info(f"OrderService中的面板列表: {list(self.xui_manager.panels.keys())}")
        self.email_service = EmailService()
    
    def create_order(self, customer_email: str, customer_name: str = None,
                    node_type: str = 'vless', duration_days: int = 30,
                    traffic_limit_gb: int = 100, price: float = 0, # This price is the final_price
                    payment_method: str = None, payment_id: str = None,
                    customer_remarks: str = None, test_mode: bool = False,
                    user_id: int = None,
                    applied_coupon_code: str = None,
                    discount_amount: float = 0.0) -> Tuple[bool, Optional[Order]]:
        """创建新订单"""
        try:
            # 生成订单ID
            order_id = f"ORD{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
            
            # 创建订单对象
            order = Order(
                order_id=order_id,
                user_id=user_id,  # 添加用户ID
                customer_email=customer_email,
                customer_name=customer_name,
                node_type=NodeType(node_type.lower()),
                duration_days=duration_days,
                traffic_limit_gb=traffic_limit_gb,
                price=price,
                status=OrderStatus.PENDING,
                payment_method=payment_method,
                payment_id=payment_id,
                customer_remarks=customer_remarks,
                expires_at=datetime.utcnow() + timedelta(days=duration_days),
                applied_coupon_code=applied_coupon_code, # Store applied coupon code
                discount_amount=discount_amount # Store discount amount
            )
            
            # 保存到数据库
            db.session.add(order)
            db.session.commit()
            
            logger.info(f"订单创建成功: {order_id}")
            
            # 发送订单确认邮件（测试模式下跳过）
            if not test_mode:
                order_details = {
                    'node_type': node_type.upper(),
                    'duration_days': duration_days,
                    'traffic_limit_gb': traffic_limit_gb,
                    'price': price
                }

                self.email_service.send_order_confirmation_email(
                    customer_email, customer_name, order_id, order_details
                )
            else:
                logger.info(f"测试模式：跳过订单确认邮件发送 - {order_id}")
            
            return True, order
            
        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            db.session.rollback()
            return False, None
    
    def process_order(self, order_id: str) -> Tuple[bool, Optional[str]]:
        """处理订单，分配节点"""
        try:
            # 查找订单
            order = Order.query.filter_by(order_id=order_id).first()
            if not order:
                logger.error(f"订单不存在: {order_id}")
                return False, "订单不存在"
            
            if order.status != OrderStatus.PENDING:
                logger.warning(f"订单状态不正确: {order_id}, 当前状态: {order.status}")
                return False, f"订单状态不正确: {order.status.value}"
            
            # 更新订单状态为处理中
            order.status = OrderStatus.PROCESSING
            db.session.commit()
            
            # 生成客户端ID和邮箱
            client_id = str(uuid.uuid4())
            client_email = order.customer_remarks or f"{order.customer_email}_{order.order_id}"
            
            # 根据产品分组配置分配节点
            logger.info(f"开始为订单 {order_id} 分配节点")
            logger.info(f"客户端邮箱: {client_email}")
            logger.info(f"客户端ID: {client_id}")
            logger.info(f"流量限制: {order.traffic_limit_gb}GB")
            logger.info(f"有效期: {order.duration_days}天")

            # 检查订单是否关联产品和分组
            logger.info(f"开始检查产品分组配置...")
            logger.info(f"订单ID: {order_id}")
            logger.info(f"订单产品ID: {order.product_id}")
            logger.info(f"hasattr(order, 'product'): {hasattr(order, 'product')}")

            if hasattr(order, 'product'):
                logger.info(f"order.product: {order.product}")
                if order.product:
                    logger.info(f"产品名称: {order.product.name}")
                    logger.info(f"产品目标分组ID: {order.product.target_group_id}")
                    logger.info(f"target_group_id is not None: {order.product.target_group_id is not None}")
                else:
                    logger.info("order.product 为 None")
            else:
                logger.info("订单没有 product 属性")

            # 检查是否使用产品分组策略
            if (hasattr(order, 'product') and order.product and order.product.target_group_id):
                logger.info(f"✅ 使用产品分组策略，分组ID: {order.product.target_group_id}")
                success, node_configs = self._add_client_to_product_group_multi_nodes(
                    order, client_email, client_id
                )
            else:
                logger.info("❌ 产品未指定分组，使用默认分配策略")
                success, node_configs = self._add_client_to_default_panel(
                    order, client_email, client_id
                )

            if not success or not node_configs:
                order.status = OrderStatus.FAILED
                db.session.commit()
                logger.error(f"节点分配失败: {order_id}")
                return False, "节点分配失败：无法连接到X-UI面板或添加客户端失败"

            # 批量保存节点配置
            for node_config in node_configs:
                db.session.add(node_config)

            # 处理订阅覆盖逻辑（如果订单有用户ID）
            if hasattr(order, 'user_id') and order.user_id:
                from services.subscription_service import SubscriptionService
                subscription_service = SubscriptionService()
                subscription_service.handle_user_subscription_override(order.user_id, order)

            # 创建订阅记录（_create_subscription 方法已经直接插入数据库）
            subscription = self._create_subscription(order)

            # 更新订单状态为已完成
            order.status = OrderStatus.COMPLETED
            order.updated_at = datetime.utcnow()

            # 更新优惠券使用次数
            if order.applied_coupon_code:
                self._update_coupon_usage(order.applied_coupon_code)

            db.session.commit()

            # 发送订阅链接邮件（测试模式下跳过）
            if order.payment_method != 'test':
                self._send_subscription_email(order, subscription)
            else:
                logger.info(f"测试模式：跳过订阅邮件发送 - {order_id}")

            logger.info(f"订单处理完成: {order_id}, 创建了 {len(node_configs)} 个节点")
            return True, "订单处理成功"
            
        except Exception as e:
            logger.error(f"处理订单失败 {order_id}: {str(e)}")
            db.session.rollback()
            return False, f"处理失败: {str(e)}"
    
    def get_order_by_id(self, order_id: str) -> Optional[Order]:
        """根据订单ID获取订单"""
        return Order.query.filter_by(order_id=order_id).first()
    
    def get_orders_by_email(self, customer_email: str) -> list:
        """根据邮箱获取订单列表"""
        return Order.query.filter_by(customer_email=customer_email).order_by(Order.created_at.desc()).all()
    
    def get_config_by_remarks(self, customer_remarks: str) -> Optional[NodeConfig]:
        """根据客户备注名获取节点配置"""
        try:
            # 先通过订单的客户备注查找
            order = Order.query.filter_by(customer_remarks=customer_remarks).first()
            if order and order.node_configs:
                return order.node_configs[0]  # 返回第一个配置
            
            # 如果没找到，尝试通过节点配置的客户端邮箱查找
            node_config = NodeConfig.query.filter_by(client_email=customer_remarks).first()
            return node_config
            
        except Exception as e:
            logger.error(f"根据备注名查找配置失败 {customer_remarks}: {str(e)}")
            return None
    
    def update_traffic_stats(self, client_email: str) -> bool:
        """更新客户端流量统计"""
        try:
            # 从所有X-UI面板获取流量信息
            traffic_info = self.xui_manager.get_client_traffic_from_all_panels(client_email)
            if not traffic_info:
                return False

            # 查找对应的节点配置
            node_config = NodeConfig.query.filter_by(client_email=client_email).first()
            if not node_config:
                logger.warning(f"未找到客户端配置: {client_email}")
                return False

            # 更新流量信息（转换为GB）
            node_config.upload_traffic_gb = traffic_info.get('up', 0) / (1024 * 1024 * 1024)
            node_config.download_traffic_gb = traffic_info.get('down', 0) / (1024 * 1024 * 1024)
            node_config.total_traffic_gb = node_config.upload_traffic_gb + node_config.download_traffic_gb
            node_config.last_traffic_update = datetime.utcnow()

            db.session.commit()

            logger.info(f"流量统计更新成功: {client_email}")
            return True

        except Exception as e:
            logger.error(f"更新流量统计失败 {client_email}: {str(e)}")
            db.session.rollback()
            return False
    
    def cancel_order(self, order_id: str, reason: str = None) -> Tuple[bool, str]:
        """取消订单"""
        try:
            order = Order.query.filter_by(order_id=order_id).first()
            if not order:
                return False, "订单不存在"
            
            if order.status in [OrderStatus.COMPLETED, OrderStatus.CANCELLED]:
                return False, f"订单状态不允许取消: {order.status.value}"
            
            order.status = OrderStatus.CANCELLED
            order.updated_at = datetime.utcnow()
            if reason:
                order.notes = f"取消原因: {reason}"
            
            db.session.commit()
            
            logger.info(f"订单已取消: {order_id}")
            return True, "订单取消成功"
            
        except Exception as e:
            logger.error(f"取消订单失败 {order_id}: {str(e)}")
            db.session.rollback()
            return False, f"取消失败: {str(e)}"

    def _add_client_to_product_group(self, order, client_email: str, client_id: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        """根据产品分组配置添加客户端"""
        try:
            from models import XUIPanelGroup, XUIPanelGroupMembership, XUIPanel, PanelStatus

            # 获取产品关联的分组
            group = XUIPanelGroup.query.get(order.product.target_group_id)
            if not group:
                logger.error(f"找不到分组ID: {order.product.target_group_id}")
                return False, None, None

            if not group.is_active:
                logger.error(f"分组 {group.name} 未激活")
                return False, None, None

            logger.info(f"使用产品分组: {group.name}")

            # 获取分组中的面板成员关系
            memberships = XUIPanelGroupMembership.query.filter_by(
                group_id=group.id
            ).join(XUIPanel).filter(
                XUIPanel.status == PanelStatus.ACTIVE
            ).order_by(XUIPanelGroupMembership.weight.asc()).all()

            if not memberships:
                logger.error(f"分组 {group.name} 中没有活跃的面板")
                return False, None, None

            logger.info(f"分组中有 {len(memberships)} 个活跃面板")

            # 调试：检查当前管理器状态
            logger.info(f"当前管理器中的面板: {list(self.xui_manager.panels.keys())}")
            logger.info(f"当前管理器中的客户端: {list(self.xui_manager.clients.keys())}")

            # 尝试在分组的面板中添加客户端
            for membership in memberships:
                panel = membership.panel
                panel_id = f"panel_{panel.id}"

                logger.info(f"尝试面板: {panel.name} (ID: {panel_id})")

                # 检查面板是否在管理器中
                if panel_id not in self.xui_manager.clients:
                    logger.warning(f"面板 {panel_id} 不在管理器中，跳过")
                    continue

                logger.info(f"面板 {panel_id} 在管理器中，直接尝试添加客户端（跳过健康检查以避免代理问题）")

                # 使用指定的入站协议ID（如果有）
                if membership.inbound_id:
                    logger.info(f"使用指定的入站协议ID: {membership.inbound_id}")
                    success, client_data = self._add_client_to_specific_inbound(
                        panel_id, membership.inbound_id, client_email, client_id, order
                    )
                else:
                    logger.info("未指定入站协议ID，使用面板的第一个可用入站协议")
                    success, client_data = self.xui_manager._add_client_to_panel(
                        panel_id, client_email, client_id,
                        order.traffic_limit_gb, order.duration_days
                    )

                if success:
                    logger.info(f"成功在分组面板 {panel.name} 中添加客户端")
                    return True, client_data, panel_id
                else:
                    logger.warning(f"在面板 {panel.name} 中添加客户端失败，尝试下一个")

            logger.error(f"在分组 {group.name} 的所有面板中添加客户端都失败了")
            return False, None, None

        except Exception as e:
            logger.error(f"根据产品分组添加客户端时发生错误: {str(e)}")
            return False, None, None

    def _add_client_to_specific_inbound(self, panel_id: str, inbound_id: int,
                                       client_email: str, client_id: str, order) -> Tuple[bool, Optional[Dict]]:
        """向指定面板的指定入站协议添加客户端"""
        try:
            logger.info(f"向面板 {panel_id} 的入站协议 {inbound_id} 添加客户端 {client_email}")

            if panel_id not in self.xui_manager.clients:
                logger.error(f"面板 {panel_id} 不在管理器中")
                return False, None

            client = self.xui_manager.clients[panel_id]

            # 直接使用指定的入站协议ID添加客户端
            success, client_data = client.add_client_to_inbound(
                inbound_id=inbound_id,
                client_email=client_email,
                client_id=client_id,
                traffic_limit_gb=order.traffic_limit_gb,
                expiry_days=order.duration_days
            )

            if success:
                logger.info(f"成功向入站协议 {inbound_id} 添加客户端")

                # 生成VLESS配置
                panel_config = self.xui_manager.panels[panel_id]
                server_address = panel_config['base_url'].replace('http://', '').replace('https://', '').split(':')[0]

                # 获取入站协议信息来生成配置
                inbounds = client.get_inbounds()
                target_inbound = None
                for inbound in inbounds:
                    if inbound.get('id') == inbound_id:
                        target_inbound = inbound
                        break

                if target_inbound:
                    vless_config = client.generate_vless_config(
                        target_inbound, client_id, client_email, server_address
                    )
                    client_data['vless_config'] = vless_config
                    client_data['server_address'] = server_address
                    client_data['server_port'] = target_inbound.get('port')

                return True, client_data
            else:
                logger.error(f"向入站协议 {inbound_id} 添加客户端失败")
                return False, None

        except Exception as e:
            logger.error(f"向指定入站协议添加客户端时发生错误: {str(e)}")
            return False, None

    def _add_client_to_product_group_multi_nodes(self, order, client_email: str, client_id: str) -> Tuple[bool, list]:
        """根据产品分组配置添加客户端到多个节点（每个面板一个节点）"""
        try:
            from models import XUIPanelGroup, XUIPanelGroupMembership, XUIPanel, PanelStatus

            # 获取产品关联的分组
            group = XUIPanelGroup.query.get(order.product.target_group_id)
            if not group:
                logger.error(f"找不到分组ID: {order.product.target_group_id}")
                return False, []

            if not group.is_active:
                logger.error(f"分组 {group.name} 未激活")
                return False, []

            logger.info(f"使用产品分组: {group.name}")

            # 获取分组中的面板成员关系
            memberships = XUIPanelGroupMembership.query.filter_by(
                group_id=group.id
            ).join(XUIPanel).filter(
                XUIPanel.status == PanelStatus.ACTIVE
            ).order_by(XUIPanelGroupMembership.weight.asc()).all()

            if not memberships:
                logger.error(f"分组 {group.name} 中没有活跃的面板")
                return False, []

            logger.info(f"分组中有 {len(memberships)} 个活跃面板，将为每个面板创建节点")

            node_configs = []
            success_count = 0

            # 为每个面板创建一个节点
            for i, membership in enumerate(memberships):
                panel = membership.panel
                panel_id = f"panel_{panel.id}"

                # 为每个面板生成独立的UUID和邮箱
                unique_client_id = str(uuid.uuid4())
                # 只有多个面板时才添加后缀，单个面板时保持原邮箱
                if len(memberships) > 1:
                    unique_client_email = f"{client_email}_{i+1}"
                else:
                    unique_client_email = client_email

                logger.info(f"为面板 {panel.name} 创建节点 (ID: {panel_id})")

                # 检查面板是否在管理器中
                if panel_id not in self.xui_manager.clients:
                    logger.warning(f"面板 {panel_id} 不在管理器中，跳过")
                    continue

                # 添加客户端到面板
                if membership.inbound_id:
                    logger.info(f"使用指定的入站协议ID: {membership.inbound_id}")
                    success, client_data = self._add_client_to_specific_inbound(
                        panel_id, membership.inbound_id, unique_client_email, unique_client_id, order
                    )
                else:
                    logger.info("未指定入站协议ID，使用面板的第一个可用入站协议")
                    success, client_data = self.xui_manager._add_client_to_panel(
                        panel_id, unique_client_email, unique_client_id,
                        order.traffic_limit_gb, order.duration_days
                    )

                if success and client_data:
                    # 创建节点配置记录
                    node_config = NodeConfig(
                        order_id=order.id,
                        xui_inbound_id=membership.inbound_id or 1,
                        client_id=unique_client_id,
                        client_email=unique_client_email,
                        server_address=client_data.get('server_address', ''),
                        server_port=client_data.get('server_port', 443),
                        protocol=order.node_type.value,
                        vless_config=client_data.get('vless_config', '')
                    )
                    node_configs.append(node_config)
                    success_count += 1
                    logger.info(f"成功在面板 {panel.name} 中创建节点")
                else:
                    logger.warning(f"在面板 {panel.name} 中创建节点失败")

            if success_count > 0:
                logger.info(f"成功创建了 {success_count}/{len(memberships)} 个节点")
                return True, node_configs
            else:
                logger.error("所有面板都创建节点失败")
                return False, []

        except Exception as e:
            logger.error(f"创建多节点时发生错误: {str(e)}")
            return False, []

    def _add_client_to_default_panel(self, order, client_email: str, client_id: str) -> Tuple[bool, list]:
        """使用默认策略添加客户端（单节点）"""
        try:
            success, client_data, panel_id = self.xui_manager.add_client_to_any_panel(
                client_email=client_email,
                client_id=client_id,
                traffic_limit_gb=order.traffic_limit_gb,
                expiry_days=order.duration_days
            )

            if success and client_data:
                node_config = NodeConfig(
                    order_id=order.id,
                    xui_inbound_id=1,
                    client_id=client_id,
                    client_email=client_email,
                    server_address=client_data.get('server_address', ''),
                    server_port=client_data.get('server_port', 443),
                    protocol=order.node_type.value,
                    vless_config=client_data.get('vless_config', '')
                )
                return True, [node_config]
            else:
                return False, []

        except Exception as e:
            logger.error(f"使用默认策略创建节点时发生错误: {str(e)}")
            return False, []

    def _create_subscription(self, order):
        """为订单创建订阅记录"""
        try:
            # 检查是否已存在订阅
            existing_subscription = Subscription.query.filter_by(order_id=order.id).first()
            if existing_subscription:
                logger.info(f"订单 {order.order_id} 已存在订阅，返回现有订阅")
                return existing_subscription

            # 生成唯一的订阅令牌
            subscription_token = secrets.token_urlsafe(32)

            # 从产品获取分组ID
            group_id = None
            if hasattr(order, 'product') and order.product and order.product.target_group_id:
                group_id = order.product.target_group_id

            # 使用SQLAlchemy模型创建订阅
            subscription = Subscription(
                order_id=order.id,
                subscription_token=subscription_token,
                is_active=True,
                group_id=group_id,
                expires_at=order.expires_at
            )

            db.session.add(subscription)
            db.session.commit()

            logger.info(f"为订单 {order.order_id} 创建订阅成功，令牌: {subscription_token[:8]}..., 分组ID: {group_id}")
            return subscription

        except Exception as e:
            logger.error(f"创建订阅失败: {e}")
            db.session.rollback()
            raise Exception(f"订阅创建失败: {e}")

    def _send_subscription_email(self, order, subscription):
        """发送订阅链接邮件"""
        try:
            # 这里可以发送包含订阅链接的邮件
            # 暂时使用原有的邮件服务，后续可以创建专门的订阅邮件模板
            logger.info(f"发送订阅邮件到: {order.customer_email}")
            # TODO: 实现订阅邮件发送
        except Exception as e:
            logger.error(f"发送订阅邮件失败: {str(e)}")

    def _update_coupon_usage(self, coupon_code: str):
        """更新优惠券使用次数，如果达到上限则删除"""
        try:
            from models import Coupon

            # 跳过测试模式标记
            if coupon_code == "TESTMODE":
                logger.info("跳过测试模式优惠券使用次数更新")
                return

            coupon = Coupon.query.filter_by(code=coupon_code).first()
            if not coupon:
                logger.warning(f"优惠券不存在: {coupon_code}")
                return

            # 增加使用次数
            coupon.increment_usage()

            # 检查是否需要删除
            if coupon.should_be_deleted():
                logger.info(f"优惠券 {coupon_code} 已达到使用上限，将被删除")
                db.session.delete(coupon)
            else:
                logger.info(f"优惠券 {coupon_code} 使用次数更新为 {coupon.used_count}")

            # 注意：这里不提交事务，由调用方统一提交

        except Exception as e:
            logger.error(f"更新优惠券使用次数失败 {coupon_code}: {str(e)}")
            # 不抛出异常，避免影响订单处理
