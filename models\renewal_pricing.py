"""
续费价格配置模型
"""
from datetime import datetime
from . import db


class RenewalPricing(db.Model):
    """续费价格配置模型"""
    __tablename__ = 'renewal_pricing'

    id = db.Column(db.Integer, primary_key=True)
    duration_months = db.Column(db.Integer, nullable=False, unique=True)  # 续费时长（月）
    discount_percentage = db.Column(db.Float, nullable=False, default=0.0)  # 折扣百分比
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, nullable=False, default=True)  # 是否启用

    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<RenewalPricing {self.duration_months}个月 {self.discount_percentage}%折扣>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'duration_months': self.duration_months,
            'discount_percentage': self.discount_percentage,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }

    @classmethod
    def get_active_pricing(cls):
        """获取所有启用的续费价格配置"""
        return cls.query.filter_by(is_active=True).order_by(cls.duration_months.asc()).all()

    @classmethod
    def get_pricing_by_duration(cls, duration_months: int):
        """根据时长获取价格配置"""
        return cls.query.filter_by(duration_months=duration_months, is_active=True).first()

    @classmethod
    def create_default_pricing(cls):
        """创建默认的续费价格配置"""
        default_configs = [
            {'duration_months': 3, 'discount_percentage': 5.0},   # 季付5%折扣
            {'duration_months': 6, 'discount_percentage': 10.0},  # 半年付10%折扣
            {'duration_months': 12, 'discount_percentage': 20.0}, # 年付20%折扣
        ]
        
        for config in default_configs:
            existing = cls.query.filter_by(duration_months=config['duration_months']).first()
            if not existing:
                pricing = cls(
                    duration_months=config['duration_months'],
                    discount_percentage=config['discount_percentage'],
                    is_active=True
                )
                db.session.add(pricing)
        
        db.session.commit()
