"""
续费任务跟踪模型
"""
import json
from datetime import datetime
from . import db
from .enums import OrderStatus


class RenewalTaskStatus:
    """续费任务状态枚举"""
    PENDING = "pending"          # 待处理
    PROCESSING = "processing"    # 处理中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败
    PARTIAL_SUCCESS = "partial_success"  # 部分成功


class RenewalTask(db.Model):
    """续费任务跟踪模型"""
    __tablename__ = 'renewal_tasks'

    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.<PERSON>ey('subscriptions.id', ondelete='CASCADE'), nullable=False)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=True)  # 关联的续费订单
    
    # 续费信息
    renewal_months = db.Column(db.Integer, nullable=False)  # 续费月数
    original_expiry = db.Column(db.DateTime, nullable=False)  # 原到期时间
    new_expiry = db.Column(db.DateTime, nullable=False)  # 新到期时间
    
    # 任务状态
    status = db.Column(db.String(20), nullable=False, default=RenewalTaskStatus.PENDING)
    panel_update_status = db.Column(db.Text, nullable=True)  # JSON格式记录每个面板的更新状态
    error_message = db.Column(db.Text, nullable=True)  # 错误信息
    retry_count = db.Column(db.Integer, nullable=False, default=0)  # 重试次数
    max_retries = db.Column(db.Integer, nullable=False, default=3)  # 最大重试次数
    
    # 时间字段
    created_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = db.Column(db.DateTime, nullable=True)  # 完成时间

    # 关联关系 - 修改为级联删除
    subscription = db.relationship('Subscription', backref=db.backref('renewal_tasks', cascade='all, delete-orphan'), lazy=True)
    order = db.relationship('Order', backref='renewal_tasks', lazy=True)

    def __repr__(self):
        return f'<RenewalTask {self.id} subscription={self.subscription_id} status={self.status}>'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'subscription_id': self.subscription_id,
            'order_id': self.order_id,
            'renewal_months': self.renewal_months,
            'original_expiry': self.original_expiry.isoformat() if self.original_expiry else None,
            'new_expiry': self.new_expiry.isoformat() if self.new_expiry else None,
            'status': self.status,
            'panel_update_status': json.loads(self.panel_update_status) if self.panel_update_status else {},
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }

    def set_panel_update_status(self, panel_status_dict):
        """设置面板更新状态"""
        self.panel_update_status = json.dumps(panel_status_dict)

    def get_panel_update_status(self):
        """获取面板更新状态"""
        if self.panel_update_status:
            return json.loads(self.panel_update_status)
        return {}

    def update_panel_status(self, panel_id, status, error_message=None):
        """更新单个面板的状态"""
        panel_status = self.get_panel_update_status()
        panel_status[str(panel_id)] = {
            'status': status,
            'error_message': error_message,
            'updated_at': datetime.utcnow().isoformat()
        }
        self.set_panel_update_status(panel_status)

    def is_completed(self):
        """检查任务是否完成"""
        return self.status in [RenewalTaskStatus.COMPLETED, RenewalTaskStatus.PARTIAL_SUCCESS]

    def can_retry(self):
        """检查是否可以重试"""
        return self.retry_count < self.max_retries and self.status in [RenewalTaskStatus.FAILED, RenewalTaskStatus.PARTIAL_SUCCESS]

    def mark_completed(self):
        """标记任务为完成"""
        self.status = RenewalTaskStatus.COMPLETED
        self.completed_at = datetime.utcnow()

    def mark_failed(self, error_message):
        """标记任务为失败"""
        self.status = RenewalTaskStatus.FAILED
        self.error_message = error_message

    def mark_partial_success(self, error_message=None):
        """标记任务为部分成功"""
        self.status = RenewalTaskStatus.PARTIAL_SUCCESS
        if error_message:
            self.error_message = error_message

    def increment_retry(self):
        """增加重试次数"""
        self.retry_count += 1
        self.status = RenewalTaskStatus.PROCESSING
