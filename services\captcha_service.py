"""
图形验证码服务
"""
import logging
import io
import base64
from typing import Tuple, Optional, Dict, Any
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import random
from flask import request, session
from models import db, CaptchaCode

logger = logging.getLogger(__name__)


class CaptchaService:
    """图形验证码服务类"""
    
    def __init__(self):
        self.default_valid_minutes = 10  # 默认有效期10分钟
        self.image_width = 120
        self.image_height = 40
        self.font_size = 24

    def generate_captcha(self, session_id: Optional[str] = None) -> Tuple[bool, str, Optional[Dict]]:
        """
        生成图形验证码
        
        Args:
            session_id: 会话ID，如果不提供则使用Flask session
            
        Returns:
            Tuple: (是否成功, 消息, 验证码信息)
        """
        try:
            # 获取会话ID
            if session_id is None:
                session_id = session.get('session_id')
                if not session_id:
                    # 生成新的会话ID
                    session_id = self._generate_session_id()
                    session['session_id'] = session_id
            
            # 清理该会话的旧验证码
            self._cleanup_session_codes(session_id)
            
            # 创建新的验证码
            captcha_code = CaptchaCode(
                session_id=session_id,
                valid_minutes=self.default_valid_minutes,
                ip_address=self._get_client_ip(),
                user_agent=request.headers.get('User-Agent', '') if request else ''
            )
            
            # 保存到数据库
            db.session.add(captcha_code)
            db.session.commit()
            
            # 生成验证码图片
            image_data = self._generate_captcha_image(captcha_code.code)
            
            logger.info(f"图形验证码生成成功: session_id={session_id}")
            return True, '验证码生成成功', {
                'captcha_id': captcha_code.id,
                'session_id': session_id,
                'image_data': image_data,
                'expires_at': captcha_code.expires_at.isoformat()
            }
                
        except Exception as e:
            logger.error(f"生成图形验证码失败: {e}")
            db.session.rollback()
            return False, '系统错误，请稍后重试', None

    def check_captcha(self, session_id: str, input_code: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        检查图形验证码（不标记为已使用，用于前端预验证）

        Args:
            session_id: 会话ID
            input_code: 输入的验证码

        Returns:
            Tuple: (是否验证成功, 消息, 验证码信息)
        """
        try:
            # 查找有效的验证码
            captcha_code = CaptchaCode.get_valid_code(session_id)

            if not captcha_code:
                return False, '验证码不存在或已过期', None

            # 检查验证码（不标记为已使用）
            success, message = captcha_code.check_code(input_code)

            if success:
                logger.debug(f"图形验证码检查成功: session_id={session_id}")
                return True, message, {
                    'captcha_id': captcha_code.id,
                    'attempts': captcha_code.attempts,
                    'max_attempts': captcha_code.max_attempts
                }
            else:
                logger.debug(f"图形验证码检查失败: session_id={session_id}, 原因: {message}")
                return False, message, {
                    'captcha_id': captcha_code.id,
                    'attempts': captcha_code.attempts,
                    'max_attempts': captcha_code.max_attempts
                }

        except Exception as e:
            logger.error(f"检查图形验证码失败: {e}")
            return False, '系统错误，请稍后重试', None

    def verify_captcha(self, session_id: str, input_code: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        验证图形验证码（会标记为已使用，用于真正的验证）

        Args:
            session_id: 会话ID
            input_code: 输入的验证码

        Returns:
            Tuple: (是否验证成功, 消息, 验证码信息)
        """
        try:
            # 查找有效的验证码
            captcha_code = CaptchaCode.get_valid_code(session_id)

            if not captcha_code:
                return False, '验证码不存在或已过期', None

            # 验证验证码
            success, message = captcha_code.verify(input_code, self._get_client_ip())

            # 保存验证结果
            db.session.commit()

            if success:
                logger.info(f"图形验证码验证成功: session_id={session_id}")
                return True, message, {
                    'captcha_id': captcha_code.id,
                    'used_at': captcha_code.used_at.isoformat() if captcha_code.used_at else None
                }
            else:
                logger.warning(f"图形验证码验证失败: session_id={session_id}, 原因: {message}")
                return False, message, {
                    'captcha_id': captcha_code.id,
                    'attempts': captcha_code.attempts,
                    'max_attempts': captcha_code.max_attempts
                }

        except Exception as e:
            logger.error(f"验证图形验证码失败: {e}")
            db.session.rollback()
            return False, '系统错误，请稍后重试', None

    def _generate_captcha_image(self, code: str) -> str:
        """
        生成验证码图片
        
        Args:
            code: 验证码文本
            
        Returns:
            str: Base64编码的图片数据
        """
        # 创建图片
        image = Image.new('RGB', (self.image_width, self.image_height), color='white')
        draw = ImageDraw.Draw(image)
        
        # 添加背景噪点
        self._add_noise(draw)
        
        # 绘制验证码文本
        self._draw_text(draw, code)
        
        # 添加干扰线
        self._add_lines(draw)
        
        # 转换为Base64
        buffer = io.BytesIO()
        image.save(buffer, format='PNG')
        image_data = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{image_data}"

    def _add_noise(self, draw):
        """添加背景噪点"""
        for _ in range(50):
            x = random.randint(0, self.image_width)
            y = random.randint(0, self.image_height)
            draw.point((x, y), fill=self._random_color())

    def _draw_text(self, draw, code: str):
        """绘制验证码文本"""
        try:
            # 尝试使用系统字体
            font = ImageFont.truetype("arial.ttf", self.font_size)
        except:
            # 如果没有找到字体，使用默认字体
            font = ImageFont.load_default()
        
        # 计算文本位置
        char_width = self.image_width // len(code)
        
        for i, char in enumerate(code):
            x = char_width * i + random.randint(5, 15)
            y = random.randint(5, 15)
            
            # 随机颜色
            color = self._random_color()
            
            # 绘制字符
            draw.text((x, y), char, font=font, fill=color)

    def _add_lines(self, draw):
        """添加干扰线"""
        for _ in range(3):
            start = (random.randint(0, self.image_width), random.randint(0, self.image_height))
            end = (random.randint(0, self.image_width), random.randint(0, self.image_height))
            draw.line([start, end], fill=self._random_color(), width=1)

    def _random_color(self):
        """生成随机颜色"""
        return (
            random.randint(0, 255),
            random.randint(0, 255),
            random.randint(0, 255)
        )

    def _generate_session_id(self):
        """生成会话ID"""
        import uuid
        return str(uuid.uuid4())

    def _get_client_ip(self):
        """获取客户端IP地址"""
        if request:
            return request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        return None

    def _cleanup_session_codes(self, session_id: str):
        """清理会话的旧验证码"""
        try:
            old_codes = CaptchaCode.query.filter_by(session_id=session_id).all()
            for code in old_codes:
                db.session.delete(code)
            db.session.commit()
        except Exception as e:
            logger.warning(f"清理旧验证码失败: {e}")
            db.session.rollback()

    def cleanup_expired_codes(self, days_old: int = 1) -> int:
        """
        清理过期的验证码
        
        Args:
            days_old: 清理多少天前的记录
            
        Returns:
            int: 清理的记录数
        """
        try:
            count = CaptchaCode.cleanup_expired(days_old)
            db.session.commit()
            logger.info(f"清理了 {count} 个过期图形验证码")
            return count
            
        except Exception as e:
            logger.error(f"清理过期图形验证码失败: {e}")
            db.session.rollback()
            return 0


# 创建全局实例
captcha_service = CaptchaService()
