#!/usr/bin/env python3
"""
检查用户账户
"""
import sqlite3

def check_users():
    try:
        conn = sqlite3.connect('instance/node_sales.db')
        cursor = conn.cursor()
        
        # 查询所有用户
        cursor.execute('SELECT id, username, email, role FROM users')
        users = cursor.fetchall()
        
        print("所有用户:")
        for user in users:
            print(f"  ID: {user[0]}, 用户名: {user[1]}, 邮箱: {user[2]}, 角色: {user[3]}")
        
        # 查询有订阅的用户
        cursor.execute('''
            SELECT DISTINCT u.id, u.username, u.email 
            FROM users u 
            JOIN orders o ON u.id = o.user_id 
            JOIN subscriptions s ON o.id = s.order_id 
            WHERE s.is_active = 1 AND s.expires_at > datetime('now')
        ''')
        active_users = cursor.fetchall()
        
        print("\n有活跃订阅的用户:")
        for user in active_users:
            print(f"  ID: {user[0]}, 用户名: {user[1]}, 邮箱: {user[2]}")
            
        conn.close()
        
    except Exception as e:
        print(f"查询失败: {e}")

if __name__ == "__main__":
    check_users()
