{% extends "base.html" %}

{% block title %}注册 - 节点商城{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-person-plus"></i> 用户注册
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('auth.register') }}">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名 *</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱地址 *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码 *</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="captcha_code" class="form-label">图形验证码 *</label>
                            <div class="row">
                                <div class="col-8">
                                    <input type="text" class="form-control" id="captcha_code" name="captcha_code"
                                           placeholder="请输入图形验证码" maxlength="4" required>
                                </div>
                                <div class="col-4">
                                    <img id="captcha_image" src="" alt="验证码" class="img-fluid border rounded"
                                         style="height: 38px; cursor: pointer;" onclick="refreshCaptcha()">
                                </div>
                            </div>
                            <div class="form-text">点击图片刷新验证码</div>
                        </div>

                        <div class="mb-3">
                            <label for="email_code" class="form-label">邮箱验证码 *</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="email_code" name="email_code"
                                       placeholder="请输入邮箱验证码" maxlength="6" required>
                                <button type="button" class="btn btn-outline-secondary" id="send_email_code"
                                        onclick="sendEmailCode()" disabled>
                                    <span id="send_code_text">获取验证码</span>
                                </button>
                            </div>
                            <div class="form-text">请先输入正确的图形验证码</div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-person-check"></i> 注册
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        已有账户？ 
                        <a href="{{ url_for('auth.login') }}" class="text-decoration-none">立即登录</a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let sessionId = null;
let emailCodeSent = false;
let countdown = 0;

// 页面加载时生成验证码
document.addEventListener('DOMContentLoaded', function() {
    refreshCaptcha();

    // 监听图形验证码输入
    let lastCheckedCode = '';
    document.getElementById('captcha_code').addEventListener('input', function() {
        const captchaCode = this.value.trim();
        const sendButton = document.getElementById('send_email_code');

        if (captchaCode.length === 4 && captchaCode !== lastCheckedCode) {
            // 验证图形验证码（避免重复验证）
            lastCheckedCode = captchaCode;
            verifyCaptcha(captchaCode);
        } else if (captchaCode.length !== 4) {
            sendButton.disabled = true;
            document.getElementById('send_code_text').textContent = '获取验证码';
            lastCheckedCode = '';
        }
    });

    // 表单提交验证
    document.querySelector('form').addEventListener('submit', function(e) {
        const emailCode = document.getElementById('email_code').value.trim();
        if (!emailCode) {
            e.preventDefault();
            alert('请输入邮箱验证码');
            return false;
        }

        if (!emailCodeSent) {
            e.preventDefault();
            alert('请先获取邮箱验证码');
            return false;
        }
    });
});

// 刷新图形验证码
function refreshCaptcha() {
    fetch('/api/verification/captcha/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('captcha_image').src = data.data.image_data;
            sessionId = data.data.session_id;

            // 重置状态
            document.getElementById('captcha_code').value = '';
            document.getElementById('send_email_code').disabled = true;
            document.getElementById('send_code_text').textContent = '获取验证码';
        } else {
            alert('生成验证码失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('生成验证码失败:', error);
        alert('生成验证码失败，请刷新页面重试');
    });
}

// 检查图形验证码（不标记为已使用）
function verifyCaptcha(captchaCode) {
    if (!sessionId) {
        alert('请先刷新验证码');
        return;
    }

    fetch('/api/verification/captcha/check', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            session_id: sessionId,
            captcha_code: captchaCode
        })
    })
    .then(response => response.json())
    .then(data => {
        const sendButton = document.getElementById('send_email_code');
        if (data.success) {
            sendButton.disabled = false;
            document.getElementById('send_code_text').textContent = '获取验证码';
        } else {
            sendButton.disabled = true;
            document.getElementById('send_code_text').textContent = '图形验证码错误';
        }
    })
    .catch(error => {
        console.error('检查图形验证码失败:', error);
        document.getElementById('send_email_code').disabled = true;
    });
}

// 发送邮箱验证码
function sendEmailCode() {
    const email = document.getElementById('email').value.trim();
    const captchaCode = document.getElementById('captcha_code').value.trim();

    if (!email) {
        alert('请先输入邮箱地址');
        return;
    }

    if (!captchaCode || captchaCode.length !== 4) {
        alert('请输入4位图形验证码');
        return;
    }

    if (!sessionId) {
        alert('请先刷新验证码');
        return;
    }

    const sendButton = document.getElementById('send_email_code');
    const sendText = document.getElementById('send_code_text');

    sendButton.disabled = true;
    sendText.textContent = '发送中...';

    fetch('/api/verification/email/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            email: email,
            session_id: sessionId,
            captcha_code: captchaCode,
            code_type: 'register'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            emailCodeSent = true;
            alert('验证码已发送，请查收邮件');
            startCountdown();
        } else {
            alert('发送失败: ' + data.message);
            sendButton.disabled = false;
            sendText.textContent = '获取验证码';
        }
    })
    .catch(error => {
        console.error('发送邮箱验证码失败:', error);
        alert('发送失败，请稍后重试');
        sendButton.disabled = false;
        sendText.textContent = '获取验证码';
    });
}

// 开始倒计时
function startCountdown() {
    countdown = 60;
    const sendButton = document.getElementById('send_email_code');
    const sendText = document.getElementById('send_code_text');

    const timer = setInterval(() => {
        sendText.textContent = `${countdown}秒后重发`;
        countdown--;

        if (countdown < 0) {
            clearInterval(timer);
            sendButton.disabled = false;
            sendText.textContent = '重新获取';
        }
    }, 1000);
}
</script>
{% endblock %}
