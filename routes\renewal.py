"""
续费路由 - 处理用户订阅续费
"""
import logging
from functools import wraps
from flask import Blueprint, request, session, render_template, redirect, url_for, flash, jsonify
from models import db, User, Subscription, Order, OrderStatus
from services.renewal_service import RenewalService

logger = logging.getLogger(__name__)

renewal_bp = Blueprint('renewal', __name__)
renewal_service = RenewalService()

def login_required(f):
    """登录验证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return decorated_function


@renewal_bp.route('/renewal')
@login_required
def renewal_page():
    """用户续费页面"""
    try:
        user_id = session['user_id']
        
        # 获取用户的活跃订阅
        subscription = renewal_service.get_user_active_subscription(user_id)
        if not subscription:
            flash('您当前没有活跃的订阅', 'warning')
            return redirect(url_for('user.dashboard'))
        
        # 获取续费价格配置
        pricing_configs = renewal_service.get_renewal_pricing()
        if not pricing_configs:
            flash('续费功能暂时不可用', 'error')
            return redirect(url_for('user.dashboard'))
        
        # 计算每个续费选项的价格
        renewal_options = []
        for config in pricing_configs:
            try:
                price_info = renewal_service.calculate_renewal_price(
                    subscription, config['duration_months']
                )
                renewal_options.append({
                    'duration_months': config['duration_months'],
                    'discount_percentage': config['discount_percentage'],
                    'is_active': config['is_active'],
                    'price_info': price_info
                })
            except Exception as e:
                logger.error(f"计算续费价格失败: {e}")
                continue
        
        return render_template('user/renewal.html',
                             subscription=subscription,
                             renewal_options=renewal_options)
        
    except Exception as e:
        logger.error(f"加载续费页面失败 user_id={session.get('user_id')}: {e}")
        flash('加载续费页面失败', 'error')
        return redirect(url_for('user.dashboard'))


@renewal_bp.route('/api/renewal/pricing')
@login_required
def get_renewal_pricing():
    """获取续费价格信息API"""
    try:
        user_id = session['user_id']
        
        # 获取用户的活跃订阅
        subscription = renewal_service.get_user_active_subscription(user_id)
        if not subscription:
            return jsonify({
                'success': False,
                'message': '没有找到活跃的订阅'
            }), 404
        
        # 获取续费价格配置
        pricing_configs = renewal_service.get_renewal_pricing()
        
        # 计算每个续费选项的价格
        renewal_options = []
        for config in pricing_configs:
            try:
                price_info = renewal_service.calculate_renewal_price(
                    subscription, config['duration_months']
                )
                renewal_options.append({
                    'duration_months': config['duration_months'],
                    'discount_percentage': config['discount_percentage'],
                    'is_active': config['is_active'],
                    'price_info': price_info
                })
            except Exception as e:
                logger.error(f"计算续费价格失败: {e}")
                continue
        
        return jsonify({
            'success': True,
            'subscription': subscription.to_dict(),
            'renewal_options': renewal_options
        })
        
    except Exception as e:
        logger.error(f"获取续费价格API失败 user_id={session.get('user_id')}: {e}")
        return jsonify({
            'success': False,
            'message': '获取续费价格失败'
        }), 500


@renewal_bp.route('/api/renewal/create-order', methods=['POST'])
@login_required
def create_renewal_order():
    """创建续费订单API"""
    try:
        user_id = session['user_id']
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        duration_months = data.get('duration_months')
        if not duration_months or duration_months not in [3, 6, 12]:
            return jsonify({
                'success': False,
                'message': '无效的续费时长'
            }), 400
        
        # 获取用户的活跃订阅
        subscription = renewal_service.get_user_active_subscription(user_id)
        if not subscription:
            return jsonify({
                'success': False,
                'message': '没有找到活跃的订阅'
            }), 404
        
        # 创建续费订单
        success, order, error_msg = renewal_service.create_renewal_order(
            user_id, subscription.id, duration_months
        )
        
        if not success:
            return jsonify({
                'success': False,
                'message': error_msg or '创建续费订单失败'
            }), 400
        
        logger.info(f"用户 {user_id} 创建续费订单: {order.order_id}")
        
        return jsonify({
            'success': True,
            'message': '续费订单创建成功',
            'order': {
                'order_id': order.order_id,
                'price': order.price,
                'duration_days': order.duration_days,
                'notes': order.notes
            }
        })
        
    except Exception as e:
        logger.error(f"创建续费订单API失败 user_id={session.get('user_id')}: {e}")
        return jsonify({
            'success': False,
            'message': '创建续费订单失败'
        }), 500


@renewal_bp.route('/api/renewal/process-payment', methods=['POST'])
@login_required
def process_renewal_payment():
    """处理续费支付API（简化版）"""
    try:
        user_id = session['user_id']
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'message': '请求数据不能为空'
            }), 400
        
        order_id = data.get('order_id')
        if not order_id:
            return jsonify({
                'success': False,
                'message': '订单ID不能为空'
            }), 400
        
        # 验证订单属于当前用户
        order = Order.query.filter_by(
            order_id=order_id,
            user_id=user_id,
            order_type='renewal'
        ).first()
        
        if not order:
            return jsonify({
                'success': False,
                'message': '订单不存在或不属于当前用户'
            }), 404
        
        if order.status != OrderStatus.PENDING:
            return jsonify({
                'success': False,
                'message': f'订单状态不正确: {order.status.value}'
            }), 400
        
        # 处理支付（这里简化为直接成功，实际应该集成支付网关）
        success, error_msg = renewal_service.process_renewal_payment(
            order_id, 
            payment_method='manual',
            payment_id=f'TEST_{order_id}'
        )
        
        if not success:
            return jsonify({
                'success': False,
                'message': error_msg or '支付处理失败'
            }), 400
        
        logger.info(f"用户 {user_id} 续费支付成功: {order_id}")
        
        return jsonify({
            'success': True,
            'message': '续费成功！您的订阅已延长。',
            'order_id': order_id
        })
        
    except Exception as e:
        logger.error(f"处理续费支付API失败 user_id={session.get('user_id')}: {e}")
        return jsonify({
            'success': False,
            'message': '支付处理失败'
        }), 500


@renewal_bp.route('/renewal/success')
@login_required
def renewal_success():
    """续费成功页面"""
    try:
        order_id = request.args.get('order_id')
        if not order_id:
            flash('订单信息缺失', 'error')
            return redirect(url_for('user.dashboard'))
        
        # 验证订单
        user_id = session['user_id']
        order = Order.query.filter_by(
            order_id=order_id,
            user_id=user_id,
            order_type='renewal',
            status=OrderStatus.COMPLETED
        ).first()
        
        if not order:
            flash('订单不存在或状态异常', 'error')
            return redirect(url_for('user.dashboard'))
        
        return render_template('user/renewal_success.html', order=order)
        
    except Exception as e:
        logger.error(f"加载续费成功页面失败: {e}")
        flash('页面加载失败', 'error')
        return redirect(url_for('user.dashboard'))
