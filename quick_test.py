#!/usr/bin/env python3
"""
快速测试修改是否正确
"""

def test_imports():
    """测试导入"""
    try:
        print("测试导入...")
        from services.traffic_stats_service import TrafficStatsService
        from services.subscription_service import SubscriptionService
        print("✅ 导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_methods():
    """测试方法存在"""
    try:
        print("测试方法...")
        from services.traffic_stats_service import TrafficStatsService
        service = TrafficStatsService()
        
        # 检查新方法
        assert hasattr(service, 'get_subscription_latest_traffic_stats')
        assert hasattr(service, 'get_order_traffic_stats_from_db')
        print("✅ 新方法存在")
        return True
    except Exception as e:
        print(f"❌ 方法测试失败: {e}")
        return False

def main():
    print("开始快速测试...")
    
    tests = [test_imports, test_methods]
    passed = 0
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n测试结果: {passed}/{len(tests)} 通过")
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    print("🎉 测试完成" if success else "❌ 测试失败")
