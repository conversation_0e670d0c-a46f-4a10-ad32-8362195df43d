#!/usr/bin/env python3
"""
修复用户角色枚举值不匹配问题

问题描述：
- 数据库中存储的角色值为小写 ('admin', 'user')
- 枚举定义的值为大写 ('ADMIN', 'USER')
- 导致获取用户列表时出现枚举值错误

修复方案：
1. 备份数据库
2. 更新所有用户的角色值为大写
3. 验证修复结果
"""

import sqlite3
import shutil
import os
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def backup_database(db_path='instance/node_sales.db'):
    """备份数据库"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{db_path}.backup_{timestamp}"
        shutil.copy2(db_path, backup_path)
        logger.info(f"数据库备份成功: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"数据库备份失败: {e}")
        return None

def check_user_roles(db_path='instance/node_sales.db'):
    """检查用户角色数据"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询所有用户角色
        cursor.execute('SELECT id, username, role FROM users ORDER BY id')
        users = cursor.fetchall()
        
        logger.info("当前用户角色数据:")
        role_counts = {'admin': 0, 'user': 0, 'ADMIN': 0, 'USER': 0, 'other': 0}
        
        for user in users:
            user_id, username, role = user
            logger.info(f"  ID: {user_id}, 用户名: {username}, 角色: '{role}'")
            
            if role in role_counts:
                role_counts[role] += 1
            else:
                role_counts['other'] += 1
        
        logger.info(f"角色统计: {role_counts}")
        conn.close()
        return users, role_counts
        
    except Exception as e:
        logger.error(f"检查用户角色失败: {e}")
        return None, None

def fix_user_roles(db_path='instance/node_sales.db'):
    """修复用户角色值"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 更新小写角色值为大写
        logger.info("开始修复用户角色值...")
        
        # 更新 admin -> ADMIN
        cursor.execute("UPDATE users SET role = 'ADMIN' WHERE role = 'admin'")
        admin_updated = cursor.rowcount
        logger.info(f"更新 admin -> ADMIN: {admin_updated} 条记录")
        
        # 更新 user -> USER  
        cursor.execute("UPDATE users SET role = 'USER' WHERE role = 'user'")
        user_updated = cursor.rowcount
        logger.info(f"更新 user -> USER: {user_updated} 条记录")
        
        # 提交更改
        conn.commit()
        logger.info("用户角色修复完成")
        
        conn.close()
        return admin_updated + user_updated
        
    except Exception as e:
        logger.error(f"修复用户角色失败: {e}")
        if conn:
            conn.rollback()
            conn.close()
        return 0

def verify_fix(db_path='instance/node_sales.db'):
    """验证修复结果"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否还有小写角色值
        cursor.execute("SELECT COUNT(*) FROM users WHERE role IN ('admin', 'user')")
        lowercase_count = cursor.fetchone()[0]
        
        # 检查大写角色值数量
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'ADMIN'")
        admin_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'USER'")
        user_count = cursor.fetchone()[0]
        
        # 检查其他角色值
        cursor.execute("SELECT COUNT(*) FROM users WHERE role NOT IN ('ADMIN', 'USER')")
        other_count = cursor.fetchone()[0]
        
        logger.info("修复验证结果:")
        logger.info(f"  小写角色值数量: {lowercase_count}")
        logger.info(f"  ADMIN 角色数量: {admin_count}")
        logger.info(f"  USER 角色数量: {user_count}")
        logger.info(f"  其他角色值数量: {other_count}")
        
        conn.close()
        
        # 验证是否修复成功
        if lowercase_count == 0 and other_count == 0:
            logger.info("✅ 修复验证成功：所有角色值都是有效的枚举值")
            return True
        else:
            logger.warning("⚠️ 修复验证失败：仍有无效的角色值")
            return False
            
    except Exception as e:
        logger.error(f"验证修复结果失败: {e}")
        return False

def main():
    """主函数"""
    db_path = 'instance/node_sales.db'
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return False
    
    logger.info("开始修复用户角色枚举值问题...")
    
    # 1. 检查当前状态
    logger.info("1. 检查当前用户角色数据...")
    users, role_counts = check_user_roles(db_path)
    if users is None:
        return False
    
    # 检查是否需要修复
    if role_counts.get('admin', 0) == 0 and role_counts.get('user', 0) == 0:
        logger.info("✅ 无需修复：所有角色值都已是正确的枚举值")
        return True
    
    # 2. 备份数据库
    logger.info("2. 备份数据库...")
    backup_path = backup_database(db_path)
    if not backup_path:
        logger.error("数据库备份失败，停止修复操作")
        return False
    
    # 3. 修复角色值
    logger.info("3. 修复用户角色值...")
    updated_count = fix_user_roles(db_path)
    if updated_count == 0:
        logger.error("修复失败，没有更新任何记录")
        return False
    
    logger.info(f"成功更新 {updated_count} 条用户记录")
    
    # 4. 验证修复结果
    logger.info("4. 验证修复结果...")
    if verify_fix(db_path):
        logger.info("🎉 用户角色枚举值修复完成！")
        logger.info("现在可以正常访问管理员用户管理页面了")
        return True
    else:
        logger.error("修复验证失败")
        return False

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n修复失败！请检查日志信息。")
        exit(1)
    else:
        print("\n修复成功！")
